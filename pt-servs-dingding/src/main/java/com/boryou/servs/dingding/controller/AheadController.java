package com.boryou.servs.dingding.controller;

import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.dingding.util.AccessTokenUtil;
import org.bouncycastle.util.Strings;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: Young
 */
public class AheadController {
    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 验证Authorization
     */
    private String verifyAuth() {
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isEmptyIfStr(authorization) || StrUtil.isEmptyIfStr(getUserData(authorization))) {
            throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "无权限");
        }
        return authorization;
    }


    /**
    * 获取AccessToken
    * <AUTHOR>
    */
    protected String getAccessToken() throws Exception {
        String auth = verifyAuth();
        Long id;
        Map<String, Object> userData = getUserData(auth);
        String tplName = (String) userData.get("tplName");
        //区分查询组织架构 和 使用模板的审批、推送操作
        if (tplName == null) {
            id = 1L;
        } else {
            id = (Long) getTemplateData(tplName).get("app_id");
        }
        Map<String, Object> map =  getAppData(id);
        return AccessTokenUtil.getToken(map.get("app_key").toString(),map.get("app_sectet").toString());
    }


    /**
     * 获取模板（tpl）
     */
    protected Map<String, Object> getTplInfo() {
        String auth = verifyAuth();
        Map<String, Object> userData = getUserData(auth);
        String tplName = (String) userData.get("tplName");
        if (tplName == null) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        }
        Map<String, Object> map = getTemplateData(tplName);
        return map;
    }


    /**
     * 获取租户标识（tenantFlag）
     */
    protected String getTenantFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getUserData(auth);
        return templateData.get("tenantFlag").toString();
    }


    /**
     * 获取项目标识（projFlag）
     */
    protected String getProjFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getUserData(auth);
        return templateData.get("projFlag").toString();
    }


    /**
     * 获取业务标识（bizFlag）
     */
    protected String getBizFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getUserData(auth);
        return templateData.get("bizFlag").toString();
    }


    /**
     * 获取APP信息
     */
    private static Map<String, Object> getAppData(Long id) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();
        tokenMap1.put("id", 5000000001559713L);
        tokenMap1.put("app_name", "账号审批");
        tokenMap1.put("app_key", "dingk5ghnhdsrqfqmj9p");
        tokenMap1.put("app_sectet", "qPiAC79yrryiQRORlFPBEuBrr0sdI11MnlhRElDdwdSJ9f33v6Y0JiUv0SqTyHRt");
        tokenMap1.put("app_agentid", 1386517777L);
        tokenMap1.put("app_status", 1);
        tokenMap1.put("remark", "");
        map.put("5000000001559713", tokenMap1);

        Map<String, Object> tokenMap2 = new HashMap<>();
        tokenMap1.put("id", 1L);
        tokenMap1.put("app_name", "博约舆情");
        tokenMap1.put("app_key", "dingfiuq1jtzstl2k352");
        tokenMap1.put("app_sectet", "kx1hMCUBLYtBoVA4tiVo8ebyoTn__Ii8xRJriQc7LUObtxeS8s70_ET3PJ7pK9nF");
        tokenMap1.put("app_agentid", 1386023025L);
        tokenMap1.put("app_status", 1);
        tokenMap1.put("remark", "");
        map.put("1", tokenMap2);
        return map.get(id.toString());
    }


    /**
     * 获取模板信息
     */
    private static Map<String, Object> getTemplateData(String name) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();
        tokenMap1.put("id", "xxx");
        tokenMap1.put("tpl_name", "账号申请");
        tokenMap1.put("tpl_content", "");
        tokenMap1.put("tpl_sign", "PROC-350BC003-9B19-463C-9EDD-9BC358182C13");
        tokenMap1.put("app_id", 5000000001559713L);
        tokenMap1.put("tpl_status", 1);
        tokenMap1.put("remark", "");
        map.put("账号申请", tokenMap1);

        Map<String, Object> tokenMap2 = new HashMap<>();
        tokenMap1.put("id", "xxxx");
        tokenMap1.put("tpl_name", "图片审批");
        tokenMap1.put("tpl_content", "");
        tokenMap1.put("tpl_sign", "PROC-54DD5B56-2FB1-4AD2-9146-CADE01CFEB8F");
        tokenMap1.put("app_id", 5000000001559713L);
        tokenMap1.put("tpl_status", 1);
        tokenMap1.put("remark", "");
        map.put("图片审批", tokenMap2);

        Map<String, Object> tokenMap3 = new HashMap<>();
        tokenMap1.put("id", "xxxxx");
        tokenMap1.put("tpl_name", "预警消息");
        tokenMap1.put("tpl_content", "");
        tokenMap1.put("tpl_sign", "");
        tokenMap1.put("app_id", 1L);
        tokenMap1.put("tpl_status", 1);
        tokenMap1.put("remark", "");
        map.put("预警消息", tokenMap3);
        return map.get(name);
    }


    /**
     * 模拟token与用户数据对应关系
     */
    private static Map<String, Object> getUserData(String token) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();
        tokenMap1.put("tplName", "账号申请");   //模板type获取模板id
        tokenMap1.put("projFlag", "博约舆情");      //项目标记
        tokenMap1.put("tenantFlag", "内部用户");    //租户标记
        tokenMap1.put("bizFlag", "审批");   //业务标记
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM1", tokenMap1);


        Map<String, Object> tokenMap2 = new HashMap<>();
        tokenMap2.put("tplName", "图片申请");   //模板type获取模板id
        tokenMap2.put("projFlag", "博约舆情");      //项目标记
        tokenMap2.put("tenantFlag", "内部用户");    //租户标记
        tokenMap2.put("bizFlag", "审批");   //业务标记
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM2", tokenMap2);


        Map<String, Object> tokenMap3 = new HashMap<>();
        tokenMap3.put("tplName", "");   //模板type获取模板id
        tokenMap3.put("projFlag", "博约舆情");      //项目标记
        tokenMap3.put("tenantFlag", "内部用户");    //租户标记
        tokenMap3.put("bizFlag", "普通业务");   //业务标记
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM3", tokenMap3);

        return map.get(token);
    }

}
