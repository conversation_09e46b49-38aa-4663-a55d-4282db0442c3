package com.boryou.servs.dingding.pojo.sb;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 消息模板表
 */
@Data
@TableName("dd_message_template")
public class InfoTemplateSB {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String tpl_name;

    private String tpl_content;

    private String app_id;

    private int tpl_status;

    private String remark;

    public InfoTemplateSB() {
    }

    public InfoTemplateSB(Long id) {
        this.id = id;
    }
}
