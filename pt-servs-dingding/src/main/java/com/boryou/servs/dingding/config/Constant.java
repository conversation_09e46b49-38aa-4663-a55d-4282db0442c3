package com.boryou.servs.dingding.config;

/**
 * 项目中的常量定义类
 */
public class Constant {
    /**
     * 企业corpid, 需要修改成开发者所在企业
     */
    public static final String CORP_ID = "***";
    /**
     * 应用的AppKey，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String APPKEY = "***";
    /**
     * 应用的AppSecret，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String APPSECRET = "***";

    /**
     * 数据加密密钥。用于回调数据的加密，长度固定为43个字符，从a-z, A-Z, 0-9共62个字符中选取,您可以随机生成
     */
    public static final String ENCODING_AES_KEY = "ZD1aURJMLIDRMTtERX2844OFIWkgnIeNWNjksG7OapA";

    /**
     * 加解密需要用到的token，企业可以随机填写。如 "12345"
     */
    public static final String TOKEN = "oEsRxVtGJzXqmppgxPiLqKL83ku67y39uraXjouF9VfqOT";

    /**
     * 应用的agentdId，登录开发者后台可查看
     */
    public static final Long AGENTID = 111L;

    /**
     * 审批模板唯一标识，可以在审批管理后台--OA审批--编辑审批  的URL中找到
     */
    public static final String PROCESS_CODE = "***";

    /**
     * 回调host
     */
    public static final String CALLBACK_URL_HOST = "***";
}
