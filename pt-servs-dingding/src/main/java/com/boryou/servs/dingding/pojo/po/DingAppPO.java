package com.boryou.servs.dingding.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 钉钉应用表
 */
@Data
@TableName("dd_app")
public class DingAppPO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String app_name;

    private String app_key;

    private String app_sectet;

    private String app_agentid;
}
