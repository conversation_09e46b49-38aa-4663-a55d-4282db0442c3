package com.boryou.servs.dingding.controller;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dingding.config.URLConstant;
import com.boryou.servs.dingding.pojo.vo.ProcessTemplateVO;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceCreateRequest;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceCreateResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 企业 E应用审批解决方案示例代码
 * 实现了审批的基础功能
 */
@RestController
@RequestMapping("/dingding/dept")
public class DeptController extends AheadController {


    /**
     * 发起审批
     */
    @RequestMapping(value = "/getUser", method = RequestMethod.POST)
    @ResponseBody
    public Return startProcessInstance(@RequestBody ProcessTemplateVO processInstance)  {
        try {


        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 根据审批实例id获取审批详情
     *
     * @param instanceId
     * @return
     */
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    @ResponseBody
    public Return getProcessinstanceById(@RequestParam String instanceId) {
        try {


        } catch (Exception e) {

        }
        return null;
    }
}


