package com.boryou.servs.dingding.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InfoTemplateVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long tplId;

    private String tpl_name;

    private List<String> tpl_content;

    private String app_id;

    private int tpl_status;

    private String remark;
}
