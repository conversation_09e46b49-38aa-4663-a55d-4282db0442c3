package com.boryou.servs.dingding.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 工作流审批模板表
 */
@Data
@TableName("dd_workflow_template")
public class ProcessTemplatePO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String tpl_name;

    private String tpl_content;

    private String tpl_sign;

    private String app_id;

    private int tpl_status;

    private String remark;
}
