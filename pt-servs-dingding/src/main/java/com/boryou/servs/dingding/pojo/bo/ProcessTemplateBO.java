package com.boryou.servs.dingding.pojo.bo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 工作流审批模板表
 */
@Data
public class ProcessTemplateBO extends BaseBO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String tpl_name;

    private String tpl_content;

    private String tpl_sign;

    private String app_id;

    private int tpl_status;

    private String remark;
}
