package com.boryou.servs.common.annotation.valid;

import com.boryou.servs.common.validator.PickNumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.ElementType.*;

/**
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = PickNumValidator.class)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(PickNum.List.class)
public @interface PickNum {

    String message() default "该值不在候选项中";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int[] value();

    @Documented
    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RetentionPolicy.RUNTIME)
    @interface List {
        PickNum[] value();
    }
}
