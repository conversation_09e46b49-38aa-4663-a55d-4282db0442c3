package com.boryou.servs.common.config;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.ulisesbocchio.jasyptspringboot.EncryptablePropertyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class EncPropConfig {

    @Bean(name = "encryptablePropertyResolver")
    public EncryptablePropertyResolver encryptablePropertyResolver() {
        return new EncryptionPropertyResolver();
    }

    static class EncryptionPropertyResolver implements EncryptablePropertyResolver {

        @Override
        public String resolvePropertyValue(String val) {
            if (StrUtil.isBlank(val)) {
                return val;
            }
            // 以 ENC@@ 开头的值均需解密
            if (val.startsWith("ENC@@")) {
                return parseEncVal(val.substring(5));
            }
            return val;
        }

        private String parseEncVal(String val) {
            String priKeyBase64 = ResourceUtil.readUtf8Str("_prod.pri.key");
            SM2 sm2 = new SM2(priKeyBase64, null);
            return sm2.decryptStr(val, KeyType.PrivateKey);
        }

    }
}
