package com.boryou.servs.common.bean;

import com.diboot.core.vo.Pagination;

/**
 * <AUTHOR>
 */
public class Page extends Pagination {

    public Page() {
        // 不用Diboot（Pagination）自带的排序
        super.clearDefaultOrder();
    }

    public Page(int pageNum, int pageSize) {
        // 不用Diboot（Pagination）自带的排序
        super.clearDefaultOrder();
        super.setPageIndex(pageNum);
        super.setPageSize(pageSize);
    }

    @Override
    public void setPageSize(int pageSize) {
        // TODO: 限制每页最大5000条记录
        // Diboot（Pagination）默认限制为1000
        if (pageSize > 5000) {
            pageSize = 5000;
        }
        super.setPageSize(pageSize);
    }

}
