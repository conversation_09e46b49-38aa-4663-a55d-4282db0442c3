package com.boryou.servs.common.bean;

import cn.hutool.http.HttpStatus;

import java.util.HashMap;

/**
 * 返回对象
 *
 * <AUTHOR>
 */
public class Return extends HashMap<String, Object> {

    private static final long serialVersionUID = 1L;

    private static final String TIP_OK = "操作成功";

    private static final String TIP_ERROR = "操作失败";

    /**
     * 状态码
     */
    private static final String CODE_TAG = "code";

    /**
     * 提示信息
     */
    private static final String MSG_TAG = "msg";

    /**
     * 数据对象
     */
    private static final String DATA_TAG = "data";

    public Return() {
    }

    public Return(int code, String msg) {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    public Return(int code, String msg, Object data) {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        super.put(DATA_TAG, data);
    }

    public static Return ok() {
        return Return.okMsg(TIP_OK);
    }

    public static Return ok(Object data) {
        return Return.ok(TIP_OK, data);
    }

    public static Return okMsg(String msg) {
        return Return.ok(msg, null);
    }

    public static Return ok(String msg, Object data) {
        return new Return(HttpStatus.HTTP_OK, msg, data);
    }

    public static Return error() {
        return Return.error(TIP_ERROR);
    }

    public static Return error(String msg) {
        return Return.error(msg, null);
    }

    public static Return error(String msg, Object data) {
        return new Return(HttpStatus.HTTP_INTERNAL_ERROR, msg, data);
    }

    public static Return error(int code, String msg) {
        return new Return(code, msg, null);
    }
}
