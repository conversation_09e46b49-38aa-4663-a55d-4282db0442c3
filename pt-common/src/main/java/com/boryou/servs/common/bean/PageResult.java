package com.boryou.servs.common.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页的记录列表
     */
    private List<T> records;

    /**
     * 记录的总数
     */
    private long total;

    /**
     * 每页条数
     */
    private long size;

    /**
     * 当前页
     */
    private long current;


    public PageResult(List<T> records, long total) {
        this.records = records;
        this.total = total;
    }
}
