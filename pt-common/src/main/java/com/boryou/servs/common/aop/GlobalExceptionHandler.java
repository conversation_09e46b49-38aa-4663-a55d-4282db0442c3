package com.boryou.servs.common.aop;

import cn.hutool.core.exceptions.StatefulException;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.common.constant.BasicConstant;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * 全局异常捕获处理（控制器增强类）
 *
 * <AUTHOR>
 */
//@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 通用异常
     */
    @ExceptionHandler(Exception.class)
    public Return handleException(Exception exception) {
        // 控制台输出异常信息
        exception.printStackTrace();

        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains(BasicConstant.OS_WIN)) {
            // windows环境下抛出异常信息
            return Return.error("!!! 待捕获 err !!! " + exception.getMessage());
        } else {
            // 非win环境下屏蔽异常信息
            return Return.error("异常--待处理");
        }
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Return handleHttpMessageNotReadableException() {
        return Return.error("~~~ HTTP 参数格式 ERR ~~~");
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Return handleMethodArgumentTypeMismatchException() {
        return Return.error("~~~ METHOD ARG TYPE ERR ~~~");
    }

    /**
     * 自定义异常（有状态码 @Hutool）
     */
    @ExceptionHandler(StatefulException.class)
    public Return handleMyException(StatefulException exception) {
        return Return.error(exception.getStatus(), exception.getMessage());
    }

    /**
     * 类型转换异常
     */
    @ExceptionHandler(ClassCastException.class)
    public Return handleClassCastException() {
        return Return.error("类型转换异常");
    }

    /**
     * 404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public Return handleNoHandlerFoundException() {
        return Return.error("~~~ 404 ~~~");
    }

    /**
     * 空指针异常（NPE）
     */
    @ExceptionHandler(NullPointerException.class)
    public Return handleNullPointerException() {
        return Return.error("~~~ N^P ~~~");
    }

    /**
     * 文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Return handleMaxUploadSizeExceededException() {
        return Return.error("文件上传大小超过限制");
    }

    /**
     * 方法参数验证异常（参数非法）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Return handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        ObjectError err = exception.getBindingResult().getAllErrors().get(0);
        return Return.error(err.getDefaultMessage());
    }

}
