package com.boryou.servs.common.validator;

import com.boryou.servs.common.annotation.valid.PickNum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PickNumValidator implements ConstraintValidator<PickNum, Integer> {

    private List<Integer> pickNumList;

    @Override
    public void initialize(PickNum constraintAnnotation) {
        int[] value = constraintAnnotation.value();
        pickNumList = Arrays.stream(value).boxed().collect(Collectors.toList());
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return pickNumList.contains(value);
    }
}
