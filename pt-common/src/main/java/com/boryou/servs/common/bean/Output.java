package com.boryou.servs.common.bean;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 输出JSON
 *
 * <AUTHOR>
 */
public class Output {

    private static void out(HttpServletResponse response, Return ret) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        try {
            ServletOutputStream os = response.getOutputStream();
            new ObjectMapper().writeValue(os, ret);
        } catch (IOException e) {
//            e.printStackTrace();
        }
    }

    public static void ok(HttpServletResponse response) {
        out(response, Return.ok());
    }

    public static void ok(HttpServletResponse response, Object data) {
        out(response, Return.ok(data));
    }

    public static void okMsg(HttpServletResponse response, String msg) {
        out(response, Return.okMsg(msg));
    }

    public static void ok(HttpServletResponse response, String msg, Object data) {
        out(response, Return.ok(msg, data));
    }

    public static void error(HttpServletResponse response) {
        out(response, Return.error());
    }

    public static void error(HttpServletResponse response, String msg) {
        out(response, Return.error(msg));
    }

    public static void error(HttpServletResponse response, String msg, Object data) {
        out(response, Return.error(msg, data));
    }

    public static void error(HttpServletResponse response, int code, String msg) {
        out(response, Return.error(code, msg));
    }

}
