package com.boryou.servs.common.util;

import cn.hutool.http.HttpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 获取雪花ID
 *
 * <AUTHOR>
 */
@Component
public class IdUtil {

    private static String projectName;

    private static String url = "http://iapi.boryou.com:9244/api/snowflake/get/";

    @Value("${project.name}")
    public void setProjectName(String projectName) {
        IdUtil.projectName = projectName;
        IdUtil.url += projectName;
    }

    public static Long nextLong() {
        // TODO !!
//        Snowflake snowflake = cn.hutool.core.util.IdUtil.getSnowflake(20L);
//        return snowflake.nextId();
        return Long.valueOf(nextString());
    }

    public static String nextString() {
        return HttpUtil.get(url);
    }

}
