package com.boryou.servs.common.validator;

import com.boryou.servs.common.annotation.valid.PickString;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PickStringValidator implements ConstraintValidator<PickString, String> {

    private List<String> pickStringList;

    @Override
    public void initialize(PickString constraintAnnotation) {
        pickStringList = Arrays.asList(constraintAnnotation.value());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return pickStringList.contains(value);
    }
}
