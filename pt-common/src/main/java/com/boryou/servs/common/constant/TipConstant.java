package com.boryou.servs.common.constant;

/**
 * 常用提示常量
 *
 * <AUTHOR>
 */
public class TipConstant {

    public static final String BAD_DEED = "非法操作";

    public static final String RECORD_EXISTED = "记录已存在，不可重复";

    public static final String RECORD_NOT_EXISTED = "记录可能不存在，无法操作";

    public static final String TREE_NODE_MOUNT_ERROR = "树节点挂载异常";

    public static final String CANNOT_REMOVE_FOR_HAS_CHILD = "删除失败（因存在与之关联的下级单元）";

    public static final String CANNOT_REMOVE_FOR_OTHER_DEPENDS = "删除失败（因存在其他数据对其依赖）";

}
