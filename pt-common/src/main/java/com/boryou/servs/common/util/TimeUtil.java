package com.boryou.servs.common.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TimeUtil {

    public static DateTime getStartDateTimeFromYm(String ym) {
        if (!ym.contains("-")) {
            ym = ym.substring(0, 4) + "-" + ym.substring(4, 6);
        }
        return DateUtil.parse(ym + "-01");
    }

    public static DateTime getEndDateTimeFromYm(String ym) {
        if (!ym.contains("-")) {
            ym = ym.substring(0, 4) + "-" + ym.substring(4, 6);
        }
        return DateUtil.endOfMonth(DateUtil.parse(ym + "-01"));
    }

    public static String getStartDateTimeStrFromYm(String ym) {
        DateTime startDateTimeFromYm = getStartDateTimeFromYm(ym);
        return startDateTimeFromYm.toString("yyyy-MM-dd HH:mm:ss");
    }

    public static String getEndDateTimeStrFromYm(String ym) {
        DateTime endDateTimeFromYm = getEndDateTimeFromYm(ym);
        return endDateTimeFromYm.toString("yyyy-MM-dd HH:mm:ss");
    }

    public static String getStartDateStrFromYm(String ym) {
        DateTime startDateTimeFromYm = getStartDateTimeFromYm(ym);
        return startDateTimeFromYm.toString("yyyy-MM-dd");
    }

    public static String getEndDateStrFromYm(String ym) {
        DateTime endDateTimeFromYm = getEndDateTimeFromYm(ym);
        return endDateTimeFromYm.toString("yyyy-MM-dd");
    }

    public static DateTime getStartDateTimeFromDay(String day) {
        if (!day.contains("-")) {
            day = day.substring(0, 4) + "-" + day.substring(4, 6) + "-" + day.substring(6, 8);
        }
        return DateUtil.parse(day + " 00:00:00");
    }

    public static DateTime getEndDateTimeFromDay(String day) {
        if (!day.contains("-")) {
            day = day.substring(0, 4) + "-" + day.substring(4, 6) + "-" + day.substring(6, 8);
        }
        return DateUtil.parse(day + " 23:59:59");
    }

    public static String getStartDateTimeStrFromDay(String day) {
        DateTime startDateTimeFromDay = getStartDateTimeFromDay(day);
        return startDateTimeFromDay.toString("yyyy-MM-dd HH:mm:ss");
    }

    public static String getEndDateTimeStrFromDay(String day) {
        DateTime endDateTimeFromDay = getEndDateTimeFromDay(day);
        return endDateTimeFromDay.toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取某日期之前连续的月份列表
     * num：倒推的月份数
     * date：若为null，则从当前日期开始
     */
    public static List<String> getBeforeYmList(Integer num, Date date) {
        if (date == null) {
            date = DateUtil.date();
        }
        List<String> list = new ArrayList<>();

        for (int i = num - 1; i >= 0; i--) {
            DateTime dateTime = DateUtil.offsetMonth(date, -i);
            String ym = DateUtil.format(dateTime, "yyyyMM");
            list.add(ym);
        }
        return list;
    }

    /**
     * 获取某日期之前指定跨度的月份（ym）
     * num：倒推的月份数
     * date：若为null，则从当前日期开始
     */
    public static String getBeforeYm(Integer num, Date date) {
        if (date == null) {
            date = DateUtil.date();
        }

        DateTime dateTime = DateUtil.offsetMonth(date, -num);
        return DateUtil.format(dateTime, "yyyyMM");
    }

    /**
     * 20210531232400 -> 2021-05-31 23:24:00
     */
    public static String getDateTimeStrFromNumStr(String timeNumStr) {
        return timeNumStr.substring(0, 4) + "-" + timeNumStr.substring(4, 6) + "-" + timeNumStr.substring(6, 8) + " " +
                timeNumStr.substring(8, 10) + ":" + timeNumStr.substring(10, 12) + ":" + timeNumStr.substring(12, 14);
    }

    /**
     * 获取两个Date之间的List<Date>
     */
    public static List<Date> getBetweenDateList(Date startDate, Date endDate) {
        List<Date> dateList = new ArrayList<>();
        Calendar tmpStart = Calendar.getInstance();
        tmpStart.setTime(startDate);
        tmpStart.add(Calendar.DAY_OF_YEAR, 1);
        Calendar tmpEnd = Calendar.getInstance();
        tmpEnd.setTime(endDate);
        while (tmpStart.before(tmpEnd)) {
            dateList.add(tmpStart.getTime());
            tmpStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return dateList;
    }

    /**
     * 获取两个DateStr之间的List<DateStr>
     */
    public static List<String> getBetweenDateStrList(String startTime, String endTime) {
        // 返回的日期集合
        List<String> dateStrList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);
            Calendar tmpStart = Calendar.getInstance();
            tmpStart.setTime(start);
            Calendar tmpEnd = Calendar.getInstance();
            tmpEnd.setTime(end);
            tmpEnd.add(Calendar.DATE, +1); // 日期加1（包含结束）
            while (tmpStart.before(tmpEnd)) {
                dateStrList.add(dateFormat.format(tmpStart.getTime()));
                tmpStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateStrList;
    }

    /**
     * 获取前一个ym（输入：202105，输出202104；输入2021-05，输出2021-04）
     */
    public static String getLastYm(String ym) {
        int hasDash = 0;
        if (!ym.contains("-")) {
            ym = ym.substring(0, 4) + "-" + ym.substring(4, 6);
        } else {
            hasDash = 1;
        }

        DateTime dateTime = DateUtil.offsetMonth(DateUtil.parse(ym + "-01"), -1);

        String lastYm = "";
        if (hasDash == 0) {
            lastYm = DateUtil.format(dateTime, "yyyyMM");
        } else {
            lastYm = DateUtil.format(dateTime, "yyyy-MM");
        }
        return lastYm;
    }

    /**
     * 获取两个时间范围的交集
     */
    public static DateTime[] getInterRange(Date startTime1, Date endTime1, Date startTime2, Date endTime2) {
        long leftVal = Math.max(startTime1.getTime(), startTime2.getTime());
        long rightVal = Math.min(endTime1.getTime(), endTime2.getTime());
        if (leftVal <= rightVal) {
            DateTime startTime = DateUtil.date(leftVal);
            DateTime endTime = DateUtil.date(rightVal);
            return new DateTime[]{startTime, endTime};
        }
        return null;
    }

}
