package com.boryou.servs.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = "com.boryou.servs.*.mapper")
@EnableTransactionManagement
public class MybatisPlusConfig {

    /**
     * 新的分页插件，一缓和二缓遵循mybatis的规则，需设置 MybatisConfiguration#useDeprecatedExecutor = false，
     * 以避免缓存出现问题（该属性会在旧插件移除后一同移除）
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 动态表名配置
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
            // 获取参数方法
            Map<String, Object> paramMap = RequestDataHelper.getRequestData();
            if (paramMap != null) {
                if (paramMap.containsKey("year")) {
                    String year = "_" + paramMap.get("year");
                    // TODO tmp-for:合肥有害
                    if ("sc_prj".equals(tableName) || "pt-servs.sc_prj".equals(tableName)) {
                        return tableName;
                    }
                    return tableName + year;
                }
                if (paramMap.containsKey("ym")) {
                    String ym = "_" + paramMap.get("ym");
                    return tableName + ym;
                }
            }

            return tableName;
        });

        // 3.4.3.2 作废该方式
        // dynamicTableNameInnerInterceptor.setTableNameHandlerMap(map);
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        return interceptor;
    }

}
