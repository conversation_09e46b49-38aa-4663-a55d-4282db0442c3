package com.boryou.servs.common.aop;

import com.boryou.servs.common.bean.Return;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
public class HttpErrorController implements ErrorController {

    private static final String ERROR_PATH = "/error";

    @ResponseBody
    @RequestMapping(path = ERROR_PATH)
    public Object error(HttpServletRequest request, HttpServletResponse response) {
        return Return.error(response.getStatus(), response.getStatus() + "");
    }
}
