spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************
    username: main
    password: bef9hKWUu1]#rDI
    # HikariCP连接池
    hikari:
      # 最小空闲连接数
      minimum-idle: 10
      # 空闲连接最大存活时间（默认 600000，10分钟）
      idle-timeout: 60000
      # 连接池最大连接数（默认 10）
      maximum-pool-size: 300
      # 是否自动提交（默认 true）
      auto-commit: true
      # 连接池名字
      pool-name: my-hikari
      # 连接的最长生命周期（默认 1800000，30分钟）
      max-lifetime: 500000
      # 数据库连接超时时间（默认 30秒）
      connection-timeout: 30000
      connection-test-query: SELECT 1
      #connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

  redis:
    # 地址
    host: ***********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: '{gpZww4KRry#{Rc'
    # 连接超时时间
    timeout: 30s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 50
        # 连接池的最大数据库连接数
        max-active: 500
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 3000ms

# Mybatis Plus
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  # pojo类所在包路径
  type-aliases-package: com.boryou.servs.*.pojo
    #  configuration:
  # 打印SQL等信息
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值（默认 1）
      logic-not-delete-value: 0 # 逻辑未删除值（默认 0）

project:
  # 用于IdUtil
  name: pt-servs
