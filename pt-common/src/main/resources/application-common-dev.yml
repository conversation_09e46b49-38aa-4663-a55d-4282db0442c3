spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: main
    password: pass123X@#88
    # HikariCP连接池
    hikari:
      # 最小空闲连接数
      minimum-idle: 10
      # 空闲连接最大存活时间（默认 600000，10分钟）
      idle-timeout: 180000
      # 连接池最大连接数（默认 10）
      maximum-pool-size: 300
      # 是否自动提交（默认 true）
      auto-commit: true
      # 连接池名字
      pool-name: my-hikari
      # 连接的最长生命周期（默认 1800000，30分钟）
      max-lifetime: 1800000
      # 数据库连接超时时间（默认 30秒）
      connection-timeout: 30000
      connection-test-query: SELECT 1
      #connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

  redis:
    cluster:
      nodes: 192.168.10.240:7000,192.168.10.240:7001,192.168.10.241:7000,192.168.10.241:7001,192.168.10.242:7000,192.168.10.242:7001
      max-redirects: 3
    password:
    timeout: 30000

# Mybatis Plus
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  # pojo类所在包路径
  type-aliases-package: com.boryou.servs.*.pojo
#  configuration:
#    # 打印SQL等信息
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值（默认 1）
      logic-not-delete-value: 0 # 逻辑未删除值（默认 0）

project:
  # 用于IdUtil
  name: pt-servs
