spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************************************************************************************
    username: main
    password: Uu_9zdv&}C(LO0D
    # HikariCP连接池
    hikari:
      # 最小空闲连接数
      minimum-idle: 10
      # 空闲连接最大存活时间（默认 600000，10分钟）
      idle-timeout: 60000
      # 连接池最大连接数（默认 10）
      maximum-pool-size: 300
      # 是否自动提交（默认 true）
      auto-commit: true
      # 连接池名字
      pool-name: my-hikari
      # 连接的最长生命周期（默认 1800000，30分钟）
      max-lifetime: 500000
      # 数据库连接超时时间（默认 30秒）
      connection-timeout: 30000
      connection-test-query: SELECT 1
      #connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

  redis:
    timeout: 6000
    commandTimeout: 6000
    host: redis01
    pool:
      max-idle: 100
      max-wait: -1
#    cluster:
#      nodes:
#        - redis01:7001
#        - redis01:7002
#        - redis02:7003
#        - redis02:7004
#        - redis03:7005
#        - redis03:7006
    #    password: ENC@@BJHNOOY3CIlcscgjry+gR0ifCWIo6sw8WXJvxk1i4AOlMM48vCMogxpttQ9pdmN2RndcSX3I8fj7CmNckA56iDyhkuPirp6Uk6OqAN/iyQnpT1P5DjMRv88i5DBg1g7dLdjR818uDxqj7c0emHKpCNY=
    password: 1[naVn,XHZD$Sv-

    # 废弃的redis(lettuce)配置

#  redis:
#    timeout: 5000ms
#    cluster:
#      max-redirects: 3 # 获取失败 最大重定向次数
#      nodes:
#        - **************:7000
#        - **************:7001
#        - **************:7000
#        - **************:7001
#        - **************:7000
#        - **************:7001
#    lettuce:
#      pool:
#        max-idle: 10 # 连接池中的最大空闲连接
#        max-wait: 600s # 连接池最大阻塞等待时间（用负值表示没有限制）
#        max-active: 1000 # 连接池最大连接数（用负值表示没有限制）
#        min-idle: 5 # 连接池中的最小空闲连接
##    password: ENC@@BJHNOOY3CIlcscgjry+gR0ifCWIo6sw8WXJvxk1i4AOlMM48vCMogxpttQ9pdmN2RndcSX3I8fj7CmNckA56iDyhkuPirp6Uk6OqAN/iyQnpT1P5DjMRv88i5DBg1g7dLdjR818uDxqj7c0emHKpCNY=
#    password:

# Mybatis Plus
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  # pojo类所在包路径
  type-aliases-package: com.boryou.servs.*.pojo
    #  configuration:
  # 打印SQL等信息
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值（默认 1）
      logic-not-delete-value: 0 # 逻辑未删除值（默认 0）

project:
  # 用于IdUtil
  name: pt-servs
