<?xml version="1.0" encoding="UTF-8"?>

<!-- configuration的status用于设置log4j2自身的log级别（可以去除status不设置） -->
<!-- monitorInterval设置配置文件的动态加载间隔秒数 -->
<configuration status="warn" monitorInterval="20">
    <!-- 日志级别：OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->

    <!-- 变量配置 -->
    <Properties>
        <!--
            格式化输出：%date 日期；%thread 线程名；%-5level 日志级别（左对齐，宽度为5）；%msg 信息；%n 换行；%logger{35}（Logger名最大长度35）
        -->
        <property name="LOG_PATTERN" value="%date{HH:mm:ss.SSS} [%thread] %-5level %logger{35} - %msg%n"/>
        <!-- 定义日志存储的路径 -->
        <property name="FILE_PATH" value="logs"/>
        <!-- 项目名称 -->
        <property name="PROJECT_NAME" value="pt-servs"/>
    </Properties>

    <appenders>
        <!-- 输出控制台 -->
        <console name="Console" target="SYSTEM_OUT">
            <!-- 输出日志的格式 -->
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <!-- 控制台只输出level及其以上级别的信息 -->
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>

        <!-- 输出文件（append：是否追加） -->
        <File name="Filelog" fileName="${FILE_PATH}/test.log" append="false">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </File>

        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingFile name="RollingFileInfo" fileName="${FILE_PATH}/info.log" filePattern="${FILE_PATH}/${PROJECT_NAME}-info-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!-- 1 hour 滚动一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 50MB 滚动一次 -->
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- 文件夹下最多20个文件时开始覆盖（默认为7） -->
            <DefaultRolloverStrategy max="20"/>
        </RollingFile>

        <!-- 这个会打印出所有的warn及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingFile name="RollingFileWarn" fileName="${FILE_PATH}/warn.log" filePattern="${FILE_PATH}/${PROJECT_NAME}-warn-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!-- 1 hour 滚动一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 50MB 滚动一次 -->
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- 文件夹下最多20个文件时开始覆盖（默认为7） -->
            <DefaultRolloverStrategy max="20"/>
        </RollingFile>

        <!-- 这个会打印出所有的error及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingFile name="RollingFileError" fileName="${FILE_PATH}/error.log" filePattern="${FILE_PATH}/${PROJECT_NAME}-error-%d{yyyy-MM-dd}_%i.log.gz">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <!-- 1 hour 滚动一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 50MB 滚动一次 -->
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- 文件夹下最多20个文件时开始覆盖（默认为7） -->
            <DefaultRolloverStrategy max="20"/>
        </RollingFile>
    </appenders>

    <!-- 定义logger，只有定义了logger并引入的appender，appender才会生效 -->
    <loggers>

        <!-- 过滤掉spring和mybatis的一些无用的debug信息 -->
        <logger name="org.mybatis" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>

        <!-- 监控系统信息 -->
        <!-- 若additivity设为false，则子Logger只会在自己的appender里输出，而不会在父Logger的appender里输出 -->
        <Logger name="org.springframework" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <root level="info">
            <appender-ref ref="Console"/>
<!--            <appender-ref ref="Filelog"/>-->
<!--            <appender-ref ref="RollingFileInfo"/>-->
<!--            <appender-ref ref="RollingFileWarn"/>-->
<!--            <appender-ref ref="RollingFileError"/>-->
        </root>
    </loggers>
</configuration>
