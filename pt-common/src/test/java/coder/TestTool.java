package coder;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.SM2;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class TestTool {

    /**
     * RSA
     * 生成密钥对
     */
    @Test
    public void testRSA() {
        RSA rsa = new RSA();
        String privateKeyBase64 = rsa.getPrivateKeyBase64();
        String publicKeyBase64 = rsa.getPublicKeyBase64();
        System.out.println(privateKeyBase64);
        System.out.println(publicKeyBase64);

        // 记录下私钥
        String priKeyBase64 = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAPCfUDrdh2sn2j604mVWTcNKhb3PMjvCZZ9oU7QEESi4hujzoZLL+786HoymP5IGwk4UQ9hSd/GZnZmMylmguVJaAdwaweSN/VGGVfNjkVKj4ONscpvUJk18ARXu9YQ7pI7GrHCyFIJdf2ayvG/sPq6Yz2m5jdBd39F1YKkRwWOdAgMBAAECgYAZnNutaGdBwLw6cIaowscEQqp5ydRPCCJJFZHTV5t7+ihiI4zPU2c4s7cMwaWCpbWJGh8ep6iczv03mwTvBWy0YodGq7LM6MXd85Bzq4gkTX4eDr+wLniZ3rTIk2M3/6m0kcXOdZnNgRU9sKYBOhFcqulqOYRtITJkBan8C2TrAQJBAP0TaNoMfGl21QE0vEe0aq49ehQng5Eieg7G6uWpqGqWHuPn7F1Jnw8iGySMvboTHpyZCb8F4J1p3Yii7suHsZsCQQDzZxEWrEiZRHppl44nwn16PyvO+0fjksktRtF9HtEkXrjb7Zv8anr64zYzoPLHzMh9KdssfR+xNlk1CNIme88nAkEAtSR4b2JyhpaM/hD+FyD3HwJX6CckT/Tqgrs0lPBr4ObU83Ikx90TYaSa4v1uK8rVpWcRB86wKFwHOm7NF5dnswJAaf8sVe9Y/kXv+SPi6DWuZxozfcO4HsbBjVIV0KMm+Ou3HS9wio/kVnW0GO+ySohHlxovNESZFJdSaeIZgmDQaQJBAON18q2FmRZQPR68GRX+NSifxGsAezYvfBbmKYAXYg+SuImsiMKK2jk1tLwFTCi6lceJ+GPuyVJs19HGptwFTK0=";
        // 记录下公钥
        String pubKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDwn1A63YdrJ9o+tOJlVk3DSoW9zzI7wmWfaFO0BBEouIbo86GSy/u/Oh6Mpj+SBsJOFEPYUnfxmZ2ZjMpZoLlSWgHcGsHkjf1RhlXzY5FSo+DjbHKb1CZNfAEV7vWEO6SOxqxwshSCXX9msrxv7D6umM9puY3QXd/RdWCpEcFjnQIDAQAB";
    }

    /**
     * RSA签名（私钥加密，公钥解密）
     * 用于让所有公钥所有者验证私钥所有者的身份，并且用来防止私钥所有者发布的内容被篡改，但是不用来保证内容不被他人获得。
     */
    @Test
    public void testRSA1() {
        String raw = "我 是 AaBbCc @_@ 123#";

        // 私钥加密
        String priKeyBase64 = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAPCfUDrdh2sn2j604mVWTcNKhb3PMjvCZZ9oU7QEESi4hujzoZLL+786HoymP5IGwk4UQ9hSd/GZnZmMylmguVJaAdwaweSN/VGGVfNjkVKj4ONscpvUJk18ARXu9YQ7pI7GrHCyFIJdf2ayvG/sPq6Yz2m5jdBd39F1YKkRwWOdAgMBAAECgYAZnNutaGdBwLw6cIaowscEQqp5ydRPCCJJFZHTV5t7+ihiI4zPU2c4s7cMwaWCpbWJGh8ep6iczv03mwTvBWy0YodGq7LM6MXd85Bzq4gkTX4eDr+wLniZ3rTIk2M3/6m0kcXOdZnNgRU9sKYBOhFcqulqOYRtITJkBan8C2TrAQJBAP0TaNoMfGl21QE0vEe0aq49ehQng5Eieg7G6uWpqGqWHuPn7F1Jnw8iGySMvboTHpyZCb8F4J1p3Yii7suHsZsCQQDzZxEWrEiZRHppl44nwn16PyvO+0fjksktRtF9HtEkXrjb7Zv8anr64zYzoPLHzMh9KdssfR+xNlk1CNIme88nAkEAtSR4b2JyhpaM/hD+FyD3HwJX6CckT/Tqgrs0lPBr4ObU83Ikx90TYaSa4v1uK8rVpWcRB86wKFwHOm7NF5dnswJAaf8sVe9Y/kXv+SPi6DWuZxozfcO4HsbBjVIV0KMm+Ou3HS9wio/kVnW0GO+ySohHlxovNESZFJdSaeIZgmDQaQJBAON18q2FmRZQPR68GRX+NSifxGsAezYvfBbmKYAXYg+SuImsiMKK2jk1tLwFTCi6lceJ+GPuyVJs19HGptwFTK0=";
        RSA rsa = new RSA(priKeyBase64, null);
        String encBase64 = rsa.encryptBase64(raw, KeyType.PrivateKey);

        System.out.println("原文：" + raw);
        System.out.println("私钥：" + priKeyBase64);
        System.out.println("密文：" + encBase64);

        // 公钥解密
        String pubKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDwn1A63YdrJ9o+tOJlVk3DSoW9zzI7wmWfaFO0BBEouIbo86GSy/u/Oh6Mpj+SBsJOFEPYUnfxmZ2ZjMpZoLlSWgHcGsHkjf1RhlXzY5FSo+DjbHKb1CZNfAEV7vWEO6SOxqxwshSCXX9msrxv7D6umM9puY3QXd/RdWCpEcFjnQIDAQAB";
        RSA rsaX = new RSA(null, pubKeyBase64);
        String decStr = rsaX.decryptStr(encBase64, KeyType.PublicKey);

        System.out.println("解密后：" + decStr);
    }

    /**
     * RSA加密（公钥加密，私钥解密）
     * 用于向公钥所有者发布信息，这个信息可能被他人篡改，但是无法被他人获得。
     */
    @Test
    public void testRSA2() {
        String raw = "我 是 AaBbCc @_@ 123#";

        // 公钥加密
        String pubKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDwn1A63YdrJ9o+tOJlVk3DSoW9zzI7wmWfaFO0BBEouIbo86GSy/u/Oh6Mpj+SBsJOFEPYUnfxmZ2ZjMpZoLlSWgHcGsHkjf1RhlXzY5FSo+DjbHKb1CZNfAEV7vWEO6SOxqxwshSCXX9msrxv7D6umM9puY3QXd/RdWCpEcFjnQIDAQAB";
        RSA rsa = new RSA(null, pubKeyBase64);
        String encBase64 = rsa.encryptBase64(raw, KeyType.PublicKey);

        System.out.println("原文：" + raw);
        System.out.println("公钥：" + pubKeyBase64);
        System.out.println("密文：" + encBase64);

        // 私钥解密
        String priKeyBase64 = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAPCfUDrdh2sn2j604mVWTcNKhb3PMjvCZZ9oU7QEESi4hujzoZLL+786HoymP5IGwk4UQ9hSd/GZnZmMylmguVJaAdwaweSN/VGGVfNjkVKj4ONscpvUJk18ARXu9YQ7pI7GrHCyFIJdf2ayvG/sPq6Yz2m5jdBd39F1YKkRwWOdAgMBAAECgYAZnNutaGdBwLw6cIaowscEQqp5ydRPCCJJFZHTV5t7+ihiI4zPU2c4s7cMwaWCpbWJGh8ep6iczv03mwTvBWy0YodGq7LM6MXd85Bzq4gkTX4eDr+wLniZ3rTIk2M3/6m0kcXOdZnNgRU9sKYBOhFcqulqOYRtITJkBan8C2TrAQJBAP0TaNoMfGl21QE0vEe0aq49ehQng5Eieg7G6uWpqGqWHuPn7F1Jnw8iGySMvboTHpyZCb8F4J1p3Yii7suHsZsCQQDzZxEWrEiZRHppl44nwn16PyvO+0fjksktRtF9HtEkXrjb7Zv8anr64zYzoPLHzMh9KdssfR+xNlk1CNIme88nAkEAtSR4b2JyhpaM/hD+FyD3HwJX6CckT/Tqgrs0lPBr4ObU83Ikx90TYaSa4v1uK8rVpWcRB86wKFwHOm7NF5dnswJAaf8sVe9Y/kXv+SPi6DWuZxozfcO4HsbBjVIV0KMm+Ou3HS9wio/kVnW0GO+ySohHlxovNESZFJdSaeIZgmDQaQJBAON18q2FmRZQPR68GRX+NSifxGsAezYvfBbmKYAXYg+SuImsiMKK2jk1tLwFTCi6lceJ+GPuyVJs19HGptwFTK0=";
        RSA rsaX = new RSA(priKeyBase64, null);
        String decStr = rsaX.decryptStr(encBase64, KeyType.PrivateKey);

        System.out.println("解密后：" + decStr);
    }

    /**
     * 国密算法（依赖 Bouncy Castle 库）
     * SM2（非对称加密和签名）
     * SM3（摘要签名算法）
     * SM4（对称加密）
     */
    @Test
    public void testSM2() {
        String raw = "我 是 AaBbCc @_@ 123#";

        // 执行一次后注掉（记录密钥对）
//        SM2 sm2 = SmUtil.sm2();
//        String priKeyBase64 = sm2.getPrivateKeyBase64();
//        String pubKeyBase64 = sm2.getPublicKeyBase64();
//        System.out.println(priKeyBase64);
//        System.out.println(pubKeyBase64);

        String priKeyBase64 = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgaOnq8s+qQDVywYIddWvdRZ/dc9TNr/0a1XyH6plI6xigCgYIKoEcz1UBgi2hRANCAARxlH2uP/roNBJhbw3FDyE/6uBHDt2i3xJZC92CmUpELnW4cVfqWbhxhBJ7nE2wzsSgpHo4fljMFc4CmazdfeeV";
        String pubKeyBase64 = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEcZR9rj/66DQSYW8NxQ8hP+rgRw7dot8SWQvdgplKRC51uHFX6lm4cYQSe5xNsM7EoKR6OH5YzBXOApms3X3nlQ==";

        // 公钥加密
        SM2 sm2 = new SM2(null, pubKeyBase64);
        String encBase64 = sm2.encryptBase64(raw, KeyType.PublicKey);

        System.out.println("原文：" + raw);
        System.out.println("公钥：" + pubKeyBase64);
        System.out.println("密文：" + encBase64);

        // 私钥解密
        SM2 sm2X = new SM2(priKeyBase64, null);
        String decStr = sm2X.decryptStr(encBase64, KeyType.PrivateKey);

        System.out.println("解密后：" + decStr);
    }

    @Test
    public void createEncSM2() {

        String raw = "123pwd";

        String priKeyBase64 = ResourceUtil.readUtf8Str("_prod.pri.key");
        String pubKeyBase64 = ResourceUtil.readUtf8Str("_prod.pub.key");

        // 公钥加密
        SM2 sm2 = new SM2(null, pubKeyBase64);
        String encBase64 = sm2.encryptBase64(raw, KeyType.PublicKey);

        System.out.println("原文：" + raw);
        System.out.println("公钥：" + pubKeyBase64);
        System.out.println("密文【再加上前缀 ENC@@】：" + encBase64);

        // 私钥解密
        SM2 sm2X = new SM2(priKeyBase64, null);
        String decStr = sm2X.decryptStr(encBase64, KeyType.PrivateKey);

        System.out.println("解密后：" + decStr);

    }

}
