<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>pt-servs</name>
    <description>公共服务综合</description>
    <packaging>pom</packaging>

    <groupId>com.boryou.servs</groupId>
    <artifactId>pt-servs</artifactId>
    <version>1.0-SNAPSHOT</version>

    <modules>
        <module>pt-common</module>
        <module>pt-servs-screenshot</module>
        <module>pt-file</module>
        <module>pt-core</module>
        <module>pt-servs-dplatform</module>
        <module>pt-servs-oss</module>
        <module>pt-servs-proxyip</module>
        <module>pt-servs-monitor</module>
        <module>pt-servs-sms</module>
        <module>pt-servs-mail</module>
        <module>pt-servs-dingding</module>
        <module>pt-servs-mq</module>
        <module>pt-servs-pushwx</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>

        <app.version>1.0-SNAPSHOT</app.version>

        <java.version>1.8</java.version>
<!--        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>-->
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <spring-boot-maven-plugin.version>2.5.5</spring-boot-maven-plugin.version>
        <elasticsearch.version>8.8.2</elasticsearch.version>
        <jakarta.json-api.version>2.1.1</jakarta.json-api.version>
        <jackson-databind.version>2.14.3</jackson-databind.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot 版本管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>2.5.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <!-- TODO 等log4j2的漏洞问题解决后还原！ -->
            <!-- 移除默认日志（logback） -->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <!-- 等log4j2的漏洞问题解决后还原！ -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-log4j2</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>
