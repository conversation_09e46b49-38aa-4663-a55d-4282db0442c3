package com.boryou.servs.oss.service.impl;

import cn.hutool.core.io.FileUtil;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.boryou.servs.oss.service.MinioService;
import com.pig4cloud.plugin.oss.OssProperties;
import com.pig4cloud.plugin.oss.service.OssTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
public class MinioServiceImpl implements MinioService {

    @Value("${oss.bucket-name}")
    private String bucketName;

    @Autowired
    private OssProperties ossProperties;
    @Autowired
    private OssTemplate ossTemplate;


    /**
     * 上传文件
     *
     * @param file 上传文件
     * @return 成功则返回文件名，失败返回空
     */
    @Override
    public boolean uploadFile(MultipartFile file, String fileName, String projectName) throws IOException {
        ossTemplate.putObject(ossProperties.getBucketName(), projectName + "/" + fileName, file.getContentType(), file.getInputStream());
        return true;

    }

    @Override
    public boolean uploadFile(File file, String fileName, String projectName) throws IOException {
        ossTemplate.putObject(ossProperties.getBucketName(), projectName + "/" + fileName, FileUtil.getType(file), FileUtil.getInputStream(file));
        return true;
    }


    /**
     * 删除文件
     *
     * @param fileName 文件路径
     * @return
     */
    @Override
    public boolean deleteFile(String fileName, String projectName) {
        try {
            ossTemplate.removeObject(bucketName, projectName + "/" + fileName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据前缀与后缀删除文件*
     *
     * @param fileName
     * @param fileType
     * @return
     */
    @Override
    public boolean deleteFiles(String fileName, String fileType, String projectName) {
        if (StringUtils.isEmpty(fileName) || StringUtils.isEmpty(fileType)) {
            return false;
        }
        List<S3ObjectSummary> allObjectsByPrefix = ossTemplate.getAllObjectsByPrefix(bucketName, projectName + "/" + fileName);
        for (S3ObjectSummary o : allObjectsByPrefix) {
            String key = o.getKey();
            if (key.contains(fileName) && key.contains(fileType)) {
                deleteFile(key, projectName);
            }
        }
        return true;
    }

    /**
     * 得到指定文件的InputStream
     *
     * @param originalName 文件路径
     * @return
     */
    @Override
    public InputStream getObject(String originalName, String projectName) {
        try {
            return ossTemplate.getObject(bucketName, projectName + "/" + originalName).getObjectContent();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据文件路径得到预览文件绝对地址
     *
     * @param fileName 文件路径
     * @return
     */
    @Override
    public String getPreviewFileUrl(String fileName, String projectName) {
        try {
            return ossTemplate.getObjectURL(bucketName, projectName + "/" + fileName);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    @Override
    public String getObjectUrl(String fileName, String projectName) {
        return ossTemplate.getObjectURL(bucketName, projectName + "/" + fileName);
    }


}
