package com.boryou.servs.oss.pojo.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String bucketName;

    private String fileId;

    private String fileUrl;

    private String fileType;

    private Long fileSize;
}
