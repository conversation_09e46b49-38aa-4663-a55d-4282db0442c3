package com.boryou.servs.oss.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.common.constant.BasicConstant;
import com.boryou.servs.oss.service.MinioService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oss")
public class OssController {

    @Resource
    private MinioService minioService;


    /**
     * 上传文件*
     * @param file
     * @param fileName
     * @param projectName
     * @return
     */
    @PostMapping("/uploadMultipartFile")
    public boolean upload(MultipartFile file, String fileName, String projectName) {
        boolean b = false;
        try {
            b = minioService.uploadFile(file, fileName, projectName);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return b;
    }


    /**
     * 上传file文件*
     * @param file
     * @param fileName
     * @param projectName
     * @return
     */
    @PostMapping("/uploadFile")
    public Return upload(File file, String fileName, String projectName) {
        Return r = Return.ok("上传成功");
        try {
            minioService.uploadFile(file, fileName, projectName);
        } catch (IOException e) {
            e.printStackTrace();
            r = Return.error("上传失败");
        }
        return r;
    }

    /**
     * 下载文件*
     * @param fileName
     * @param projectName
     * @return
     */
    @GetMapping("/download")
    public InputStream download(String fileName, String projectName) {
        return minioService.getObject(fileName, projectName);
    }

    /**
     * 删除文件*
     * @param fileName
     * @param projectName
     * @return
     */
    @DeleteMapping("/remove")
    public boolean remove(String fileName, String projectName) {
        return minioService.deleteFile(fileName, projectName);
    }


    @ResponseBody
    @GetMapping("/getFileUrl")
    public String getFileUrl(String fileName, String projectName) {
        return minioService.getObjectUrl(fileName, projectName);
    }


    /**
     * @Date 20250207
     * 根据地域码获取地图数据接口
     */
    @ResponseBody
    @GetMapping("/geo/{areaId}")
    public JsonNode getGeoJson(@PathVariable(name = "areaId") String areaId) throws IOException {
        InputStream geoJson = minioService.getObject(areaId + ".geoJson", BasicConstant.PROJ_NAME);
        ObjectMapper objectMapper = new ObjectMapper();
        // 将输入流转换为 JsonNode 对象
        return objectMapper.readTree(geoJson);
    }

}
