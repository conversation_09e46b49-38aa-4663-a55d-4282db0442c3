package com.boryou.servs.oss.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * * minio存储服务
 * <AUTHOR>
 */
public interface MinioService {

    /**
     *上传文件
     * @param file
     * @param fileName
     * @return
     * @throws IOException
     */
    boolean uploadFile(MultipartFile file, String fileName, String projectName) throws IOException;

    boolean uploadFile(File file, String fileName, String projectName) throws IOException;

    /**
     * 删除文件*
     * @param fileName
     * @param projectName
     * @return
     */
    boolean deleteFile(String fileName ,String projectName);

    /**
     * 根据前缀后缀删除文件*
     * @param fileName
     * @param fileType
     * @param projectName
     * @return
     * @throws Exception
     */
    boolean deleteFiles(String fileName, String fileType,String projectName);

    /**
     * 获取文件*
     * @param originalName
     * @return
     */
    InputStream getObject(String originalName,String projectName);

    /**
     * 获取预览链接
     * @param fileName
     * @param projectName
     * @return
     */
    String getPreviewFileUrl(String fileName , String projectName);


    /**
     * *
     */
    String getObjectUrl(String fileName , String projectName);



}
