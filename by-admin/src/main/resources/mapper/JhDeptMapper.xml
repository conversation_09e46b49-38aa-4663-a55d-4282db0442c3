<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.webservice.mapper.JhDeptMapper">

    <resultMap id="BaseResultMap" type="com.boryou.web.module.webservice.domain.JhDept">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orgCode" column="org_code" jdbcType="BIGINT"/>
            <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
            <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
            <result property="deptAlias" column="dept_alias" jdbcType="VARCHAR"/>
            <result property="deptFatherCode" column="dept_father_code" jdbcType="VARCHAR"/>
            <result property="deptPhone" column="dept_phone" jdbcType="VARCHAR"/>
            <result property="deptHeadName" column="dept_head_name" jdbcType="VARCHAR"/>
            <result property="deptAddr" column="dept_addr" jdbcType="VARCHAR"/>
            <result property="deptRemark" column="dept_remark" jdbcType="VARCHAR"/>
            <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,org_code,dept_code,
        dept_name,dept_alias,dept_father_code,
        dept_phone,dept_head_name,dept_addr,
        dept_remark,modified_time,create_time
    </sql>
</mapper>
