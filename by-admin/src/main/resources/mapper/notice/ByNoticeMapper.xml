<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.notice.mapper.ByNoticeMapper">

    <resultMap type="ByNotice" id="ByNoticeResult">
        <result property="noticeId" column="notice_id"/>
        <result property="docIndexId" column="doc_index_id"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeTime" column="notice_time"/>
        <result property="time" column="time"/>
    </resultMap>

    <resultMap id="ByNoticeByNoticeRelaResult" type="ByNotice" extends="ByNoticeResult">
        <collection property="byNoticeRelaList" notNullColumn="id"
                    javaType="java.util.List" resultMap="ByNoticeRelaResult"/>
    </resultMap>

    <resultMap type="ByNoticeRela" id="ByNoticeRelaResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectByNoticeVo">
        select notice_id, doc_index_id, notice_type, notice_time,time from by_notice t
    </sql>

    <select id="selectByNoticeList" parameterType="ByNotice" resultMap="ByNoticeResult">
        <include refid="selectByNoticeVo"/>
        <where>
            <if test="docIndexId != null ">
                and doc_index_id = #{docIndexId}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and notice_type = #{noticeType}
            </if>
            <if test="noticeTime != null ">
                and notice_time = #{noticeTime}
            </if>
            <if test="userId != null">
                and exists (select 1 from by_notice_rela c where c.notice_id=t.notice_id and c.user_id=#{userId} )
            </if>
            order by notice_time desc
        </where>
    </select>


    <select id="selectNoticeList" parameterType="ByNotice" resultMap="ByNoticeResult">
        SELECT DISTINCT n.notice_id, n.doc_index_id, n.notice_type, n.notice_time, n.time
        FROM by_notice n
        JOIN by_notice_rela c ON n.notice_id = c.notice_id AND c.user_id = #{userId}
        ORDER BY n.notice_time DESC
    </select>

    <select id="selectNoticeListCount" resultType="java.lang.Integer">
        select count(distinct n.notice_id)
        from by_notice n
        inner join by_notice_rela c on n.notice_id = c.notice_id and c.user_id = #{userId}
    </select>

    <select id="selectByNoticeById" parameterType="Long"
            resultMap="ByNoticeByNoticeRelaResult">
        select a.notice_id, a.doc_index_id, a.notice_type, a.notice_time,
        b.id , b.notice_id , b.user_id
        from by_notice a
        left join by_notice_rela b on b.notice_id = a.notice_id
        where a.notice_id = #{noticeId}
    </select>

    <insert id="insertByNotice" parameterType="ByNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into by_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,
            </if>
            <if test="docIndexId != null">doc_index_id,
            </if>
            <if test="noticeType != null">notice_type,
            </if>
            <if test="noticeTime != null">notice_time,
            </if>
            <if test="time != null">time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},
            </if>
            <if test="docIndexId != null">#{docIndexId},
            </if>
            <if test="noticeType != null">#{noticeType},
            </if>
            <if test="noticeTime != null">#{noticeTime},
            </if>
            <if test="time != null">#{time}
            </if>
        </trim>
    </insert>

    <update id="updateByNotice" parameterType="ByNotice">
        update by_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="docIndexId != null">doc_index_id =
                #{docIndexId},
            </if>
            <if test="noticeType != null">notice_type =
                #{noticeType},
            </if>
            <if test="noticeTime != null">notice_time =
                #{noticeTime},
            </if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteByNoticeById" parameterType="Long">
        delete from by_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteByNoticeByIds" parameterType="String">
        delete from by_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <delete id="deleteByNoticeRelaByNoticeIds" parameterType="String">
        delete from by_notice_rela where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <delete id="deleteByNoticeRelaByNoticeId" parameterType="Long">
        delete from by_notice_rela where notice_id = #{noticeId}
    </delete>

    <insert id="batchByNoticeRela">
        insert into by_notice_rela
        ( id , notice_id , user_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id
                }, #{item.noticeId
                }, #{item.userId
                })
        </foreach>
    </insert>
</mapper>
