<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.report.mapper.ReportMapper">

    <resultMap id="ReportMap" type="com.boryou.web.module.report.entity.vo.ReportVO">
        <id property="reportId" column="report_id" jdbcType="BIGINT"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="head" column="head" jdbcType="VARCHAR"/>
        <result property="issue" column="issue" jdbcType="VARCHAR"/>
        <result property="tempId" column="temp_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="reportIntro" column="report_intro" jdbcType="VARCHAR"/>
        <result property="suggest" column="suggest" jdbcType="VARCHAR"/>
        <result property="overview" column="overview" jdbcType="VARCHAR"/>
        <result property="mediaStatistics" column="media_statistics" jdbcType="VARCHAR"/>
        <result property="emotionAnalysis" column="emotion_analysis" jdbcType="VARCHAR"/>
        <result property="mediaDetails" column="media_details" jdbcType="VARCHAR"/>
        <result property="charCloud" column="char_cloud" jdbcType="VARCHAR"/>
        <result property="mainInfo" column="main_info" jdbcType="VARCHAR"/>
        <result property="infoIntro" column="info_intro" jdbcType="VARCHAR"/>
        <result property="mediaTrendChart" column="media_trend_chart" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="BIGINT"/>
        <result property="materialId" column="material_id" jdbcType="BIGINT"/>
        <result property="inputComponents" column="input_components" jdbcType="VARCHAR"/>
        <result property="reportType" column="report_type" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TemplateMap" type="com.boryou.web.module.report.entity.ReportTemplate">
        <id property="tempId" column="temp_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="reportIntro" column="report_intro" jdbcType="INTEGER"/>
        <result property="suggest" column="suggest" jdbcType="INTEGER"/>
        <result property="overview" column="overview" jdbcType="INTEGER"/>
        <result property="mediaStatistics" column="media_statistics" jdbcType="INTEGER"/>
        <result property="emotionAnalysis" column="emotion_analysis" jdbcType="INTEGER"/>
        <result property="mediaDetails" column="media_details" jdbcType="INTEGER"/>
        <result property="charCloud" column="char_cloud" jdbcType="INTEGER"/>
        <result property="mainInfo" column="main_info" jdbcType="INTEGER"/>
        <result property="infoIntro" column="info_intro" jdbcType="INTEGER"/>
        <result property="mediaTrendChart" column="media_trend_chart" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectReportSql">
        select report_id,
        title,
        head,
        issue,
        temp_id,
        user_id,
        report_intro,
        suggest,
        overview,
        media_statistics,
        emotion_analysis,
        media_details,
        char_cloud,
        main_info,
        info_intro,
        media_trend_chart,
        create_time,
        create_by,
        file_id,
        material_id,
        input_components,
        report_type
        from by_report
    </sql>
    <sql id="selectReportTemplateSql">
        select temp_id,
        name,
        user_id,
        report_intro,
        suggest,
        overview,
        media_statistics,
        emotion_analysis,
        media_details,
        char_cloud,
        main_info,
        info_intro,
        media_trend_chart,
        create_time,
        create_by
        from by_report_template
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultMap="ReportMap">
        <include refid="selectReportSql"/>
        where report_id = #{reportId,jdbcType=BIGINT}
    </select>

    <select id="selectReport" resultType="com.boryou.web.module.report.entity.vo.ReportVO" resultMap="ReportMap">
        select r.report_id,
        r.title,
        r.head,
        r.issue,
        r.temp_id,
        r.user_id,
        r.create_time,
        r.create_by,
        r.file_id,
        r.material_id,
        input_components,
        r.report_type,
        CASE WHEN r.temp_id IS NOT NULL THEN 1 ELSE 2 END AS isFile,
        t.name tempName
        from by_report r
        left join by_report_template t on r.temp_id = t.temp_id
        left join sys_user u on r.create_by = u.user_name
        <where>
            <if test="deptId != null">
                AND u.dept_id = #{deptId,jdbcType=BIGINT}
            </if>
            <if test="reportType != null and reportType != ''">
                AND r.report_type = #{reportType,jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != ''">
                AND r.title like concat('%', #{title,jdbcType=VARCHAR}, '%')
            </if>
            <if test="tempId != null">
                AND r.temp_id &gt;= #{tempId,jdbcType=BIGINT}
            </if>
            <if test="startTime != null">
                AND r.create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND r.create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isFile != null and isFile == 1">AND r.temp_id is not null</if>
            <if test="isFile != null and isFile == 2">AND r.temp_id is null</if>
        </where>
        order by r.create_time desc
    </select>
    <select id="selectByIds" resultType="com.boryou.web.module.report.entity.Report" resultMap="ReportMap">
        <include refid="selectReportSql"/>
        where find_in_set(report_id, #{reportIds})
    </select>

    <delete id="deleteByIds" parameterType="java.lang.String">
        delete from by_report
        where find_in_set(report_id, #{ids})
    </delete>

    <select id="selectIssue" resultType="com.boryou.web.module.report.entity.Report" resultMap="ReportMap">
        <include refid="selectReportSql"/>
        where title = #{title,jdbcType=VARCHAR} AND temp_id = #{tempId,jdbcType=BIGINT} order by issue desc limit 1
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.boryou.web.module.report.entity.Report"
            useGeneratedKeys="true">
        insert into by_report
        (report_id,title,head,issue
        ,temp_id,user_id,report_intro,suggest
        ,overview,media_statistics,emotion_analysis,media_details
        ,char_cloud,main_info,info_intro,media_trend_chart
        ,create_time,create_by,file_id,input_components,material_id,report_type)
        values (#{reportId,jdbcType=BIGINT},#{title,jdbcType=VARCHAR},#{head,jdbcType=VARCHAR},#{issue,jdbcType=VARCHAR}
        ,#{tempId,jdbcType=BIGINT},#{userId,jdbcType=BIGINT},#{reportIntro,jdbcType=VARCHAR},#{suggest,jdbcType=VARCHAR}
        ,#{overview,jdbcType=VARCHAR},#{mediaStatistics,jdbcType=VARCHAR},#{emotionAnalysis,jdbcType=VARCHAR},#{mediaDetails,jdbcType=VARCHAR}
        ,#{charCloud,jdbcType=VARCHAR},#{mainInfo,jdbcType=VARCHAR},#{infoIntro,jdbcType=VARCHAR},#{mediaTrendChart,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR},#{fileId,jdbcType=BIGINT},#{inputComponents,jdbcType=VARCHAR}
        ,#{materialId,jdbcType=BIGINT},#{reportType,jdbcType=VARCHAR})
    </insert>

    <update id="updateById" parameterType="com.boryou.web.module.report.entity.Report">
        update by_report
        <set>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="head != null">
                head = #{head,jdbcType=VARCHAR},
            </if>
            <if test="issue != null">
                issue = #{issue,jdbcType=VARCHAR},
            </if>
            <if test="tempId != null">
                temp_id = #{tempId,jdbcType=BIGINT},
            </if>
            <if test="reportIntro != null">
                report_intro = #{reportIntro,jdbcType=VARCHAR},
            </if>
            <if test="suggest != null">
                suggest = #{suggest,jdbcType=VARCHAR},
            </if>
            <if test="overview != null">
                overview = #{overview,jdbcType=VARCHAR},
            </if>
            <if test="mediaStatistics != null">
                media_statistics = #{mediaStatistics,jdbcType=VARCHAR},
            </if>
            <if test="emotionAnalysis != null">
                emotion_analysis = #{emotionAnalysis,jdbcType=VARCHAR},
            </if>
            <if test="mediaDetails != null">
                media_details = #{mediaDetails,jdbcType=VARCHAR},
            </if>
            <if test="charCloud != null">
                char_cloud = #{charCloud,jdbcType=VARCHAR},
            </if>
            <if test="mainInfo != null">
                main_info = #{mainInfo,jdbcType=VARCHAR},
            </if>
            <if test="infoIntro != null">
                info_intro = #{infoIntro,jdbcType=VARCHAR},
            </if>
            <if test="mediaTrendChart != null">
                media_trend_chart = #{mediaTrendChart,jdbcType=VARCHAR},
            </if>
            <if test="inputComponents != null">
                input_components = #{inputComponents,jdbcType=VARCHAR},
            </if>
            <if test="fileId != null">
                file_id = #{fileId,jdbcType=BIGINT},
            </if>
            <if test="materialId != null">
                material_id = #{materialId,jdbcType=BIGINT},
            </if>
            <if test="reportType != null">
                report_type = #{reportType,jdbcType=BIGINT},
            </if>
        </set>
        where report_id = #{reportId,jdbcType=BIGINT}
    </update>

    <update id="clearFileById" parameterType="com.boryou.web.module.report.entity.Report">
        update by_report set file_id = null
        where report_id = #{reportId,jdbcType=BIGINT}
    </update>

    <select id="selectTemplateById" parameterType="java.lang.Long" resultMap="TemplateMap">
        <include refid="selectReportTemplateSql"/>
        where temp_id = #{tempId,jdbcType=BIGINT}
    </select>

    <select id="selectTemplate" resultType="com.boryou.web.module.report.entity.ReportTemplate" resultMap="TemplateMap">
        <include refid="selectReportTemplateSql"/>
        <where>
            <if test="userId != null">
                AND user_id = #{userId,jdbcType=BIGINT}
            </if>
            <if test="userId == null">
                AND user_id is null
            </if>
        </where>
    </select>

    <delete id="deleteReportByTempIds" parameterType="java.lang.String">
        delete from by_report
        where find_in_set(temp_id, #{ids})
    </delete>

    <delete id="deleteTemplateByIds" parameterType="java.lang.String">
        delete from by_report_template
        where find_in_set(temp_id, #{ids})
    </delete>

    <insert id="insertTemplate" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.report.entity.ReportTemplate" useGeneratedKeys="true">
        insert into by_report_template
        (temp_id,name,user_id,report_intro
        ,suggest,overview,media_statistics
        ,emotion_analysis,media_details,char_cloud
        ,main_info,info_intro,media_trend_chart
        ,create_time,create_by)
        values
        (#{tempId,jdbcType=BIGINT},#{name,jdbcType=VARCHAR},#{userId,jdbcType=BIGINT},#{reportIntro,jdbcType=INTEGER}
        ,#{suggest,jdbcType=INTEGER},#{overview,jdbcType=INTEGER},#{mediaStatistics,jdbcType=INTEGER}
        ,#{emotionAnalysis,jdbcType=INTEGER},#{mediaDetails,jdbcType=INTEGER},#{charCloud,jdbcType=INTEGER}
        ,#{mainInfo,jdbcType=INTEGER},#{infoIntro,jdbcType=INTEGER},#{mediaTrendChart,jdbcType=INTEGER}
        ,#{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR})
    </insert>

    <update id="updateTemplateById" parameterType="com.boryou.web.module.report.entity.ReportTemplate">
        update by_report_template
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="reportIntro != null">
                report_intro = #{reportIntro,jdbcType=INTEGER},
            </if>
            <if test="suggest != null">
                suggest = #{suggest,jdbcType=INTEGER},
            </if>
            <if test="overview != null">
                overview = #{overview,jdbcType=INTEGER},
            </if>
            <if test="mediaStatistics != null">
                media_statistics = #{mediaStatistics,jdbcType=INTEGER},
            </if>
            <if test="emotionAnalysis != null">
                emotion_analysis = #{emotionAnalysis,jdbcType=INTEGER},
            </if>
            <if test="mediaDetails != null">
                media_details = #{mediaDetails,jdbcType=INTEGER},
            </if>
            <if test="charCloud != null">
                char_cloud = #{charCloud,jdbcType=INTEGER},
            </if>
            <if test="mainInfo != null">
                main_info = #{mainInfo,jdbcType=INTEGER},
            </if>
            <if test="infoIntro != null">
                info_intro = #{infoIntro,jdbcType=INTEGER},
            </if>
            <if test="mediaTrendChart != null">
                media_trend_chart = #{mediaTrendChart,jdbcType=INTEGER},
            </if>
        </set>
        where temp_id = #{tempId,jdbcType=BIGINT}
    </update>
    <update id="changeTemplateToSys">
        update by_report_template set user_id = NULL
        where temp_id = #{tempId,jdbcType=BIGINT}
    </update>

    <select id="selectDefault" resultType="Long" parameterType="Long">
        select temp_id from by_report_template_default where user_id = #{userId,jdbcType=BIGINT}
    </select>

    <insert id="insertDefault" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.report.entity.ReportTemplate" useGeneratedKeys="true">
        insert into by_report_template_default
        (temp_id,user_id)
        values (#{tempId,jdbcType=BIGINT},#{userId,jdbcType=BIGINT})
    </insert>
    <update id="updateDefault" parameterType="com.boryou.web.module.report.entity.ReportTemplate">
        update by_report_template_default
        set temp_id = #{tempId,jdbcType=BIGINT}
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

</mapper>
