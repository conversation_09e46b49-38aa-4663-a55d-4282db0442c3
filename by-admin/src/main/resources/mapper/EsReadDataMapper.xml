<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.search.mapper.EsReadDataMapper">
    <resultMap id="esReadDataMap" type="com.boryou.web.module.search.entity.EsReadData">
        <id property="id" column="id"/>
        <result property="indexId" column="index_id"/>
        <result property="md5" column="md5"/>
        <result property="createBy" column="create_by"/>
    </resultMap>
    <insert id="insertList" parameterType="com.boryou.web.module.search.entity.EsReadData">
        insert into by_read_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            index_id,
            create_by,
        </trim>
        <foreach collection="esReadDatas" item="item" open="values (" close=")" separator="),(" index="i">
            #{item.id},
            #{item.indexId},
            #{item.createBy}
        </foreach>
    </insert>
</mapper>
