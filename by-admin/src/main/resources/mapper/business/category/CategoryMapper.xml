<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.admin.module.business.category.dao.CategoryDao">

    <!-- 根据父级id 查询子类 -->
    <select id="queryByParentId"
            resultType="com.boryou.web.admin.module.business.category.domain.entity.CategoryEntity">
        SELECT * FROM t_category
        WHERE
        parent_id IN
        <foreach collection="parentIdList" open="(" separator="," close=")" item="id">#{id}</foreach>
        AND deleted_flag = #{deletedFlag}
        ORDER BY sort ASC
    </select>


    <!-- 根据父级id 查询子类 -->
    <select id="queryByParentIdAndType"
            resultType="com.boryou.web.admin.module.business.category.domain.entity.CategoryEntity">
        SELECT * FROM t_category
        WHERE
        parent_id IN
        <foreach collection="parentIdList" open="(" separator="," close=")" item="id">#{id}</foreach>
        AND category_type = #{categoryType}
        AND deleted_flag = #{deletedFlag}
        ORDER BY sort ASC
    </select>

    <select id="queryByType"
            resultType="com.boryou.web.admin.module.business.category.domain.entity.CategoryEntity">
        SELECT * FROM t_category
        WHERE category_type = #{categoryType}
        AND deleted_flag = #{deletedFlag}
        ORDER BY sort ASC
    </select>

    <!-- 查看类目 -->
    <select id="selectOne" resultType="com.boryou.web.admin.module.business.category.domain.entity.CategoryEntity">
        SELECT * FROM t_category
        <where>
            <if test="categoryType != null">
                AND category_type = #{categoryType}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="categoryName != null">
                AND category_name = #{categoryName}
            </if>
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="selectByTypeAndId"
            resultType="com.boryou.web.admin.module.business.category.domain.entity.CategoryEntity">
        select * from  t_category where category_type = #{categoryType} and category_id = #{categoryId}
    </select>
</mapper>
