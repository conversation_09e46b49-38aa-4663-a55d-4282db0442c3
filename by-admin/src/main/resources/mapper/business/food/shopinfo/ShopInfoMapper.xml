<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.admin.module.business.food.shopinfo.dao.ShopInfoDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        b_shop_info.id,
        b_shop_info.shop_id,
        b_shop_info.shop_name,
        b_shop_info.shop_platform,
        b_shop_info.enterprise_name,
        b_shop_info.enterprise_type,
        b_shop_info.legal_representative,
        b_shop_info.business_scope,
        b_shop_info.registered_address,
        b_shop_info.food_license,
        b_shop_info.deleted_flag,
        b_shop_info.create_user_id,
        b_shop_info.create_time,
        b_shop_info.update_user_id,
        b_shop_info.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="com.boryou.web.admin.module.business.food.shopinfo.domain.vo.ShopInfoVO">
        SELECT
        <include refid="base_columns"/>
        FROM b_shop_info
        <where>
            <!--店铺名称查询-->
            <if test="queryForm.shopName != null and queryForm.shopName != ''">
                AND INSTR(b_shop_info.shop_name,#{queryForm.shopName})
            </if>
            <!--企业名称查询-->
            <if test="queryForm.enterpriseName != null and queryForm.enterpriseName != ''">
                AND INSTR(b_shop_info.enterprise_name,#{queryForm.enterpriseName})
            </if>
            <!--经营范围查询-->
            <if test="queryForm.businessScope != null and queryForm.businessScope != ''">
                AND INSTR(b_shop_info.business_scope,#{queryForm.businessScope})
            </if>
            <!--注册地址查询-->
            <if test="queryForm.registeredAddress != null and queryForm.registeredAddress != ''">
                AND INSTR(b_shop_info.registered_address,#{queryForm.registeredAddress})
            </if>
            <!--店铺平台-->
            <if test="queryForm.shopPlatform != null and queryForm.shopPlatform != ''">
                AND INSTR(b_shop_info.shop_platform,#{queryForm.shopPlatform})
            </if>
            <!--食品许可证(0:有 1:无)-->
            <if test="queryForm.foodLicense != null">
                AND b_shop_info.food_license = #{queryForm.foodLicense}
            </if>
            <if test="queryForm.deletedFlag != null">
                AND deleted_flag = #{queryForm.deletedFlag}
            </if>
            <!--法定代表人查询-->
            <if test="queryForm.legalRepresentative != null and queryForm.legalRepresentative != ''">
                AND INSTR(b_shop_info.legal_representative,#{queryForm.legalRepresentative})
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            ORDER BY id DESC
        </if>
    </select>

    <update id="batchUpdateDeleted">
        update b_shop_info set deleted_flag = #{deletedFlag}
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="updateDeleted">
        update b_shop_info
        set deleted_flag = #{deletedFlag}
        where id = #{id}
    </update>

</mapper>
