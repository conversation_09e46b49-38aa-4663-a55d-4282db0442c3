<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.secondary.OutHotListMapper">

    <sql id="selectTHotListVo">
        select id,
        title,
        url,
        indexNum,
        type,
        indexStatus,
        newInfo,
        sort,
        areaCode,
        updateTime
        from t_hot_list t
    </sql>

    <select id="selectHotList" resultType="com.boryou.web.domain.OutHot">
        select id,
        title,
        url,
        indexNum,
        type platform,
        sort `order`,
        updateTime ranklistTime,
        updateTime
        from t_hot_list t
        where type in
        <foreach item="type" collection="list" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="keyword != null and keyword != ''">
            <foreach item="word" collection="keyword" separator=" OR " open=" and ( " close=")">
                INSTR(title, #{word})
            </foreach>
        </if>
        <if test="time != null and time != ''">
            and updateTime &gt;= #{time}
        </if>
        order by updateTime desc
    </select>
</mapper>
