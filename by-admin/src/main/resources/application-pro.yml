app:
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/boryou/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /data/uploads/yq-boryou
  #  profile: D:/boryou/uploadPath
  # 获取ip地址开关
  addressEnabled: true

# 数据源配置
spring:
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  # redis 配置
  redis:
    mq:
      streams:
        # key名称
        - name: AFTER_WARN
          groups:
            - name: WARN_GROUP
              consumers: WARN-CONSUMER-A
    cluster:
      #      nodes: **************:7000,**************:7001,**************:7000,**************:7001,**************:7000,**************:7001
      nodes: **************:6379,**************:6379,**************:6379,**************:6379,**************:6379,**************:6379
    # 数据库索引
    database: 0
    # 用户名
    username: u_yq-boryou
    # 密码
    password: Tezk7@utj%MniYr7
    # 连接超时时间
    timeout: 30s
    lettuce:
      cluster:
        refresh:
          adaptive: true
          period: 30s
      pool:
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池中的最大空闲连接
        max-idle: 50
        # 连接池的最大数据库连接数
        max-active: 500
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    dynamic:
      primary: main
      strict: false
      datasource:
        main:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************************
          username: root
          password: pass@123
        secondary:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************************
          username: root
          password: pass@123
      #        pg:
      #          driver-class-name: org.postgresql.Driver
      #          url: *******************************************************************************************************************************************************************************
      #          username: postgres
      #          password: by.IPO@2026
      # HikariCP连接池
      hikari:
        # 最小空闲连接数
        minimum-idle: 10
        # 空闲连接最大存活时间（默认 600000，10分钟）
        idle-timeout: 180000
        # 连接池最大连接数（默认 10）
        maximum-pool-size: 500
        # 是否自动提交（默认 true）
        auto-commit: true
        # 连接池名字
        pool-name: yq-hikari
        # 连接的最长生命周期（默认 1800000，30分钟）
        max-lifetime: 1800000
        # 数据库连接超时时间（默认 30秒）
        connection-timeout: 30000
        connection-test-query: SELECT 1
        #connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_general_ci;

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9666
  servlet:
    # 应用的访问路径
    context-path: /
  #  tomcat:
  #    # tomcat的URI编码
  #    uri-encoding: UTF-8
  #    # tomcat最大线程数，默认为200
  #    max-threads: 800
  #    # Tomcat启动初始化的线程数，默认值25
  #    min-spare-threads: 100
  # undertow 配置
  undertow:
    # HTTP post 最大值（默认：-1 不限）
    max-http-post-size: -1
    # 每块buffer的空间大小，越小被利用越充分
    buffer-size: 16384  # 增加到 16KB
    # 是否分配直接内存
    direct-buffers: true
    io-threads: 16
    worker-threads: 512
    # 支持大消息传输
    max-entity-size: 10485760  # 10MB

# 日志配置
logging:
  level:
    com.boryou: info
    org.springframework: warn

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: mMrefgRRi7998RXpXXFXXwa80N
  # 令牌有效期（默认30分钟）
  expireTime: 300

# MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.boryou.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# Minio配置
minio:
  url: https://oapi.boryou.com:33003
  accessKey: u-yq-zhejiang-court-prod
  secretKey: QdV6W5MreeZFkY6X
  bucketName: zhejiangcourt-prod
#  bucket-name: 111general11-dev
#  access-key: u-xx-general-dev
#  secret-key: YQde8WFAHXTQcM7Bx1

es:
  hosts: **************:9200,**************:9200,**************:9200,**************:9200,**************:9200,**************:9200,**************:9200,**************:9200
  username: elastic-netxman
  password: jWpV7Fvj4_z_jYhfX
  certPath: classpath:certs/es.http_ca.crt

wx:
  callbackUrl: https://justice.boryou.com/prod-api/wx/portal/
  templateId: 8BhV0lpc-obe0pGr6fZdvxvKQPnGxuj0xVaugapWRE8
  mp:
    configs:
      #            - appId: wx5de26a625c1fce0e # 第一个公众号的appid
      #              secret: 4711b93faf2fd77687570d5756831a60 # 公众号的appsecret
      #              token: gehergeabgaegre # 接口配置里的Token值
      #              aesKey: # 接口配置里的EncodingAESKey值
      - appId: wxae7c9658ae7a0345 # 第一个公众号的appid
        secret: 2973be2cab8ab9c564db67291ccb0222 # 公众号的appsecret
        token: boryoutechnology321 # 接口配置里的Token值
        aesKey: dnckoV9QgRMZ6uXhC3E7sKjRrtCKBTePOOlmpCp8avE # 接口配置里的EncodingAESKey值

# 邮箱池配置
mail:
  configs:
    - i: 0【第1个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 1【第2个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 2【第3个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 3【第4个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 4【第5个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 5【第6个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 6【第7个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory
    - i: 7【第8个】
      username: <EMAIL>
      password: Z8x402eq690q3#
      host: smtp.ym.163.com
      port: 25
      protocol: smtp
      default-encoding: UTF-8
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
              socketFactory:
                port: 465
                class: javax.net.ssl.SSLSocketFactory

# 开启预警
runWarn: false
warnHost: https://byyq.boryou.com

# 服务器中文字体路径
fontSettingPath: /usr/share/fonts/

# 配置是否开启AOP参数加密解密，不配置默认为true
isSecret: false
