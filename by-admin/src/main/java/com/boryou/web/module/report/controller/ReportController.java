package com.boryou.web.module.report.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.file.MinioUtil;
import com.boryou.manage.domain.File;
import com.boryou.manage.service.FileService;
import com.boryou.manage.service.MinioFileService;
import com.boryou.web.module.report.entity.Report;
import com.boryou.web.module.report.entity.ReportTemplate;
import com.boryou.web.module.report.entity.vo.ReportVO;
import com.boryou.web.module.report.service.ReportService;
import com.boryou.web.util.ReportFieldUtil;
import io.minio.StatObjectResponse;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.common.config.MinioConfig.getBucketName;

/**
 * <AUTHOR>
 */
@RestController
public class ReportController extends BaseController {

    @Resource
    private ReportService reportService;
    @Resource
    private MinioFileService minioFileService;
    @Resource
    private FileService fileService;

    @GetMapping("/report/selectById/{id}")
    public AjaxResult selectById(@PathVariable Long id) {
        ReportVO reportVO = new ReportVO();
        Report report = reportService.selectById(id);
        ReportTemplate reportTemplate = reportService.selectTemplateById(report.getTempId());
        if (reportTemplate == null) {
            AjaxResult.error("报告模板异常");
        }
        List<Field> fields = ReportFieldUtil.getField(ReportTemplate.class);
        JSONObject reportJson = JSONUtil.parseObj(report);
        BeanUtil.copyProperties(report, reportVO, fields.stream().map(Field::getName).collect(Collectors.joining()));
        JSONObject data = JSONUtil.createObj();
        List<String> inputComponents = new ArrayList<>();
        if (report.getInputComponents() != null) {
            inputComponents = JSONUtil.toList(report.getInputComponents(), String.class);
        }
        try {
            Map<String, Integer> entryMap = new HashMap<>();
            for (Field field : fields) {
                String fieldName = field.getName();
                if (inputComponents.isEmpty()) {
                    field.setAccessible(true);
                    Integer value = (Integer) field.get(reportTemplate);
                    entryMap.put(fieldName, value);
                }
//                if (!inputComponents.isEmpty() && !inputComponents.contains(fieldName)) {
//                    continue;
//                }
                String reportValue = (String) reportJson.get(fieldName);
                if (reportValue == null) {
                    data.putOnce(fieldName, "");
                    continue;
                }
                if (!reportValue.startsWith("[") && !reportValue.startsWith("{")) {
                    data.putOnce(fieldName, reportValue);
                    continue;
                }
                if (JSONUtil.parse(reportValue) instanceof JSONObject) {
                    data.putOnce(fieldName, JSONUtil.parseObj(reportValue));
                } else if (JSONUtil.parse(reportValue) instanceof JSONArray) {
                    data.putOnce(fieldName, JSONUtil.parseArray(reportValue));
                }
            }
            //字段名
            if (inputComponents.isEmpty()) {
                List<Map.Entry<String, Integer>> sortedEntries = entryMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).collect(Collectors.toList());
                inputComponents = sortedEntries.stream().map(Map.Entry::getKey).collect(Collectors.toList());
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }

        data.putOnce("inputComponents", inputComponents);
        reportVO.setData(data);
        return AjaxResult.success(reportVO);
    }

    @GetMapping("/report/selectTemplateById/{id}")
    public AjaxResult selectTemplateById(@PathVariable String id) {
        ReportTemplate reportTemplate = reportService.selectTemplateById(Long.valueOf(id));
        List<String> params = ReportFieldUtil.setTemplateParamSort(reportTemplate);
        reportTemplate.setInputComponents(params);
        return AjaxResult.success(reportTemplate);
    }

    @GetMapping("/report/selectReportPage")
    public TableDataInfo selectReportPage(@ModelAttribute ReportVO vo) {
        vo.setUserId(Long.valueOf(SecurityUtils.getUserId()));
        vo.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        startPage();
        return getDataTable(reportService.selectReport(vo));
    }

    @PostMapping("/report/selectTemplate")
    public AjaxResult selectTemplate(@RequestBody(required = false) ReportTemplate reportTemplate) {
        if (reportTemplate == null) {
            reportTemplate = new ReportTemplate();
        }
        return AjaxResult.success(reportService.selectTemplate(reportTemplate));
    }

    @PostMapping("/report/selectTemplateSortDefault")
    public AjaxResult selectTemplateSortDefault(@RequestBody(required = false) ReportTemplate reportTemplate) {
        if (reportTemplate == null) {
            reportTemplate = new ReportTemplate();
        }
        return AjaxResult.success(reportService.selectTemplateSortDefault(reportTemplate));
    }

    @PostMapping("/report/insertReport")
    public AjaxResult insertReport(@RequestBody Report report) {
        ReportTemplate reportTemplate = reportService.selectTemplateById(report.getTempId());
        if (reportTemplate != null) {
            List<String> params = ReportFieldUtil.setTemplateParamSort(reportTemplate);
            JSONArray array = JSONUtil.parseArray(params);
            report.setInputComponents(array.toString());
        }
        return AjaxResult.success(reportService.insert(report));
    }

    @PostMapping("/report/insertTemplate")
    public AjaxResult insertTemplate(@RequestBody ReportTemplate reportTemplate) {
        ReportFieldUtil.getTemplateParamSort(reportTemplate);
        return AjaxResult.success(reportService.insertTemplate(reportTemplate));
    }

    @PostMapping("/report/updateReport")
    public AjaxResult updateReport(@RequestBody ReportVO reportVO) {
        JSONObject json = reportVO.getData();
        if (json != null) {
            for (Map.Entry<String, Object> stringObjectEntry : json) {
                if (stringObjectEntry.getKey().contains("inputComponents")) {
                    reportVO.setInputComponents(String.valueOf(stringObjectEntry.getValue()));
                    continue;
                }
                String reportValue = "";
                if (stringObjectEntry.getValue() instanceof JSONArray) {
                    JSONArray array = (JSONArray) stringObjectEntry.getValue();
                    reportValue = array.toString();
                } else if (stringObjectEntry.getValue() instanceof JSONObject) {
                    JSONObject object = (JSONObject) stringObjectEntry.getValue();
                    reportValue = object.toString();
                } else {
                    reportValue = (String) stringObjectEntry.getValue();
                }
                if (StrUtil.isEmpty(reportValue)) {
                    continue;
                }
                String fieldName = stringObjectEntry.getKey();
                try {
                    Field field = Report.class.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(reportVO, reportValue);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        Report report = new Report();
        BeanUtil.copyProperties(reportVO, report);
        Long fileId = reportService.selectById(reportVO.getReportId()).getFileId();
        if (fileId != null) {
            fileService.deleteFileById(fileId);
            report.setFileId(null);
        }
        return AjaxResult.success(reportService.updateById(report));
    }

    @GetMapping("/report/updateDefaultTemplate/{id}")
    public AjaxResult updateDefaultTemplate(@PathVariable String id) {
        return AjaxResult.success(reportService.updateDefaultTemplate(Long.valueOf(id)));
    }

    @PostMapping("/report/updateTemplateById")
    public AjaxResult updateTemplateById(@RequestBody ReportTemplate reportTemplate) {
        ReportFieldUtil.getTemplateParamSort(reportTemplate);
        return AjaxResult.success(reportService.updateTemplateById(reportTemplate));
    }

    @DeleteMapping("/report/deleteById/{id}")
    public AjaxResult deleteById(@PathVariable String id) {
        List<Report> reports = reportService.selectByIds(id);
        for (Report report : reports) {
            if (report.getFileId() != null) {
                fileService.deleteFileById(report.getFileId());
            }
        }
        return AjaxResult.success(reportService.deleteByIds(id));
    }

    @DeleteMapping("/report/deleteTemplateById/{id}")
    public AjaxResult deleteTemplateById(@PathVariable String id) {
        return AjaxResult.success(reportService.deleteTemplateByIds(id));
    }

    @GetMapping("/report/changeTemplateToSys/{id}")
    public AjaxResult changeTemplateToSys(@PathVariable String id) {
        return AjaxResult.success(reportService.changeTemplateToSys(Long.valueOf(id)));
    }

    /**
     * 上传报告
     */
    @PostMapping("/report/uploadReport")
    public AjaxResult uploadReport(@RequestPart("file") MultipartFile file) {
        return AjaxResult.success("", reportService.upload(file).toString());
    }

    /**
     * 下载报告
     */
    @PostMapping("/report/downloadReport")
    public void downloadFile(HttpServletResponse response, @RequestBody ReportVO reportVO) {
        Report report = reportService.selectById(reportVO.getReportId());
        if (report != null && report.getFileId() != null) {
            File file = fileService.selectFileById(report.getFileId());
            if (reportVO.getFileType() == null || (!file.getType().contains("doc") && !file.getType().contains("pdf")) || file.getType().equals(reportVO.getFileType())) {
                minioFileService.downloadFile(file, response);
            } else {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(file.getOriginal(), Constants.UTF8));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                response.setCharacterEncoding(Constants.UTF8);
                StatObjectResponse fileSInfo = MinioUtil.getFileInfo(getBucketName(), file.getFileName());
                String contentType = fileSInfo.contentType();
                response.setContentType(contentType);
                reportService.changeFileTypeToDownload(minioFileService.downloadInputStream(file.getFileName()), response, file.getType());
            }
        }
        if (report != null && report.getFileId() == null) {
            String path = "reportFile/";
            FileUtil.mkdir(path);
            //文件夹中的文件都未使用时尝试清空文件夹
            FileUtil.clean(path);
            java.io.File file = FileUtil.file(path + SecurityUtils.getUserId() + ".docx");

            InputStream in = reportService.createLocalReportFile(reportVO, file);
            response.setCharacterEncoding(Constants.UTF8);
            if (reportVO.getFileType() == null || reportVO.getFileType().contains("doc")) {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(report.getTitle() + ".docx", Constants.UTF8));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                response.setContentType("application/docx; charset=UTF-8");
                ServletOutputStream out = null;

                try {
                    out = response.getOutputStream();
                    IOUtils.copy(in, out);
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if (out != null) {
                        try {
                            out.flush();
                            out.close();
                            if (in != null) {
                                in.close();
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            } else {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(report.getTitle() + "." + reportVO.getFileType(), Constants.UTF8));
                    response.setContentType("application/pdf; charset=UTF-8");
                    reportService.changeFileTypeToDownload(in, response, "");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                } finally {
                    if (in != null) {
                        try {
                            in.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }

            }
        }
    }
    /**
     * 下载报告
     */
    @GetMapping("/report/downloadReportNew")
    public void downloadReportNew(HttpServletResponse response,  ReportVO reportVO) {
        Report report = reportService.selectById(reportVO.getReportId());
        if (report != null && report.getFileId() != null) {
            File file = fileService.selectFileById(report.getFileId());
            if (reportVO.getFileType() == null || (!file.getType().contains("doc") && !file.getType().contains("pdf")) || file.getType().equals(reportVO.getFileType())) {
                minioFileService.downloadFile(file, response);
            } else {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(file.getOriginal(), Constants.UTF8));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                response.setCharacterEncoding(Constants.UTF8);
                StatObjectResponse fileSInfo = MinioUtil.getFileInfo(getBucketName(), file.getFileName());
                String contentType = fileSInfo.contentType();
                response.setContentType(contentType);
                reportService.changeFileTypeToDownload(minioFileService.downloadInputStream(file.getFileName()), response, file.getType());
            }
        }
        if (report != null && report.getFileId() == null) {
            String path = "reportFile/";
            FileUtil.mkdir(path);
            //文件夹中的文件都未使用时尝试清空文件夹
            FileUtil.clean(path);
            java.io.File file = FileUtil.file(path + SecurityUtils.getUserId() + ".docx");

            InputStream in = reportService.createLocalReportFile(reportVO, file);
            response.setCharacterEncoding(Constants.UTF8);
            if (reportVO.getFileType() == null || reportVO.getFileType().contains("doc")) {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(report.getTitle() + ".docx", Constants.UTF8));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                response.setContentType("application/docx; charset=UTF-8");
                ServletOutputStream out = null;

                try {
                    out = response.getOutputStream();
                    IOUtils.copy(in, out);
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if (out != null) {
                        try {
                            out.flush();
                            out.close();
                            if (in != null) {
                                in.close();
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            } else {
                try {
                    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(report.getTitle() + "." + reportVO.getFileType(), Constants.UTF8));
                    response.setContentType("application/pdf; charset=UTF-8");
                    reportService.changeFileTypeToDownload(in, response, "");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                } finally {
                    if (in != null) {
                        try {
                            in.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }

            }
        }
    }

    /**
     * 预览报告
     */

}
