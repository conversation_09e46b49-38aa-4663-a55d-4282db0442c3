package com.boryou.web.module.socket.redis.handler;

import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WebsocketMessageHandler implements WsToServiceMsgHandler {

    @Override
    public void handlerMsg(TransferMessage transferMessage) {
        // [do something ...] 在业务层接收透传过来的 websocket信息，进行业务处理
        log.info(" [WsToService] transferMessage = {}", JacksonUtils.toJson(transferMessage));
    }

}
