package com.boryou.web.module.drill.domain.vo;

import com.boryou.web.module.drill.enums.CommentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillCommentReplyRes {
    /**
     * 评论ID
     */

    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentReplyId;

    /**
     * 所属微博ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 评论者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String content;

    /**
     * 父评论ID（0=顶级评论）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 根评论ID（树形结构快速查询）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 评论时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 点赞数
     */
    private Integer likeCount = 0;

    /**
     * 状态（0=正常，1=删除，2=审核中）
     */
    private Integer status;

    private Integer teamType;

    private String roleInfo;

    private String nickName;

    private String avatar;

    private String userName;

    private Boolean isLike;

    private String commentType = CommentEnum.COMMENT_REPLY.getType();

    private List<String> file;

    private List<DrillCommentReplyRes> children;

}
