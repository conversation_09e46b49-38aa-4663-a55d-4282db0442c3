package com.boryou.web.module.api.controller;

import cn.hutool.db.PageResult;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.module.api.domain.entity.ApiParam;
import com.boryou.web.module.api.domain.vo.ApiAccountVO;
import com.boryou.web.module.api.service.ApiLocalService;
import com.boryou.web.module.api.service.ApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class ApiController {
    private final ApiService apiService;
    private final ApiLocalService apiLocalService;

    @PostMapping("/api/v1/account")
    public AjaxResult v1Account(@RequestBody ApiParam<ApiAccountVO> apiParam) {
        String auth = apiParam.getAuth();
        apiService.judgeAuth(auth);
        ApiAccountVO param = apiParam.getParam();
        return AjaxResult.success(apiService.v1Account(param));
    }

    @PostMapping("/api/local/account")
    public AjaxResult localAccount(@RequestBody ApiAccountVO apiParam) {
        return AjaxResult.success(apiService.v1Account(apiParam));
    }

    @PostMapping("/api/local/article")
    public TableDataInfo localArticle(@RequestBody ApiAccountVO apiParam) {
        PageResult<EsBean> pageResult = apiLocalService.article(apiParam);
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

}
