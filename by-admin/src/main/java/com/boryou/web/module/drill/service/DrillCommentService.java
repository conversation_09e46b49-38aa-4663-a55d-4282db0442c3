package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.drill.domain.DrillComment;
import com.boryou.web.module.drill.domain.DrillCommentLike;
import com.boryou.web.module.drill.domain.DrillCommentReply;
import com.boryou.web.module.drill.domain.vo.*;
import com.boryou.web.module.drill.enums.CommentEnum;
import com.boryou.web.module.drill.enums.TeamTypeEnum;
import com.boryou.web.module.drill.mapper.DrillCommentMapper;
import com.boryou.web.module.drill.util.LikeRedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillCommentService extends ServiceImpl<DrillCommentMapper, DrillComment> {

    private final DrillScoreService drillScoreService;
    private final DrillCommentReplyService drillCommentReplyService;
    private final DrillCommentLikeService drillCommentLikeService;
    private final DrillWebsocketService drillWebsocketService;
    private final LikeRedisUtil likeRedisUtil;
    private final DrillCommentAsyncService drillCommentAsyncService;

    public void buildCommentRes(List<DrillComment> drillCommentList,
                                List<DrillCommentReply> drillCommentReplyList,
                                List<DrillProcessRes.DrillProcessStageRes> drillProcessStageRes,
                                Map<Long, SysUser> userMap,
                                List<Long> commentLikeId,
                                SysUser user) {
        if (CollUtil.isEmpty(drillCommentList)) {
            return;
        }

        Map<Long, List<DrillCommentReply>> drillCommetnReplyProcessStageMap = CollStreamUtil.groupByKey(drillCommentReplyList, DrillCommentReply::getProcessStageId);
        Map<Long, List<DrillCommentReplyRes>> drillCommentReplyResMap = drillCommentReplyService.convertToTreeStructure(drillCommentReplyList, userMap, commentLikeId, user);


        Map<Long, String> commentOrderMap = CollStreamUtil.toMap(drillCommentList, DrillComment::getCommentId, DrillComment::getCommentOrder);
        Map<Long, List<DrillComment>> processStageIdMap = CollStreamUtil.groupBy(drillCommentList, DrillComment::getProcessStageId,
                Collectors.collectingAndThen(Collectors.toList(), subList -> {
                    subList.sort(Comparator.comparing(DrillComment::getCreateTime));
                    return subList;
                })
        );
        // 现在分数按照评论条数来
        // Map<String, DrillScore> drillScoreMap = drillScoreService.getDrillScoreMap();
        // List<DrillProcessRes.DrillProcessStageRes.DrillStageScoreRes> redDrillStageScoreResList = new ArrayList<>();
        // List<DrillProcessRes.DrillProcessStageRes.DrillStageScoreRes> blueDrillStageScoreResList = new ArrayList<>();
        // List<String> redTotalScores = new ArrayList<>();
        // List<String> blueTotalScores = new ArrayList<>();
        List<String> redBasiclScores = new ArrayList<>();
        List<String> blueBasicScores = new ArrayList<>();
        List<String> redExpertScores = new ArrayList<>();
        List<String> blueExpertScores = new ArrayList<>();

        Map<Long, String> stageNameMap = CollStreamUtil.toMap(drillProcessStageRes, DrillProcessRes.DrillProcessStageRes::getProcessStageId, DrillProcessRes.DrillProcessStageRes::getStageName);

        for (DrillProcessRes.DrillProcessStageRes drillProcessStageRe : drillProcessStageRes) {
            Long processStageId = drillProcessStageRe.getProcessStageId();
            String stageName = drillProcessStageRe.getStageName();
            Integer stageType = drillProcessStageRe.getStageType();
            Integer scoreType = drillProcessStageRe.getScoreType();

            List<DrillComment> processStageCommentList = processStageIdMap.get(processStageId);


            if (CollUtil.isEmpty(processStageCommentList)) {
                // drillScoreService.build0Score(stageType, stageName, redDrillStageScoreResList, blueDrillStageScoreResList);
                // drillScoreService.build0ScoreBySize(scoreType, scoreAccumulator.getScoreSetter());
                continue;
            }


            // drillScoreService.buildScore(processStageCommentList, drillScoreMap, redTotalScores, blueTotalScores, redDrillStageScoreResList, stageName, blueDrillStageScoreResList);
            List<DrillCommentReply> drillCommentReplies = drillCommetnReplyProcessStageMap.get(processStageId);

            drillScoreService.buildScore((red, blue) -> {
                drillProcessStageRe.setRedStageScore(red);
                drillProcessStageRe.setBlueStageScore(blue);
            }, scoreType, processStageCommentList, drillCommentReplies);

            String redStageScore = drillProcessStageRe.getRedStageScore();
            String blueStageScore = drillProcessStageRe.getBlueStageScore();
            redBasiclScores.add(redStageScore);
            blueBasicScores.add(blueStageScore);

            List<DrillProcessRes.DrillProcessStageRes.DrillCommentRes> drillCommentRes = BeanUtil.copyToList(processStageCommentList, DrillProcessRes.DrillProcessStageRes.DrillCommentRes.class);
            for (DrillProcessRes.DrillProcessStageRes.DrillCommentRes drillCommentRe : drillCommentRes) {
                Long userId = drillCommentRe.getUserId();
                if (userMap.containsKey(userId)) {
                    SysUser sysUser = userMap.get(userId);
                    String nickName = sysUser.getNickName();
                    String userName = sysUser.getUserName();
                    String avatar = sysUser.getAvatar();

                    drillCommentRe.setNickName(nickName);
                    drillCommentRe.setUserName(userName);
                    drillCommentRe.setAvatar(avatar);
                }
                drillCommentRe.setStageName(stageName);

                String commentType = drillCommentRe.getCommentType();
                Long parentCommentId = drillCommentRe.getParentCommentId();
                Integer teamType = drillCommentRe.getTeamType();
                String score = drillCommentRe.getScore();

                if (Objects.equals(commentType, CommentEnum.EXPERT_COMMENT.getType())) {
                    // 专家点评分数
                    if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
                        redExpertScores.add(score);
                    } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
                        blueExpertScores.add(score);
                    }
                    Long scoreProcessStageId = drillCommentRe.getScoreProcessStageId();
                    drillCommentRe.setScoreStageName(stageNameMap.getOrDefault(scoreProcessStageId, null));
                }

                if (!Objects.equals(commentType, CommentEnum.COMMAND.getType()) && commentOrderMap.containsKey(parentCommentId)) {
                    String commentOrder = commentOrderMap.get(parentCommentId);
                    drillCommentRe.setCommentOrder(commentOrder);
                }

                Long commentId = drillCommentRe.getCommentId();

                // 设置点赞状态
                drillCommentRe.setIsLike(commentLikeId.contains(commentId));

                // 尝试从Redis中获取最新的点赞数
                try {
                    if (userMap.containsKey(userId)) {
                        // 检查Redis中的点赞数据是否需要恢复
                        boolean needRecover = likeRedisUtil.needRecoverLikeData(userId, commentId, commentType);

                        if (needRecover) {
                            // 从数据库恢复Redis数据
                            final Long finalCommentId = commentId; // 在Lambda中使用需要final变量
                            likeRedisUtil.recoverLikeData(
                                    userId,
                                    commentId,
                                    commentType,
                                    // 从数据库获取点赞状态
                                    () -> {
                                        DrillCommentLike like = drillCommentLikeService.lambdaQuery()
                                                .eq(DrillCommentLike::getUserId, userId)
                                                .eq(DrillCommentLike::getCommentReplyId, finalCommentId)
                                                .eq(DrillCommentLike::getCommentType, commentType)
                                                .one();
                                        return like != null && Objects.equals(like.getStatus(), 1);
                                    },
                                    // 从数据库获取点赞数量
                                    () -> Convert.toLong(drillCommentLikeService.lambdaQuery()
                                            .eq(DrillCommentLike::getCommentReplyId, finalCommentId)
                                            .eq(DrillCommentLike::getCommentType, commentType)
                                            .eq(DrillCommentLike::getStatus, 1)
                                            .count())
                            );
                            log.info("在构建评论时从数据库恢复了Redis点赞数据。userId={}, commentId={}, commentType={}",
                                    userId, commentId, commentType);
                        }

                        // 获取最新的点赞数
                        Long likeCount = likeRedisUtil.getLikeCount(commentId, commentType);
                        if (likeCount != null && likeCount > 0) {
                            drillCommentRe.setLikeCount(likeCount.intValue());
                        }
                    }
                } catch (Exception e) {
                    log.error("从Redis获取评论点赞数失败: commentId={}", commentId, e);
                }

                if (drillCommentReplyResMap.containsKey(commentId)) {
                    List<DrillCommentReplyRes> drillCommentReplyRes = drillCommentReplyResMap.get(commentId);
                    drillCommentRe.setDrillCommentReplyRes(drillCommentReplyRes);
                }
            }
            drillProcessStageRe.setDrillCommentResList(drillCommentRes);

        }

        // drillScoreService.buildTypeLastScore(drillProcessStageRes, redTotalScores, blueTotalScores, redDrillStageScoreResList, blueDrillStageScoreResList);
        drillScoreService.buildTypeLastScore(drillProcessStageRes,
                redBasiclScores, blueBasicScores, redExpertScores, blueExpertScores);
    }

    /**
     * 添加评论/回复
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCommentReply(DrillCommentReplyVO drillCommentReplyVO, SysUser user) {
        Long parentId = drillCommentReplyVO.getParentId();
        Long rootId = drillCommentReplyVO.getRootId();
        Long drillTaskId = drillCommentReplyVO.getDrillTaskId();

        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        // 设置根评论ID（如果是顶级评论，rootId=自身ID）
        if (Objects.equals(parentId, 0L)) {
            rootId = snowflakeNextId;
        }
        if (rootId == null) {
            throw new CustomException("rootId 不能为空");
        }
        DateTime date = DateUtil.date();

        Long userId = user.getUserId();
        String userName = user.getUserName();
        String nickName = user.getNickName();
        String avatar = user.getAvatar();

        DrillCommentReply drillCommentReply = BeanUtil.copyProperties(drillCommentReplyVO, DrillCommentReply.class);
        drillCommentReply.setCommentReplyId(snowflakeNextId);
        drillCommentReply.setCreatedTime(date);
        drillCommentReply.setStatus(0);
        drillCommentReply.setRootId(rootId);
        drillCommentReply.setUserId(userId);

        drillCommentReplyService.save(drillCommentReply);


        DrillCommentReplyPushVO drillCommentReplyPushVO = BeanUtil.copyProperties(drillCommentReply, DrillCommentReplyPushVO.class);
        drillCommentReplyPushVO.setUserName(userName);
        drillCommentReplyPushVO.setNickName(nickName);
        drillCommentReplyPushVO.setAvatar(avatar);

        drillWebsocketService.commentReplyToUser(drillCommentReplyPushVO, drillTaskId, user);
    }

    // 更新评论点赞数并发送WebSocket消息
    public void sendLikeWebSocketMessage(DrillCommentLikePushVO drillCommentLikePushVO, SysUser user) {
        Long commentId = drillCommentLikePushVO.getCommentId();
        Long commentReplyId = drillCommentLikePushVO.getCommentReplyId();
        Long drillTaskId = drillCommentLikePushVO.getDrillTaskId();
        Integer commentType = drillCommentLikePushVO.getCommentType();
        String typeStr = Convert.toStr(commentType);
        Integer likeCount = drillCommentLikePushVO.getLikeCount();
        Long processStageId = drillCommentLikePushVO.getProcessStageId();

        // 发送WebSocket消息
        drillWebsocketService.commentLikeToUser(drillCommentLikePushVO, drillTaskId, user);

        // 如果点赞数达到3，发送弹幕
        if (likeCount >= 3) {
            String comment = drillCommentLikePushVO.getComment();
            if (Objects.equals(typeStr, CommentEnum.COMMENT_REPLY.getType()) &&
                    CharSequenceUtil.isNotBlank(comment)) {
                DrillDanmuVO drillDanmuVO = DrillDanmuVO.builder()
                        .drillTaskId(drillTaskId)
                        .processStageId(processStageId)
                        .content(comment)
                        .build();
                drillWebsocketService.danmuToUser(drillDanmuVO, drillTaskId, user);
            }
        }

        // 异步更新数据库中的点赞计数
        drillCommentAsyncService.asyncUpdateLikeCountInDatabase(commentId, commentReplyId, commentType, likeCount);
    }



    /**
     * 点赞操作
     */
    public DrillCommentLikePushVO commentReplyLikeAdd(DrillCommentLikeVO drillCommentLikeVO, SysUser user) {
        // 使用新的统一切换点赞状态方法
        DrillCommentLikePushVO pushVO = drillCommentLikeService.toggleLike(drillCommentLikeVO, user);

        // 发送WebSocket消息
        sendLikeWebSocketMessage(pushVO, user);

        return pushVO;
    }

    /**
     * 取消点赞
     */
    public DrillCommentLikePushVO commentReplyLikeCancel(DrillCommentLikeVO drillCommentLikeVO, SysUser user) {
        // 使用新的统一切换点赞状态方法
        DrillCommentLikePushVO pushVO = drillCommentLikeService.toggleLike(drillCommentLikeVO, user);

        // 发送WebSocket消息
        sendLikeWebSocketMessage(pushVO, user);

        return pushVO;
    }

}
