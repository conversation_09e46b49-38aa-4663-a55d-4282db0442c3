package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum RemainTimeEnum {

    START("开始", "1"),
    PAUSE("暂停", "2"),
    END("结束", "3"),
    ;

    private static final Map<String, RemainTimeEnum> map = new HashMap<>();

    static {
        RemainTimeEnum[] ens = RemainTimeEnum.values();
        for (RemainTimeEnum en : ens) {
            map.put(en.getType(), en);
        }
    }

    private final String name;
    private final String type;

    public static RemainTimeEnum getEnumByType(String type) {
        return map.get(type);
    }

    public static Set<String> getTypes() {
        return map.keySet();
    }

}
