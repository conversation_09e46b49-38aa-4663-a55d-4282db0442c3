package com.boryou.web.module.webservice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.exception.CustomException;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.web.module.webservice.domain.JhDept;
import com.boryou.web.module.webservice.mapper.JhDeptMapper;
import com.boryou.web.module.webservice.service.JhDeptService;
import com.boryou.web.module.webservice.util.XmlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.w3c.dom.Element;

import java.util.HashMap;
import java.util.Map;

import static com.boryou.web.module.webservice.util.XmlUtil.strToElement;
import static com.boryou.web.module.webservice.util.XmlUtil.traverseNodes;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午6:00
 */
@Service
@RequiredArgsConstructor
public class JhDeptServiceImpl extends ServiceImpl<JhDeptMapper, JhDept> implements JhDeptService {

    private final SysDeptMapper deptMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String synDeptInfo(String str) {
        // todo 校验 token
        try {
            Element element = strToElement(str);
            Map<String, Object> map = new HashMap<>();
            traverseNodes(element, map);
            JhDept bean = BeanUtil.toBean(map, JhDept.class);

            if (bean.getIdentification().equals("add")) {
                if (this.count(Wrappers.<JhDept>lambdaQuery().eq(JhDept::getDeptCode, bean.getDeptCode())) == 0) {
                    if (this.save(bean)) {
                        SysDept sysDept = toSysDept(bean);
                        if (deptMapper.selectCount(Wrappers.lambdaQuery(SysDept.class).eq(SysDept::getDeptId, bean.getDeptCode())) == 0) {
                            if (deptMapper.insert(sysDept) == 1) {
                                return XmlUtil.returnXMLStr(true);
                            }
                        }
                    }
                    throw new CustomException("新增科室失败");
                }
                throw new CustomException("科室已存在");
            }
            // 修改
            else if (bean.getIdentification().equals("update")) {
                if (this.updateById(bean)) {
                    SysDept sysDept = toSysDept(bean);
                    if (deptMapper.updateDept(sysDept) == 1) {
                        return XmlUtil.returnXMLStr(true);
                    }
                }
                throw new CustomException("更新失败");
            } else if (bean.getIdentification().equals("del")) {
                if (this.removeById(bean.getDeptCode())) {
                    if (deptMapper.deleteById(bean.getDeptCode()) == 1) {
                        return XmlUtil.returnXMLStr(true);
                    }
                }
                throw new CustomException("删除失败");
            } else {
                throw new CustomException("非法接口类型");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return XmlUtil.returnXMLStr(false, e.getMessage());
        }
    }

    private SysDept toSysDept(JhDept bean) {
        SysDept parent = deptMapper.selectDeptById(Long.valueOf(bean.getDeptFatherCode()));
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(Long.valueOf(bean.getDeptCode()));
        sysDept.setParentId(Long.valueOf(bean.getDeptFatherCode()));
        sysDept.setAncestors("0");
        if (parent != null) {
            sysDept.setAncestors(parent.getAncestors() + "," + parent.getDeptId());
        }
        sysDept.setDeptName(bean.getDeptName());
        sysDept.setOrderNum("0");
        sysDept.setPhone(bean.getDeptPhone());
        return sysDept;
    }
}




