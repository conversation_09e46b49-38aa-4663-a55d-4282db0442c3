package com.boryou.web.module.home.entity.vo;

import com.boryou.web.domain.vo.BaseSimpleVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-05-28 13:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleVO extends BaseSimpleVO {
    //    private String name;
//    private Integer count=0;
    private String plantId;
    private Date createTime;
    private int historyFlag;
    private Long userId;

}

