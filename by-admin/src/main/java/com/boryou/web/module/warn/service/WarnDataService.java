package com.boryou.web.module.warn.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.domain.vo.WarnSMVO;
import com.boryou.web.util.SMUtil;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class WarnDataService {
    @Resource
    private WarnApiService warnApiService;
    @Resource
    private WarnDataApiService warnDataApiService;
    @Resource
    private MaterialService materialService;
    @Resource
    private SysDeptMapper sysDeptMapper;

    public PageResult<EsBean> warnDataGet(WarnDataVO warnDataVO) {
        boolean isAuthentication = false;
        Authentication authentication = SecurityUtils.getAuthentication();
        if (authentication != null) {
            isAuthentication = !authentication.getName().contains("anonymousUser");
        }
        List<String> users = new ArrayList<>();
        Long deptId = null;
        if (isAuthentication) {
            SysUser sysUser = SecurityUtils.getLoginUser().getUser();
            deptId = sysUser.getDeptId();
            Long userId = sysUser.getUserId();
            users.add(String.valueOf(userId));
        } else {
            deptId = warnDataVO.getDeptId();
            users = warnDataVO.getUsers();
        }
        if (deptId == null || CollUtil.isEmpty(users)) {
            throw new CustomException("没有权限");
        }
        List<String> deptIds = sysDeptMapper.selectManageDeptString(deptId);
        if (CollUtil.isEmpty(deptIds)) {
            throw new CustomException("没有权限");
        }
        warnDataVO.setDeptIds(deptIds);
        warnDataVO.setUsers(users);
        return warnApiService.warnDataGet(warnDataVO);
    }

    public void buildPushString(WarnDataVO warnDataVO) {
        String pushString = warnDataVO.getPushString();
        if (CharSequenceUtil.isBlank(pushString)) {
            throw new CustomException("没有权限");
        }
        WarnSMVO bean = null;
        try {
            String decrypt = SMUtil.decrypt(pushString);
            bean = JSONUtil.toBean(decrypt, WarnSMVO.class);
            if (bean == null) {
                throw new CustomException("没有权限");
            }
        } catch (Exception e) {
            throw new CustomException("没有权限");
        }

        String date = bean.getDate();
        String planId = bean.getPlanId();
        Long deptId = bean.getDeptId();
        List<String> users = bean.getUsers();
        warnDataVO.setWarnDateStart(date);
        warnDataVO.setWarnDateEnd(date);
        warnDataVO.setPlanId(planId);
        warnDataVO.setDeptId(deptId);
        warnDataVO.setUsers(users);
    }

    public List<EsBean> warnDataPush(EsSearchBO esSearchBO) {
        return warnApiService.warnDataPush(esSearchBO);
    }

    public boolean warnDataAdd(List<EsBean> warnDataResList) {
        return warnApiService.warnDataAdd(warnDataResList);
    }

    public boolean addMaterial(PageResult<EsBean> pageResult, WarnDataVO warnDataVO) {
        List<Material> materialList = new ArrayList<>();
        String folderId = warnDataVO.getFolderId();

        for (EsBean esBean : pageResult) {
            Material material = buildMaterial(esBean, folderId);
            materialList.add(material);
        }

        return materialService.addBatch(materialList);
    }

    private Material buildMaterial(EsBean esBean, String folderId) {
        Integer type = esBean.getType();
        String typeName = esBean.getTypeName();
        Date publishTime = esBean.getPublishTime();
        String title = esBean.getTitle();
        String text = esBean.getText();
        String url = esBean.getUrl();
        String host = esBean.getHost();
        String author = esBean.getAuthor();
        String siteAreaCodeName = esBean.getSiteAreaCodeName();
        Integer emotionFlag = esBean.getEmotionFlag();
        Boolean isOriginal = esBean.getIsOriginal();
        String hitWords = esBean.getHitWords();
        String articleId = esBean.getArticleId();
        String md5 = esBean.getMd5();
        Integer similarCount = esBean.getSimilarCount();
        if (similarCount == null) {
            similarCount = 1;
        }

        Material material = new Material();
        material.setContentId(Long.valueOf(articleId));
        material.setFolderId(Long.valueOf(folderId));
        material.setType(type);
        material.setTypeName(typeName);
        material.setTitle(title);
        material.setText(text);
        material.setUrl(url);
        material.setHost(host);
        material.setAuthor(author);
        material.setEmotionFlag(emotionFlag);
        material.setSiteAreaCodeName(siteAreaCodeName);
        material.setOriginFlag((isOriginal == null || isOriginal) ? "1" : "0");
        material.setHitWords(hitWords);
        material.setPublishTime(publishTime);
        material.setMd5(md5);
        material.setSimilarCount(similarCount);
        return material;
    }

    public List<String> warnDataCheck(WarnDataVO warnDataVO) {
        return warnDataApiService.warnDataCheck(warnDataVO);
    }

    public void judgeIds(WarnDataVO warnDataVO) {
        List<String> ids = warnDataVO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            warnDataVO.setPageNum(1);
            warnDataVO.setPageSize(ids.size());
        }
    }

    public Map<String, Integer> warnSimilar(List<String> md55000) {
        return warnDataApiService.warnSimilar(md55000);
    }
}
