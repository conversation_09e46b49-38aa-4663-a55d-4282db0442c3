package com.boryou.web.module.collection.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.boryou.web.module.material.entity.MaterialFolder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:18
 */
@TableName("by_collection_folder")
@Data
public class CollectionFolder {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 是否是群组
     */
    private String isGroup;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 父级 id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId = 0L;

    /**
     * 素材数量
     */
    @TableField(exist = false)
    private Integer count;

    @TableField(exist = false)
    private List<CollectionFolder> children;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
