package com.boryou.web.module.search.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.module.search.entity.FilterInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FilterInfoMapper extends BaseMapper<FilterInfo> {

    List<FilterInfo> selectFilterInfo(FilterInfo filterInfo);

    FilterInfo selectFilterInfoById(@Param("id") Long id);

    FilterInfo selectFilterInfoByIndexIdAndPlanId(@Param("indexId") Long indexId, @Param("planId") Long planId);

    int insertFilterInfo(FilterInfo filterInfo);

    int deleteFilterInfoByIds(@Param("ids") String ids);

    List<String> selectFilterInfoBySize(FilterInfo filterInfo);
}
