package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("by_drill_stage")
public class DrillStage {
    @TableId(value = "stage_id", type = IdType.AUTO)
    @ApiModelProperty(value = "阶段ID（自增主键）")
    private Long stageId;

    @ApiModelProperty(value = "阶段名称")
    private String stageName;

    @ApiModelProperty(value = "阶段顺序")
    private Integer stageOrder;

    @ApiModelProperty(value = "计时类型：1-同时计时，2-分开计时")
    private Integer timerType;

    @ApiModelProperty(value = "倒计时时长（秒）")
    private Integer timerDuration;

    @ApiModelProperty(value = "类型：1-默认")
    private Integer type;
    @ApiModelProperty(value = "类型：1-默认")

    private Integer stageType;

    private Integer delFlag;

    /**
     * 1:不显示得分 2:显示得分
     */
    private Integer scoreType;
}
