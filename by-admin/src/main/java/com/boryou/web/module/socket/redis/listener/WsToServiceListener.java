package com.boryou.web.module.socket.redis.listener;

import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.module.socket.redis.handler.WsToServiceMsgHandler;
import com.boryou.web.util.RedisStaticUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * redis消息listener, 用于监听websocket to service消息的推送
 *
 * <AUTHOR>
 * @since 2023/9/8 10:12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WsToServiceListener implements MessageListener {

    private final List<WsToServiceMsgHandler> messageHandlers;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        TransferMessage tm = RedisStaticUtils.deserializeMessageStrict(message.getBody(), TransferMessage.class);
        // TransferMessage tm = (TransferMessage) redisTemplate.getValueSerializer().deserialize(message.getBody());
        // 调用所有实现了TransferMessageHandler接口的处理器
        for (WsToServiceMsgHandler handler : messageHandlers) {
            handler.handlerMsg(tm);
        }
    }

}
