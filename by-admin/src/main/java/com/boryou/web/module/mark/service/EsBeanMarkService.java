package com.boryou.web.module.mark.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.mapper.EsBeanMarkMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * EsBeanMark 服务类
 * 模仿 module/warn 下的代码写法
 */
@Slf4j
@Service
public class EsBeanMarkService extends ServiceImpl<EsBeanMarkMapper, EsBeanMark> {

    /**
     * 根据文章ID获取EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return EsBeanMark信息
     */
    public EsBeanMarkVO getEsBeanMarkByArticleId(EsBeanMarkVO esBeanMarkVO) {
        String articleId = esBeanMarkVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }

        LambdaQueryWrapper<EsBeanMark> queryWrapper = new LambdaQueryWrapper<EsBeanMark>()
                .eq(EsBeanMark::getArticleId, articleId);

        EsBeanMark entity = this.getOne(queryWrapper);
        if (entity == null) {
            return null;
        }

        return BeanUtil.copyProperties(entity, EsBeanMarkVO.class);
    }

    /**
     * 分页查询EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return 分页结果
     */
    public IPage<EsBeanMarkVO> getEsBeanMarkPage(EsBeanMarkVO esBeanMarkVO) {
        Page<EsBeanMark> page = new Page<>(esBeanMarkVO.getPageNum(), esBeanMarkVO.getPageSize());

        LambdaQueryWrapper<EsBeanMark> queryWrapper = new LambdaQueryWrapper<>();

        // 根据条件构建查询
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getArticleId())) {
            queryWrapper.eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId());
        }
        if (esBeanMarkVO.getType() != null) {
            queryWrapper.eq(EsBeanMark::getType, esBeanMarkVO.getType());
        }
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getAuthor())) {
            queryWrapper.like(EsBeanMark::getAuthor, esBeanMarkVO.getAuthor());
        }
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getTitle())) {
            queryWrapper.like(EsBeanMark::getTitle, esBeanMarkVO.getTitle());
        }
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getPlanId())) {
            queryWrapper.eq(EsBeanMark::getPlanId, esBeanMarkVO.getPlanId());
        }
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getUserId())) {
            queryWrapper.eq(EsBeanMark::getUserId, esBeanMarkVO.getUserId());
        }
        if (CharSequenceUtil.isNotBlank(esBeanMarkVO.getDeptId())) {
            queryWrapper.eq(EsBeanMark::getDeptId, esBeanMarkVO.getDeptId());
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(EsBeanMark::getCreateTime);

        IPage<EsBeanMark> entityPage = this.page(page, queryWrapper);

        // 转换为VO
        Page<EsBeanMarkVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<EsBeanMarkVO> voList = BeanUtil.copyToList(entityPage.getRecords(), EsBeanMarkVO.class);
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 添加或更新EsBeanMark信息
     * 使用 lambdaUpdate().set() 方法，只有传进来的值与原值不一样才set
     *
     * @param esBeanMarkVO EsBeanMark信息
     * @param user 当前用户
     * @return 是否成功
     */
    public boolean saveOrUpdateEsBeanMark(EsBeanMarkVO esBeanMarkVO, SysUser user) {
        String articleId = esBeanMarkVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }

        Long userId = user.getUserId();
        String userName = user.getUserName();
        Long deptId = user.getDeptId();

        // 查询是否已存在
        EsBeanMark existEntity = this.lambdaQuery()
                .eq(EsBeanMark::getArticleId, articleId)
                .eq(EsBeanMark::getUserId, userId)
                .one();

        EsBeanMark esBeanMark = BeanUtil.copyProperties(esBeanMarkVO, EsBeanMark.class);

        Date date = new Date();

        if (existEntity != null) {
            // 更新 - 使用 lambdaUpdate().set() 方法，只有值不同才更新
            LambdaUpdateChainWrapper<EsBeanMark> lambdaUpdateChainWrapper = this.lambdaUpdate();
            // 设置更新人信息
            esBeanMark.setUBy(userName);
            return this.updateWithLambdaSet(lambdaUpdateChainWrapper, existEntity, esBeanMark);
        }

        // 新增
        EsBeanMark entity = BeanUtil.copyProperties(esBeanMarkVO, EsBeanMark.class);
        entity.setEsBeanMarkId(IdUtil.getSnowflakeNextId());
        entity.setDelFlag(0);
        entity.setCreateBy(userName);
        entity.setCreateTime(date);
        entity.setUTime(date);
        entity.setUBy(userName);
        entity.setUserId(Convert.toStr(userId));
        entity.setDeptId(Convert.toStr(deptId));
        return this.save(entity);
    }

    /**
     * 删除EsBeanMark信息（物理删除）
     *
     * @param esBeanMarkVO 删除参数
     * @return 是否成功
     */
    public boolean deleteEsBeanMark(EsBeanMarkVO esBeanMarkVO) {
        String articleId = esBeanMarkVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }

        LambdaQueryWrapper<EsBeanMark> queryWrapper = new LambdaQueryWrapper<EsBeanMark>()
                .eq(EsBeanMark::getArticleId, articleId);

        return this.remove(queryWrapper);
    }

    /**
     * 使用 Lambda 表达式更新字段，只有值不同才更新
     *
     * @param lambdaUpdateChainWrapper Lambda 更新链式包装器
     * @param existEntity 现有实体
     * @param esBeanMark 新的实体数据
     * @return 是否成功更新
     */
    private boolean updateWithLambdaSet(LambdaUpdateChainWrapper<EsBeanMark> lambdaUpdateChainWrapper,
                                       EsBeanMark existEntity, EsBeanMark esBeanMark) {
        boolean hasUpdate = false;
        Date updateTime = new Date();

        // 设置查询条件
        lambdaUpdateChainWrapper.eq(EsBeanMark::getArticleId, existEntity.getArticleId())
                               .eq(EsBeanMark::getUserId, existEntity.getUserId());

        // 比较并更新各个字段，只有值不同才更新
        if (updateFieldIfDifferent(esBeanMark.getType(), existEntity.getType())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getType, esBeanMark.getType());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getTypeName(), existEntity.getTypeName())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getTypeName, esBeanMark.getTypeName());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getPublishTime(), existEntity.getPublishTime())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getPublishTime, esBeanMark.getPublishTime());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getTitle(), existEntity.getTitle())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getTitle, esBeanMark.getTitle());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getText(), existEntity.getText())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getText, esBeanMark.getText());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSummary(), existEntity.getSummary())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSummary, esBeanMark.getSummary());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getUrl(), existEntity.getUrl())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getUrl, esBeanMark.getUrl());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getHost(), existEntity.getHost())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getHost, esBeanMark.getHost());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getDomain(), existEntity.getDomain())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getDomain, esBeanMark.getDomain());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAuthor(), existEntity.getAuthor())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAuthor, esBeanMark.getAuthor());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAuthorId(), existEntity.getAuthorId())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAuthorId, esBeanMark.getAuthorId());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAuthorSex(), existEntity.getAuthorSex())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAuthorSex, esBeanMark.getAuthorSex());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getBizId(), existEntity.getBizId())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getBizId, esBeanMark.getBizId());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAccountLevel(), existEntity.getAccountLevel())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAccountLevel, esBeanMark.getAccountLevel());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSiteAreaCode(), existEntity.getSiteAreaCode())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSiteAreaCode, esBeanMark.getSiteAreaCode());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSiteAreaCodeName(), existEntity.getSiteAreaCodeName())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSiteAreaCodeName, esBeanMark.getSiteAreaCodeName());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getContentAreaCode(), existEntity.getContentAreaCode())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getContentAreaCode, esBeanMark.getContentAreaCode());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getContentAreaCodeName(), existEntity.getContentAreaCodeName())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getContentAreaCodeName, esBeanMark.getContentAreaCodeName());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSiteMeta(), existEntity.getSiteMeta())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSiteMeta, esBeanMark.getSiteMeta());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getContentMeta(), existEntity.getContentMeta())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getContentMeta, esBeanMark.getContentMeta());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getFansNum(), existEntity.getFansNum())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getFansNum, esBeanMark.getFansNum());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getReadNum(), existEntity.getReadNum())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getReadNum, esBeanMark.getReadNum());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getCommentNum(), existEntity.getCommentNum())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getCommentNum, esBeanMark.getCommentNum());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getLikeNum(), existEntity.getLikeNum())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getLikeNum, esBeanMark.getLikeNum());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getReprintNum(), existEntity.getReprintNum())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getReprintNum, esBeanMark.getReprintNum());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSector(), existEntity.getSector())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSector, esBeanMark.getSector());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getContentForm(), existEntity.getContentForm())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getContentForm, esBeanMark.getContentForm());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getMd5(), existEntity.getMd5())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getMd5, esBeanMark.getMd5());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getSrcCodePath(), existEntity.getSrcCodePath())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getSrcCodePath, esBeanMark.getSrcCodePath());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getCoverUrl(), existEntity.getCoverUrl())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getCoverUrl, esBeanMark.getCoverUrl());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getPicUrl(), existEntity.getPicUrl())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getPicUrl, esBeanMark.getPicUrl());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAvdUrl(), existEntity.getAvdUrl())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAvdUrl, esBeanMark.getAvdUrl());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getEmotionFlag(), existEntity.getEmotionFlag())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getEmotionFlag, esBeanMark.getEmotionFlag());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getIsOriginal(), existEntity.getIsOriginal())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getIsOriginal, esBeanMark.getIsOriginal());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getIsSpam(), existEntity.getIsSpam())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getIsSpam, esBeanMark.getIsSpam());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getDay(), existEntity.getDay())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getDay, esBeanMark.getDay());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getIsRead(), existEntity.getIsRead())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getIsRead, esBeanMark.getIsRead());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getHitWords(), existEntity.getHitWords())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getHitWords, esBeanMark.getHitWords());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getAccountGrade(), existEntity.getAccountGrade())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getAccountGrade, esBeanMark.getAccountGrade());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getUpdateTime(), existEntity.getUpdateTime())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getUpdateTime, esBeanMark.getUpdateTime());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getHitWord(), existEntity.getHitWord())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getHitWord, esBeanMark.getHitWord());
            hasUpdate = true;
        }

        if (updateFieldIfDifferent(esBeanMark.getPlanId(), existEntity.getPlanId())) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getPlanId, esBeanMark.getPlanId());
            hasUpdate = true;
        }

        // 如果有字段更新，设置更新时间和更新人
        if (hasUpdate) {
            lambdaUpdateChainWrapper.set(EsBeanMark::getUTime, updateTime);
            if (CharSequenceUtil.isNotBlank(esBeanMark.getUBy())) {
                lambdaUpdateChainWrapper.set(EsBeanMark::getUBy, esBeanMark.getUBy());
            }
            return lambdaUpdateChainWrapper.update();
        }

        return false;
    }

    /**
     * 判断字段是否需要更新（值不同且新值不为空）
     *
     * @param newValue 新值
     * @param currentValue 当前值
     * @return 是否需要更新
     */
    private boolean updateFieldIfDifferent(Object newValue, Object currentValue) {
        // 如果新值为空，则不更新
        if (newValue == null) {
            return false;
        }

        // 如果新值为空字符串，则不更新
        if (newValue instanceof String && CharSequenceUtil.isBlank((String) newValue)) {
            return false;
        }

        // 比较值是否不同
        return !Objects.equals(newValue, currentValue);
    }

    /**
     * 条件性设置字段值的辅助方法
     *
     * @param condition 是否需要更新的条件
     * @param chainWrapper Lambda 更新链式包装器
     * @param setter 字段设置器
     * @param value 要设置的值
     * @return 链式包装器（支持链式调用）
     */
    private <T> LambdaUpdateChainWrapper<EsBeanMark> setIf(boolean condition,
                                                          LambdaUpdateChainWrapper<EsBeanMark> chainWrapper,
                                                          com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, T> setter,
                                                          T value) {
        if (condition) {
            chainWrapper.set(setter, value);
        }
        return chainWrapper;
    }

}
