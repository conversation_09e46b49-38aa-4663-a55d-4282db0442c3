package com.boryou.web.module.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.domain.PlanSimpleReport;
import com.boryou.web.domain.vo.PlanSimpleReportVO;
import com.boryou.web.module.report.mapper.PlanSimpleReportMapper;
import com.boryou.web.module.report.service.PlanSimpleReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlanSimpleReportServiceImpl extends ServiceImpl<PlanSimpleReportMapper, PlanSimpleReport> implements PlanSimpleReportService {

    @Override
    public List<PlanSimpleReport> getList(PlanSimpleReportVO vo) {
        LambdaQueryWrapper<PlanSimpleReport> qw = new LambdaQueryWrapper<>();
        qw.eq(vo.getId() != null, PlanSimpleReport::getId, vo.getId())
                .eq(vo.getTempId() != null, PlanSimpleReport::getTempId, vo.getTempId())
                .eq(vo.getPlanId() != null, PlanSimpleReport::getPlanId, vo.getPlanId())
                .eq(vo.getReportType() != null, PlanSimpleReport::getReportType, vo.getReportType())
                .eq(vo.getStatus() != null, PlanSimpleReport::getStatus, vo.getStatus())
                .eq(vo.getSendStatus() != null, PlanSimpleReport::getSendStatus, vo.getSendStatus())
                .ge(vo.getStartTime() != null, PlanSimpleReport::getReportTime, vo.getStartTime())
                .le(vo.getEndTime() != null, PlanSimpleReport::getReportTime, vo.getEndTime());
        if (vo.getReportType() != null && vo.getReportType() == 1) {
            qw.like(StrUtil.isNotEmpty(vo.getReportDate()), PlanSimpleReport::getReportDate, vo.getReportDate());
        }
        if (vo.getReportType() != null && vo.getReportType() != 1) {
            qw.eq(StrUtil.isNotEmpty(vo.getReportDate()), PlanSimpleReport::getReportDate, vo.getReportDate());
        }
        return baseMapper.selectList(qw);
    }

    @Override
    public int insert(PlanSimpleReport planSimpleReport) {
        if (planSimpleReport.getPlanId() == null || planSimpleReport.getReportType() == null) {
            log.error("新增定时报告设置出错");
            return 0;
        }
        PlanSimpleReportVO vo = new PlanSimpleReportVO();
        vo.setReportType(planSimpleReport.getReportType());
        vo.setPlanId(planSimpleReport.getPlanId());
        if (CollUtil.isNotEmpty(getList(vo))) {
            log.error("新增定时报告设置出错");
            return 0;
        }
        return baseMapper.insert(planSimpleReport);
    }

    @Override
    public int update(PlanSimpleReport planSimpleReport) {
        if (planSimpleReport.getId() == null) {
            log.error("修改定时报告设置出错");
            return 0;
        }
        //一个id应该对应一组planId+reportType，所以不能修改
        planSimpleReport.setPlanId(null);
        planSimpleReport.setReportType(null);
        return baseMapper.updateById(planSimpleReport);
    }

}
