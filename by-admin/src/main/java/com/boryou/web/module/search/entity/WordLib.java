package com.boryou.web.module.search.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 关键词库
 *
 * <AUTHOR>
 */
@Data
public class WordLib {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关键词库类别
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long typeId;

    /**
     * 关键词组名称
     */
    private String name;

    /**
     * 关键词组内容
     */
    private String word;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String state;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;

}
