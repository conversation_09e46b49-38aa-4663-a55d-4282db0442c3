package com.boryou.web.module.notice.bo;

import com.boryou.web.module.notice.domain.ByNotice;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-24 15:34
 */
@Data
public class NoticeBO extends ByNotice {
    /**
     * id*
     */
    private String id;
    /**
     * 媒体类型*
     */
    private Integer type;
    /**
     * 媒体类型名称
     */
    private String typeName;
    /**
     * 发文时间*
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    /**
     * 标题*
     */
    private String title;
    /**
     * 真标题 不是从正文中截取的原文内容
     */
    private String realTitle;
    /**
     * 正文*
     */
    private String text;
    /**
     * 摘要，非es字段
     */
    private String summary;
    /**
     * 原文链接*
     */
    private String url;
    /**
     * host*
     */
    private String host;
    /**
     * (账号/作者)昵称*
     */
    private String author;
    /**
     * (账号/作者)id*
     */
    private String authorId;
    /**
     * 作者性别(0:女,1:男)*
     */
    private Integer authorSex;
    /**
     * 平台业务ID*
     */
    private String bizId;
    /**
     * 账号级别 (0达人  1蓝v  2红v  3橙v  4普通用户)*
     */
    private Integer accountLevel;
    /**
     * 命中关键词 (系统预警)
     */
    private List<String> hitWord;
}

