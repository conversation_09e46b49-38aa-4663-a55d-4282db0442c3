package com.boryou.web.module.collection.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.collection.vo.CollectionQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
public interface CollectionService extends IService<Collection> {
    /**
     * 加入收藏
     *
     * @param collection
     * @return
     */
    boolean add(Collection collection);

    /**
     * 素材列表
     *
     * @param query
     * @return
     */
    List<Collection> collectionList(CollectionQuery query);

}
