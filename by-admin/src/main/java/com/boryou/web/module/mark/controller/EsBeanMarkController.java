package com.boryou.web.module.mark.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.service.EsBeanMarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/esBeanMark")
public class EsBeanMarkController extends BaseController {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    /**
     * 根据文章ID获取EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return EsBeanMark信息
     */
    @PostMapping("/get")
    public AjaxResult getEsBeanMark(@RequestBody EsBeanMarkVO esBeanMarkVO) {
        EsBeanMarkVO result = esBeanMarkService.getEsBeanMarkByArticleId(esBeanMarkVO);
        return AjaxResult.success(result);
    }

    /**
     * 分页查询EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return 分页结果
     */
    @PostMapping("/list")
    public TableDataInfo getEsBeanMarkList(@RequestBody EsBeanMarkVO esBeanMarkVO) {
        startPage();
        IPage<EsBeanMarkVO> page = esBeanMarkService.getEsBeanMarkPage(esBeanMarkVO);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 添加或更新EsBeanMark信息
     *
     * @param esBeanMarkVO EsBeanMark信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public AjaxResult saveEsBeanMark(@RequestBody @Validated EsBeanMarkVO esBeanMarkVO) {
        boolean result = esBeanMarkService.saveOrUpdateEsBeanMark(esBeanMarkVO);
        return toAjax(result);
    }

    /**
     * 根据EsBeanMark保存信息
     *
     * @param esBeanMark ES数据
     * @return 操作结果
     */
    @PostMapping("/saveFromEs")
    public AjaxResult saveFromEsBeanMark(@RequestBody EsBeanMark esBeanMark) {
        boolean result = esBeanMarkService.saveFromEsBeanMark(esBeanMark);
        return toAjax(result);
    }

    /**
     * 批量保存EsBeanMark信息
     *
     * @param esBeanMarkList ES数据列表
     * @return 操作结果
     */
    @PostMapping("/saveBatch")
    public AjaxResult saveBatchFromEsBeanMark(@RequestBody List<EsBeanMark> esBeanMarkList) {
        boolean result = esBeanMarkService.saveBatchFromEsBeanMark(esBeanMarkList);
        return toAjax(result);
    }

    /**
     * 删除EsBeanMark信息
     *
     * @param esBeanMarkVO 删除参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public AjaxResult deleteEsBeanMark(@RequestBody EsBeanMarkVO esBeanMarkVO) {
        boolean result = esBeanMarkService.deleteEsBeanMark(esBeanMarkVO);
        return toAjax(result);
    }
}
