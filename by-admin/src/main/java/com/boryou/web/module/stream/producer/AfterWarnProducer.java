package com.boryou.web.module.stream.producer;

import cn.hutool.json.JSONUtil;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.search.entity.vo.WarnAfterVO;
import com.boryou.web.module.stream.util.RedisStreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
@RequiredArgsConstructor
@Slf4j
public class AfterWarnProducer {
    private final RedisStreamUtil redisStreamUtil;

    public void sendMsg(WarnAfterVO warnAfterVO) {
        Map<String, Object> message = new HashMap<>(1);
        message.put("b", JSONUtil.toJsonStr(warnAfterVO));
        //message.put("t", DateUtil.now());
        //stream的key值，对应application.yml中配置的
        String streamKey = RedisConstant.system_prefix + "AFTER_WARN";
        redisStreamUtil.addMap(streamKey, message);
    }

}
