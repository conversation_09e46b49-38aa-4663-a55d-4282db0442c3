package com.boryou.web.module.account.service;

import com.boryou.web.module.account.entity.vo.AccountInfo;
import com.boryou.web.module.account.entity.vo.JumpLinkVO;
import com.boryou.web.module.home.entity.Lawyer;

/**
 * <AUTHOR>
 * @date 2024-07-02 15:50
 */
public interface AccountService {

    AccountInfo getAccountInfo(AccountInfo accountInfo);

    String jumpLink(JumpLinkVO jumpLinkVO);

    String getAvatar(Lawyer lawyer);
}

