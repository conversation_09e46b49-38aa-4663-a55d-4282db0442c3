package com.boryou.web.module.drill.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;


@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DrillProcessRes {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    private String taskTitle;

    private String status;

    private String taskContent;

    private String drillEvent;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimateDrillTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillEndTime;

    private SysUserSimpleVO blueCaptainUser;
    private List<SysUserSimpleVO> blueMemberUser;
    private SysUserSimpleVO redCaptainUser;
    private List<SysUserSimpleVO> redMemberUser;

    /**
     * 角色信息 红队队长: redCap 红队队员: redMem 蓝队队长: blueCap 蓝队队员: blueMem 主持人: moderator
     */
    private String roleInfo;

    /**
     * 上一阶段
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long previousStageId;

    /**
     * 当前阶段
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long currentStageId;

    /**
     * 下一阶段
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nextStageId;

    /**
     * 各个阶段
     */
    private List<DrillProcessStageRes> drillProcessStageRes;

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DrillProcessStageRes {
        /**
         * 阶段ID
         */
        @TableId(value = "stage_id", type = IdType.INPUT)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long processStageId;

        /**
         * 关联演练任务ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long drillTaskId;

        /**
         * 阶段名称
         */
        private String stageName;

        /**
         * 阶段顺序
         */
        private Integer stageOrder;

        /**
         * 计时类型（1:同时计时 2:分开计时）
         */
        private Integer timerType;

        /**
         * 倒计时时长(秒)
         */
        private Integer timerDuration;

        /**
         * 红队倒计时时长(秒)
         */
        private Integer redTimerDuration;

        /**
         * 红队倒计时剩余(秒)
         */
        private Integer redRemainTimerDuration;

        /**
         * 蓝队倒计时时长(秒)
         */
        private Integer blueTimerDuration;

        /**
         * 蓝队倒计时剩余(秒)
         */
        private Integer blueRemainTimerDuration;

        /**
         * 阶段状态 1:评论 2:总结
         */
        private Integer stageType;

        /**
         * 阶段状态 1:未开始 2:进行中 3:已结束
         */
        private Integer stageStatus;

        /**
         * 阶段评论
         */
        private List<DrillCommentRes> drillCommentResList;

        private List<DrillStageScoreRes> redScore;

        private List<DrillStageScoreRes> blueScore;

        private String redTotalScore;

        private String blueTotalScore;

        /**
         * 阶段得分 1:不显示得分 2:显示得分
         */
        private Integer scoreType;

        /**
         * 红队阶段得分 例如红队发表40个评论蓝队发表60个评论
         * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
         */
        private String redStageScore;

        /**
         * 蓝队阶段得分 例如红队发表40个评论蓝队发表60个评论
         * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
         */
        private String blueStageScore;

        @Data
        @SuperBuilder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class DrillCommentRes {

            /**
             * 评论ID
             */
            @TableId(value = "comment_id", type = IdType.INPUT)
            @JsonSerialize(using = ToStringSerializer.class)
            private Long commentId;

            /**
             * 评论属于的指令
             */
            @JsonSerialize(using = ToStringSerializer.class)
            private Long parentCommentId;

            /**
             * 指令排序(指令一,指令二,只有指令有)
             */
            private String commentOrder;

            /**
             * 演练任务ID
             */
            @JsonSerialize(using = ToStringSerializer.class)
            private Long drillTaskId;

            /**
             * 所属阶段ID
             */
            @JsonSerialize(using = ToStringSerializer.class)
            private Long stageId;

            @JsonSerialize(using = ToStringSerializer.class)
            private Long scoreProcessStageId;

            /**
             * 队伍类型（1:红队 2:蓝队）
             */
            private Integer teamType;

            /**
             * 评论类型
             */
            private String commentType;

            /**
             * 评论内容
             */
            private String content;

            /**
             * 热搜榜排名
             */
            private String rank;

            /**
             * 热度值
             */
            private String hotNum;

            /**
             * 热搜文章类型
             */
            private String rankType;

            @TableField(typeHandler = ListStringTypeHandler.class)
            private List<String> file;

            /**
             * 发布人ID
             */
            private Long userId;

            private String nickName;

            private String userName;

            private String avatar;

            private String stageName;

            private String scoreStageName;

            /**
             * 角色类型
             */
            private String roleInfo;

            /**
             * 是否队长发布（0:否 1:是）
             */
            private Boolean isCaptain;

            /**
             * 创建时间
             */
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
            private Date createTime;

            private String score;

            private Boolean isLike;

            private Integer likeCount = 0;

            private Integer relatedCommentType;

            private List<DrillCommentReplyRes> drillCommentReplyRes;
        }

    }

}
