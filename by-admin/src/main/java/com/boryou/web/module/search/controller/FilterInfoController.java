package com.boryou.web.module.search.controller;

import cn.hutool.core.collection.CollUtil;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.web.domain.Plan;
import com.boryou.web.module.search.entity.FilterInfo;
import com.boryou.web.module.search.service.FilterInfoService;
import com.boryou.web.service.PlanService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController()
public class FilterInfoController extends BaseController {

    @Resource
    private FilterInfoService filterInfoService;
    @Resource
    private PlanService planService;

    @GetMapping("/info/selectFilterInfoPage")
    public TableDataInfo selectFilterInfoPage(@ModelAttribute FilterInfo filterInfo) {
        if (filterInfo.getPlanId() == null) {
            List<Plan> plans = planService.selectByPlanList(new Plan());
            String planIds = CollUtil.join(plans.stream().map(Plan::getPlanId).collect(Collectors.toList()), ",");
            filterInfo.setPlanIds(planIds);
        }
        startPage();
        return getDataTable(filterInfoService.selectFilterInfo(filterInfo));
    }

    @Log(title = "过滤搜索单条信息", businessType = BusinessType.INSERT)
    @PostMapping("/info/insertFilterInfo")
    public AjaxResult insertFilterInfo(@RequestBody FilterInfo filterInfo) {
        if (filterInfoService.insertFilterInfo(filterInfo) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/info/deleteFilterInfo")
    public AjaxResult deleteFilterInfo(@RequestBody FilterInfo filterInfo) {
        if (CollUtil.isNotEmpty(filterInfo.getIds())) {
            if (filterInfoService.deleteFilterInfoByIds(CollUtil.join(filterInfo.getIds(), ",")) > 0) {
                return AjaxResult.success();
            }
        } else {
            if (filterInfo.getSize() != null && filterInfo.getSize() == 0) {
                filterInfo.setSize(null);
            } else {
                filterInfo.setPageNum((filterInfo.getPageNum() - 1) * filterInfo.getSize());
            }
            if (filterInfoService.deleteFilterInfoBySize(filterInfo) > 0) {
                return AjaxResult.success();
            }
        }
        return AjaxResult.error();
    }
}
