package com.boryou.web.module.report.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReportTemplate {

    /**
     * 模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tempId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 用户
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 报告导读
     */
    private Integer reportIntro = 0;

    /**
     * 处置建议
     */
    private Integer suggest = 0;

    /**
     * 监测概述
     */
    private Integer overview = 0;
    /**
     * 媒体来源统计
     */
    private Integer mediaStatistics = 0;

    /**
     * 情感分析
     */
    private Integer emotionAnalysis = 0;

    /**
     * 媒体来源明细
     */
    private Integer mediaDetails = 0;

    /**
     * 字符云
     */
    private Integer charCloud = 0;

    /**
     * 主要舆情
     */
    private Integer mainInfo = 0;

    /**
     * 舆情导读
     */
    private Integer infoIntro = 0;

    /**
     * 媒体信息走势
     */
    private Integer mediaTrendChart = 0;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 是否默认 0否 1是
     */
    private Integer isDefault = 0;

    private List<String> inputComponents;

}
