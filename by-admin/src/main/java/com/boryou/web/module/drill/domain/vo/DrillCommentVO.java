package com.boryou.web.module.drill.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 演练评论记录实体类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillCommentVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentId;

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "任务id不能为空")
    private Long drillTaskId;

    /**
     * 所属阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "所属阶段id不能为空")
    private Long processStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long scoreProcessStageId;

    /**
     * 评论属于的指令
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommentId;

    /**
     * 指令排序(指令一,指令二,只有指令有)
     */
    private String commentOrder;

    /**
     * 队伍类型（1:红队 2:蓝队）
     */
    private Integer teamType;

    /**
     * 评论类型
     */
    @NotBlank(message = "评论类型不能为空")
    private String commentType;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 热搜榜排名
     */
    private String rank;

    /**
     * 热度值
     */
    private String hotNum;

    /**
     * 热搜文章类型
     */
    private String rankType;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> file;

    /**
     * 发布人ID
     */
    private Long userId;

    /**
     * 角色类型
     */
    @NotBlank(message = "角色类型不能为空")
    private String roleInfo;

    /**
     * 是否队长发布（0:否 1:是）
     */
    private Boolean isCaptain;

    /**
     * 得分
     */
    private String score;

    private Integer likeCount;

    private Integer relatedCommentType;

}
