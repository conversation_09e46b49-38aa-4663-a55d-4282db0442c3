package com.boryou.web.module.drill.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.drill.domain.DrillStageTime;
import com.boryou.web.module.drill.mapper.DrillStageTimeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillStageTimeService extends ServiceImpl<DrillStageTimeMapper, DrillStageTime> {

    public void updateRemainTimerDuration(Integer remainTimerDurationFinal, Long drillStageTimeId) {
        if (drillStageTimeId == null) {
            return;
        }
        if (remainTimerDurationFinal < 0) {
            remainTimerDurationFinal = 0;
        }
        this.lambdaUpdate().set(DrillStageTime::getRemainTimerDuration, remainTimerDurationFinal)
                .eq(DrillStageTime::getDrillStageTimeId, drillStageTimeId)
                .update();
    }

}
