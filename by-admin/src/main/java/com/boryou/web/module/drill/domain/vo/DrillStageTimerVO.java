package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 倒计时记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillStageTimerVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillStageTimeId;

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "演练任务id不能为空")
    private Long drillTaskId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "阶段id不能为空")
    private Long processStageId;

    /**
     * 队伍类型（1:红队 2:蓝队）
     */
    private Integer teamType;

    @NotNull(message = "计时类型不能为空")
    private Integer timerType;

    /**
     * 剩余秒数
     */
    private Integer remainTimerDuration;

    /**
     * 倒计时时长
     */
    @NotNull(message = "倒计时时长不能为空")
    private Integer timerDuration;

}
