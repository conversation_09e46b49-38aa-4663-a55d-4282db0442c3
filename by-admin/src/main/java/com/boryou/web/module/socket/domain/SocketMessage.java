package com.boryou.web.module.socket.domain;

import com.boryou.web.module.socket.enums.MessageTransferScopeEnum;
import com.boryou.web.module.socket.enums.SocketChannelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/6 17:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SocketMessage {

    @Schema(description = "data")
    protected Object data;

    @Schema(description = "通道类型")
    protected SocketChannelEnum channel = SocketChannelEnum.DEFAULTS;

    @Schema(description = "消息通知作用域")
    protected MessageTransferScopeEnum scope = MessageTransferScopeEnum.SOCKET_CLIENT;

}
