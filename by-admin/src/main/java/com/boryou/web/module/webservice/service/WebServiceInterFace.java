package com.boryou.web.module.webservice.service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;

@WebService(name = "Webservice",targetNamespace = "http://bjgoodwillcis.com/")
public interface WebServiceInterFace {
	
	@WebMethod
    String synUserInfo(@WebParam(name = "ParamXml") String ParamXml);

    @WebMethod
    String synOrgInfo(@WebParam(name = "ParamXml") String ParamXml);

    @WebMethod
    String synDeptInfo(@WebParam(name = "ParamXml") String ParamXml);
}
