package com.boryou.web.module.source.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.source.entity.SourceSetting;
import com.boryou.web.module.source.vo.SourceSettingVO;

import java.util.List;

/**
 * 信源设置业务层
 *
 * <AUTHOR>
 * @date 2021-09-01 15:19:06
 */
public interface SourceSettingService extends IService<SourceSetting> {

    /**
     * 查询信源列表
     *
     * @param id
     * @param name
     * @param plateId
     * @param state
     * @param settingType
     * @param userId
     * @param sourceType
     * @param page
     * @return java.util.List<com.boryou.yuqing.entity.SourceSetting>
     * <AUTHOR>
     * @date 2021-09-02 17:53:44
     */
    List<SourceSetting> getSourceSettingList(SourceSettingVO source);

    /**
     * 根据条件获得信源设置
     *
     * @param id
     * @param name
     * @param plateId
     * @param state
     * @param userId
     * @return java.util.List<com.boryou.yuqing.entity.SourceSetting>
     * <AUTHOR>
     * @date 2021-09-01 17:41:05
     */
    List<SourceSetting> getSourceSetting(String id, String name, Long plateId, String state, String settingType, String userId, String sourceType);

    /**
     * 添加信源设置
     *
     * @param sourceSetting
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 15:24:25
     */
    void addSourceSetting(SourceSetting sourceSetting);

    /**
     * 更新信源设置
     *
     * @param sourceSetting
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 15:26:24
     */
    void updateSourceSetting(SourceSetting sourceSetting);

    /**
     * 更新信源设置状态
     *
     * @param state
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 15:24:46
     */
    void updateSourceSettingState(int state, String ids);

    /**
     * 根据主键id删除信源设置
     *
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 15:25:41
     */
    void delSourceSetting(String id);

    /**
     * 根据板块id删除信源设置
     *
     * @param plateId
     * @return int
     * <AUTHOR>
     * @date 2021-09-01 15:25:50
     */
    int deleteSourceSetting(Long plateId, String state, String sourceType);


    boolean addSource(List<SourceSetting> sourceSetting);

    boolean delete(String ids);

    String importSource(List<SourceSetting> userList, String settingType, String operName, Long plateId);
}
