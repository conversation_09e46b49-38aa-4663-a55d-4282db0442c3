package com.boryou.web.module.submit;

import com.boryou.common.annotation.Log;
import com.boryou.common.annotation.Secret;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.exception.CustomException;
import com.boryou.domain.SFile;
import com.boryou.manage.domain.File;
import com.boryou.submit.domain.SubmitInfo;
import com.boryou.submit.dto.ApprovalDTO;
import com.boryou.submit.dto.SubmitInfoAddDTO;
import com.boryou.submit.dto.SubmitInfoDTO;
import com.boryou.submit.service.ISubmitFileRelationService;
import com.boryou.submit.service.ISubmitInfoService;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysRoleService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.feign.SingleLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 信息报送Controller
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/submit")
public class SubmitInfoController extends BaseController {

    private final ISubmitInfoService submitInfoService;
    private final ISysDeptService deptService;
    private final ISubmitFileRelationService fileRelationService;
    private final ISysRoleService roleService;
    private final SingleLoginService singleLoginService;
    private final ISysUserService userService;

    /**
     * 查询信息报送列表
     */
    @PostMapping("/list")
    @Secret(value = SubmitInfoDTO.class)
    public TableDataInfo list(@RequestBody SubmitInfoDTO submitInfo) {
        startPage();
        List<SubmitInfo> list = submitInfoService.selectSubmitInfoList(submitInfo);
        return getDataTable(list);
    }

    /**
     * 任务完成状态，用作综合管理展示
     */
    @GetMapping("/taskNum")
    public AjaxResult taskNum(String token) {
        // 校验 token
        AjaxResult result = singleLoginService.verifyToken(token, 7);
        String userId;
        if (result.getCode() != 200) {
            throw new CustomException("token 校验失败");
        } else {
            userId = result.get("userId").toString();
        }
        SysUser user = userService.selectUserById(Long.valueOf(userId));
        SubmitInfoDTO submitInfo = new SubmitInfoDTO();
        // 待处理
        submitInfo.setGlState(1);
        List<SubmitInfo> submitInfo1 = submitInfoService.taskNum(submitInfo, user.getUserId());
        // 已完成
        submitInfo.setGlState(4);
        List<SubmitInfo> submitInfo2 = submitInfoService.taskNum(submitInfo, user.getUserId());
        Map<String, Object> res = new HashMap<>();
        res.put("pending", submitInfo1.size());
        res.put("completed", submitInfo2.size());
        res.put("name", "安全传输任务");
        return AjaxResult.success(res);
    }

    /**
     * 导出信息报送列表
     */
    @Log(title = "信息报送", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Secret(value = SubmitInfoDTO.class)
    public AjaxResult export(@RequestBody SubmitInfoDTO submitInfo) {
        return submitInfoService.export(submitInfo);
    }

    /**
     * 获取信息报送详细信息
     */
    @GetMapping(value = "/{id}")
    @Secret(value = Long.class)
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        HashMap<Object, Object> resMap = new HashMap<>();
        SubmitInfo data = submitInfoService.selectSubmitInfoById(id);
        resMap.put("info", data);
        List<SFile> files = fileRelationService.getFilesList(id.toString());
        resMap.put("files", files);
        return AjaxResult.success(resMap);
    }


    /**
     * 新增信息报送
     */
    @Log(title = "信息报送", businessType = BusinessType.INSERT)
    @PostMapping
    @Secret(value = SubmitInfoAddDTO.class)
    public AjaxResult add(@RequestBody SubmitInfoAddDTO submitInfo) {
        return toAjax(submitInfoService.insertSubmitInfo(submitInfo));
    }

    /**
     * 修改信息报送
     */
    @Log(title = "信息报送", businessType = BusinessType.UPDATE)
    @PutMapping
    @Secret(value = SubmitInfoAddDTO.class)
    public AjaxResult edit(@RequestBody SubmitInfoAddDTO submitInfo) {
        return toAjax(submitInfoService.updateSubmitInfo(submitInfo));
    }

    /***
     *  审核操作（1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结）
     *  @param approvalDTO  传参对象
     * <AUTHOR>
     * @date 2024/1/25 17:15
     * @return com.boryou.common.core.domain.AjaxResult
     **/
    @PostMapping("/approvalSubmit")
    @Secret(value = ApprovalDTO.class)
    public AjaxResult approvalSubmit(@RequestBody ApprovalDTO approvalDTO) {
        boolean b = submitInfoService.approvalSubmit(approvalDTO);
        return AjaxResult.success(b);
    }

    /**
     * 管理人员选择管理人员树接口
     * 1.获取当前管理人员id  2.获取所有管理人员的大部门集合  3.根据部门集合加载下面的管理人员列表
     */
    @Secret(value = Object.class)
    @GetMapping("/loadApprover")
    public AjaxResult loadApprover() {
        List<TreeSelect> tree = submitInfoService.loadApprover();
        return AjaxResult.success(tree);
    }


    /**
     * 分析师选择管理者人员列表
     * 1.获取当前登录人所管辖的公司
     * 2.在获取这个公司的部门id ，再构建这个部门，及下面的管理人员
     */
    @GetMapping("/loadManager")
    @Secret(value = Object.class)
    public AjaxResult loadManager() {
        List<TreeSelect> trees = submitInfoService.loadManager();
        return AjaxResult.success(trees);
    }


    /**
     * 加载处置者人员    管理者和处置者专用接口
     */
    @Secret(value = Object.class)
    @GetMapping("/loadProcessor")
    public AjaxResult loadProcessor() {
        SubmitInfo submitInfo = new SubmitInfo();
        List<TreeSelect> tree = submitInfoService.loadProcessor(submitInfo);
        return AjaxResult.success(tree);
    }

    /**
     * 报送列表页面-接收人下拉框列表（包含分析师名称和管理人员）
     */
    @Secret(value = Object.class)
    @GetMapping("/loadReceiver")
    public AjaxResult loadSubmitUsers() {
        List<LinkedHashMap<String, String>> tree = submitInfoService.loadSubmitUsers();
        return AjaxResult.success(tree);
    }


    /**
     * 获取当前用户类型（管理、分析师、处置员）
     *
     * @param
     * @return com.boryou.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2024/1/17 13:37
     **/
    @Secret(value = Object.class)
    @GetMapping("/getUserType")
    public AjaxResult getUserRole() {
        Integer tree = submitInfoService.getUserRole();
        return AjaxResult.success(tree);
    }

    /**
     * 给分析师选择服务公司的部门（仅一级树）
     */
//    @Secret(value = Object.class)
    @GetMapping("/loadManagerDept")
    public AjaxResult loadManagerDept() {
        List<TreeSelect> tree = submitInfoService.loadManagerDept();
        return AjaxResult.success(tree);
    }

    /**
     * 用于超时测试
     *
     * @param id
     * @param deadline
     * @param deadlineNum
     * @return com.boryou.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2024/1/25 17:14
     **/
    @GetMapping("/chaoshi")
    public AjaxResult chaoshi(String id, String deadline, String deadlineNum) {
        submitInfoService.chaoshi(id, deadline, deadlineNum);
        return AjaxResult.success();
    }

    /**
     * 删除信息报送
     */
    @Log(title = "信息报送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(submitInfoService.deleteSubmitInfoByIds(ids));
    }
}
