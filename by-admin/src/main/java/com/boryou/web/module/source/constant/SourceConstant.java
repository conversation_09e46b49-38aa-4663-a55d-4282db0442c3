package com.boryou.web.module.source.constant;

import cn.hutool.core.collection.CollUtil;
import com.boryou.web.controller.common.enums.MediaTypeEnum;

import java.util.List;

public class SourceConstant {
    // 使用type + host:
    // 新闻 电子报刊 政务 广播 电视 海外
    public static final List<String> TYPE_HOST = CollUtil.newArrayList(
            String.valueOf(MediaTypeEnum.NEWS.getValue()),
            String.valueOf(MediaTypeEnum.EPAPER.getValue()),
            String.valueOf(MediaTypeEnum.GOV.getValue()),
            String.valueOf(MediaTypeEnum.OVERSEAS.getValue())
    );
    // 贴吧特殊处理
    public static final List<String> TYPE_HOST_SECTOR = CollUtil.newArrayList(
            "tieba.baidu.com", "ent.tieba.com", "m.tieba.com", "www.tieba.com"
    );

    public static final List<String> TYPE_HOST_AUTHOR = CollUtil.newArrayList(
            String.valueOf(MediaTypeEnum.CLIENT.getValue()),
            String.valueOf(MediaTypeEnum.VIDEO.getValue()),
            String.valueOf(MediaTypeEnum.FORUM.getValue()),
            String.valueOf(MediaTypeEnum.AUDIO.getValue()),
            String.valueOf(MediaTypeEnum.TV.getValue())
    );

    public static final List<String> TYPE_AUTHOR = CollUtil.newArrayList(
            String.valueOf(MediaTypeEnum.WECHAT.getValue()),
            String.valueOf(MediaTypeEnum.WEIBO.getValue())
    );

}
