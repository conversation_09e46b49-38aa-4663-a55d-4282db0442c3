package com.boryou.web.module.warn.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.domain.PushContacts;
import com.boryou.web.module.warn.domain.WarnSet;
import com.boryou.web.module.warn.domain.vo.ContactUserVO;
import com.boryou.web.module.warn.domain.vo.ContactVO;
import com.boryou.web.module.warn.domain.vo.WarnSetVO;
import com.boryou.web.module.warn.mapper.WarnSetMapper;
import com.boryou.web.service.IPushContactsService;
import com.boryou.web.service.PlanService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class WarnSetService extends ServiceImpl<WarnSetMapper, WarnSet> {
    @Resource
    private IPushContactsService pushContactsService;
    @Resource
    private PlanService planService;
    @Resource
    private ISysUserService userService;

    public WarnSetVO warnSetGet(WarnSetVO warnSetVO) {
        Long planId = warnSetVO.getPlanId();
        if (planId == null) {
            throw new CustomException("方案Id不能为空");
        }
        LambdaQueryWrapper<WarnSet> eq = new LambdaQueryWrapper<WarnSet>().eq(WarnSet::getPlanId, planId);
        WarnSet one = this.getOne(eq);
        if (one == null) {
            return null;
        }
        WarnSetVO warnSetVOCopy = BeanUtil.copyProperties(one, WarnSetVO.class);
        this.buildReceiveTime(warnSetVOCopy);
        this.buildContact(warnSetVOCopy);

        return warnSetVOCopy;
    }

    public List<WarnSetVO> getAllOpenWarnSet(List<Long> planIdList) {
        List<Long> userIds = userService.selectRealUserIds();
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        List<WarnSet> warnSetList = this.list(new LambdaQueryWrapper<WarnSet>()
                .eq(WarnSet::getStatus, 1)
                .in(WarnSet::getPlanId, planIdList)
                .in(WarnSet::getUserId, userIds)
                .orderByDesc(WarnSet::getWarningType));
        if (CollUtil.isEmpty(warnSetList)) {
            return Collections.emptyList();
        }
        Map<Long, PushContacts> pushContactsMap = buildContactMapForBatch(warnSetList);
        List<WarnSetVO> warnSetVOList = new ArrayList<>();
        for (WarnSet warnSet : warnSetList) {
            WarnSetVO warnSetVOCopy = BeanUtil.copyProperties(warnSet, WarnSetVO.class);
            this.buildReceiveTime(warnSetVOCopy);
            this.buildBatchContact(warnSetVOCopy, pushContactsMap);
            warnSetVOList.add(warnSetVOCopy);
        }
        return warnSetVOList;
    }

    private Map<Long, PushContacts> buildContactMapForBatch(List<WarnSet> warnSetList) {
        List<Long> contactIds = new ArrayList<>();
        for (WarnSet warnSet : warnSetList) {
            List<ContactVO> mailUser = warnSet.getMailUser();
            List<ContactVO> messageUser = warnSet.getMessageUser();
            List<ContactVO> chatUser = warnSet.getChatUser();
            this.getContactId(mailUser, contactIds);
            this.getContactId(messageUser, contactIds);
            this.getContactId(chatUser, contactIds);
        }
        Map<Long, PushContacts> pushContactsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(contactIds)) {
            pushContactsMap = pushContactsService.selectAllPushContactsMapByIds(contactIds);
        }
        return pushContactsMap;
    }


    private void buildReceiveTime(WarnSetVO warnSetVOCopy) {
        Integer startTime = warnSetVOCopy.getStartTime();
        if (startTime != null) {
            if (startTime == 24) {
                warnSetVOCopy.setStartReceiveTime("24:00");
            } else {
                warnSetVOCopy.setStartReceiveTime(DateUtil.format(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.date()), startTime), "HH:mm"));
            }
        }
        Integer endTime = warnSetVOCopy.getEndTime();
        if (endTime != null) {
            if (endTime == 24) {
                warnSetVOCopy.setEndReceiveTime("24:00");
            } else {
                warnSetVOCopy.setEndReceiveTime(DateUtil.format(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.date()), endTime), "HH:mm"));
            }

        }
    }

    private void buildContact(WarnSetVO warnSetVOCopy) {
        List<ContactVO> mailUser = warnSetVOCopy.getMailUser();
        List<ContactVO> messageUser = warnSetVOCopy.getMessageUser();
        List<ContactVO> chatUser = warnSetVOCopy.getChatUser();
        List<Long> contactIds = new ArrayList<>();
        this.getContactId(mailUser, contactIds);
        this.getContactId(messageUser, contactIds);
        this.getContactId(chatUser, contactIds);
        Map<Long, PushContacts> pushContactsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(contactIds)) {
            pushContactsMap = pushContactsService.selectPushContactsMapByIds(contactIds);
        }
        warnSetVOCopy.setMailContact(this.getContact(mailUser, pushContactsMap));
        warnSetVOCopy.setMessageContact(this.getContact(messageUser, pushContactsMap));
        warnSetVOCopy.setChatContact(this.getContact(chatUser, pushContactsMap));
    }

    private void buildBatchContact(WarnSetVO warnSetVOCopy, Map<Long, PushContacts> pushContactsMap) {
        List<ContactVO> mailUser = warnSetVOCopy.getMailUser();
        List<ContactVO> messageUser = warnSetVOCopy.getMessageUser();
        List<ContactVO> chatUser = warnSetVOCopy.getChatUser();
        warnSetVOCopy.setMailContact(this.getContact(mailUser, pushContactsMap));
        warnSetVOCopy.setMessageContact(this.getContact(messageUser, pushContactsMap));
        warnSetVOCopy.setChatContact(this.getContact(chatUser, pushContactsMap));
    }

    private List<ContactUserVO> getContact(List<ContactVO> mailUser, Map<Long, PushContacts> pushContactsMap) {
        if (CollUtil.isEmpty(mailUser)) {
            return null;
        }

        List<ContactUserVO> mailContact = new ArrayList<>();
        for (ContactVO contactVO : mailUser) {
            Long id = contactVO.getId();
            if (!pushContactsMap.containsKey(id)) {
                continue;
            }
            PushContacts pushContacts = pushContactsMap.get(id);
            String username = pushContacts.getUsername();
            String phone = pushContacts.getPhone();
            String email = pushContacts.getEmail();
            String wxUserName = pushContacts.getWxUserName();
            String wxOpenId = pushContacts.getWxOpenId();
            String headImgUrl = pushContacts.getHeadImgUrl();
            ContactUserVO contactUserVO = new ContactUserVO();
            contactUserVO.setId(id);
            List<String> area = contactVO.getArea();
            contactUserVO.setArea(area);
            contactUserVO.setUsername(username);
            contactUserVO.setPhone(phone);
            contactUserVO.setEmail(email);
            contactUserVO.setWxOpenId(wxOpenId);
            contactUserVO.setHeadImgUrl(headImgUrl);
            contactUserVO.setWxUserName(wxUserName);
            mailContact.add(contactUserVO);
        }
        return mailContact;
    }

    public void getContactId(List<ContactVO> mailUser, List<Long> contactIds) {
        if (CollUtil.isNotEmpty(mailUser)) {
            //mailUser = JSONUtil.toList(JSONUtil.toJsonStr(mailUser), ContactVO.class);
            mailUser.forEach(item -> {
                Long id = item.getId();
                contactIds.add(id);
            });
        }
    }

    public boolean warnSetChange(WarnSetVO warnSetVO) {
        Long id = warnSetVO.getId();
        Long planId = warnSetVO.getPlanId();
        WarnSet one = this.getOne(new LambdaQueryWrapper<WarnSet>().eq(WarnSet::getPlanId, planId));

        String startReceiveTime = warnSetVO.getStartReceiveTime();
        int startHour = 0;
        if (CharSequenceUtil.equals(startReceiveTime, "24:00")) {
            startHour = 24;
        } else {
            DateTime startParse = DateUtil.parse(startReceiveTime, "HH:mm");
            startHour = startParse.hour(true);
        }
        warnSetVO.setStartTime(startHour);
        String endReceiveTime = warnSetVO.getEndReceiveTime();
        int endHour = 0;
        if (CharSequenceUtil.equals(endReceiveTime, "24:00")) {
            endHour = 24;
        } else {
            DateTime endParse = DateUtil.parse(endReceiveTime, "HH:mm");
            endHour = endParse.hour(true);
        }
        warnSetVO.setEndTime(endHour);

        List<ContactUserVO> mailContact = warnSetVO.getMailContact();
        List<ContactUserVO> messageContact = warnSetVO.getMessageContact();
        List<ContactUserVO> chatContact = warnSetVO.getChatContact();
        warnSetVO.setMailUser(getContact(mailContact));
        warnSetVO.setMessageUser(getContact(messageContact));
        warnSetVO.setChatUser(getContact(chatContact));

        if (one == null) {
            //新增
            SysUser sysUser = SecurityUtils.getLoginUser().getUser();
            Long userId = sysUser.getUserId();
            Long deptId = sysUser.getDeptId();
            warnSetVO.setUserId(userId);
            warnSetVO.setDeptId(deptId);
            return this.save(warnSetVO);
        }
        Long idSql = one.getId();
        if (id == null) {
            warnSetVO.setId(idSql);
        } else {
            if (!Objects.equals(id, idSql)) {
                throw new CustomException("已存在设置");
            }
        }

        return this.updateById(warnSetVO);
    }

    private static List<ContactVO> getContact(List<ContactUserVO> mailContact) {
        if (CollUtil.isEmpty(mailContact)) {
            return null;
        }
        List<ContactVO> mailUser = new ArrayList<>();
        for (ContactUserVO contactUserVO : mailContact) {
            Long contactId = contactUserVO.getId();
            List<String> area = contactUserVO.getArea();
            ContactVO contactVO = new ContactVO();
            contactVO.setId(contactId);
            contactVO.setArea(area);
            mailUser.add(contactVO);
        }
        return mailUser;
    }

    public boolean updateLastPushTime(Long id, DateTime date) {
        if (id == null && date == null) {
            return false;
        }

        return this.update(new UpdateWrapper<WarnSet>().set("last_push_time", date).eq("id", id));
    }
}
