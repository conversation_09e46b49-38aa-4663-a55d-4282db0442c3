package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 得分规则配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillSignVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillSignId;

    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "任务id不能为空")
    private Long drillTaskId;

    @NotBlank(message = "姓名不能为空")
    private String userName;

    private String phone;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Integer pageNum;
    private Integer pageSize;
}
