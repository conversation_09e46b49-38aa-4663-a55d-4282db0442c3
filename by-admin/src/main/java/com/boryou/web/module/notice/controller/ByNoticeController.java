package com.boryou.web.module.notice.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.ESConstant;
import com.boryou.web.constant.NoticeEnum;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.module.notice.bo.NoticeBO;
import com.boryou.web.module.notice.bo.SearchTime;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.service.IByNoticeService;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.service.WarnApiService;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 站内信Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/module/notice")
@AllArgsConstructor
@Slf4j
public class ByNoticeController extends BaseController {
    private final IByNoticeService byNoticeService;
    private final WarnApiService warnApiService;

    @Autowired
    private RedisCache redisTemplate;

    /**
     * 查询站内信列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ByNotice byNotice) {
        startPage();
        List<ByNotice> list = byNoticeService.selectByNoticeList(byNotice);
        Map<String, List<SearchTime>> weekList = new HashMap<>();
        List<String> warnIds = new ArrayList<>();
        for (ByNotice notice : list) {
            if (null == notice.getTime()) {
                continue;
            }
            String noticeType = notice.getNoticeType();
            if (CharSequenceUtil.equals(noticeType, NoticeEnum.WARN.getType())) {
                warnIds.add(String.valueOf(notice.getDocIndexId()));
            }
            List<SearchTime> list1 = new ArrayList<>();
            getQueryTypes(notice, list1, weekList);
        }
        PageResult<BoryouBean> search = new PageResult<>();
        Set<String> strings = weekList.keySet();
        for (String string : strings) {
            List<SearchTime> searchTimes = weekList.get(string);
            EsSearchBO bo = new EsSearchBO();
            bo.setId(CollUtil.join(searchTimes.stream().map(SearchTime::getId).collect(Collectors.toList()), ","));
            bo.setIndexs(Collections.singletonList(string));
            bo.setStartTime(DateUtil.formatDateTime(findMinDate(searchTimes)));
            bo.setEndTime(DateUtil.formatDateTime(findMaxDate(searchTimes)));
            PageResult<BoryouBean> search1 = EsSearchUtil.search(bo, EsSearchUtil.SEARCH);
            search.addAll(search1);
        }
        List<NoticeBO> notices = new ArrayList<>();
        Map<Long, BoryouBean> collect1 = search.stream().collect(Collectors.toMap(c -> Long.parseLong(c.getId()), s -> s));
        Map<Long, EsBean> warnMap = new HashMap<>();
        if (CollUtil.isNotEmpty(warnIds)) {
            WarnDataVO warnDataVO = new WarnDataVO();
            warnDataVO.setIds(warnIds);
            warnDataVO.setPageNum(1);
            warnDataVO.setPageSize(20);
            PageResult<EsBean> esBeans = warnApiService.warnDataGet(warnDataVO);
            if (CollUtil.isNotEmpty(esBeans)) {
                warnMap = esBeans.stream().collect(Collectors.toMap(c -> Long.parseLong(c.getId()), s -> s));
            }
        }
        for (ByNotice notice : list) {
            String noticeType = notice.getNoticeType();
            //预警
            if (CharSequenceUtil.equals(noticeType, NoticeEnum.WARN.getType())) {
                EsBean esBeanWarn = warnMap.get(notice.getDocIndexId());
                if (esBeanWarn == null) {
                    log.error("预警通知列表报错！！！：{}数据未找到", notice.getDocIndexId());
                    continue;
                }
                String articleId = esBeanWarn.getArticleId();
                NoticeBO bean = JSONUtil.toBean(JSONUtil.toJsonStr(esBeanWarn), NoticeBO.class);
                bean.setNoticeTime(notice.getNoticeTime());
                bean.setNoticeType(notice.getNoticeType());
                bean.setNoticeId(notice.getNoticeId());
                bean.setDocIndexId(Long.valueOf(articleId));
                bean.setId(articleId);
                if (null != bean.getText()) {
                    bean.setText(HtmlUtil.cleanHtmlTag(bean.getText()));
                }
                bean.setTypeName(MediaTypeEnum.getDesc(String.valueOf(esBeanWarn.getType())));
                notices.add(bean);
                continue;
            }
            BoryouBean esBean = collect1.get(notice.getDocIndexId());
            if (null == esBean) {
                log.error("通知列表报错！！！：{}数据未找到", notice.getDocIndexId());
                continue;
            }
            NoticeBO bean = JSONUtil.toBean(JSONUtil.toJsonStr(esBean), NoticeBO.class);
            bean.setNoticeTime(notice.getNoticeTime());
            bean.setNoticeType(notice.getNoticeType());
            bean.setNoticeId(notice.getNoticeId());
            bean.setDocIndexId(notice.getDocIndexId());
            if (null != bean.getText()) {
                bean.setText(HtmlUtil.cleanHtmlTag(bean.getText()));
            }
            bean.setTypeName(MediaTypeEnum.getDesc(String.valueOf(esBean.getType())));
            notices.add(bean);
        }
        TableDataInfo dataTable = getDataTable(notices);
        dataTable.setTotal(new PageInfo(list).getTotal());
        long total = dataTable.getTotal();
        redisTemplate.setCacheObject(RedisConstant.system_prefix + "site_message_total:" + SecurityUtils.getUserIdL(), total);
        return dataTable;
    }

    @GetMapping("/latest")
    public AjaxResult latest(ByNotice byNotice) {
        int allCount = byNoticeService.latest(byNotice);
        Object cacheObject = redisTemplate.getCacheObject(RedisConstant.system_prefix + "site_message_total:" + SecurityUtils.getUserIdL());
        HashMap map = new HashMap<>();
        map.put("unReadCount", 0L);
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            long total = Long.parseLong(cacheObject.toString());
            long l = allCount - total;
            if (l < 0) {
                l = 0;
            }
            map.put("unReadCount", l);
        } else {
            map.put("newMessage", allCount > 0);
        }
        map.put("allCount", allCount);
        return AjaxResult.success(map);
    }

    private static void getQueryTypes(ByNotice notice, List<SearchTime> list, Map<String, List<SearchTime>> weekList) {
        Date startTime = notice.getTime();
        list.add(new SearchTime(notice.getDocIndexId(), startTime));
        Date now = new Date();
        Date oneWeekAgo = DateUtil.offsetDay(now, -7);
        Date oneWeekTemp = DateUtil.offsetDay(now, -8);
        Date oneMonthAgo = DateUtil.offsetDay(now, -30);
        if (startTime.after(oneWeekAgo)) {
            // 发文开始结束时间都在一周内
            if (weekList.containsKey(ESConstant.ES_INDEX_WEEK)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_WEEK);
                ArrayList<SearchTime> c = new ArrayList<>(searchTimes);
                list.addAll(c);
                weekList.put(ESConstant.ES_INDEX_WEEK, list);
            } else {
                weekList.put(ESConstant.ES_INDEX_WEEK, list);
            }
        } else if (startTime.after(oneMonthAgo)) {
            if (startTime.after(oneWeekTemp)) {
                //为了解决7天前的数据还没迁移到月节点，做的一次周、月节点双查的兼容性查询，保证一定能查到
                if (weekList.containsKey(ESConstant.ES_INDEX_WEEK)) {
                    List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_WEEK);
                    ArrayList<SearchTime> c = new ArrayList<>(searchTimes);
                    list.addAll(c);
                    weekList.put(ESConstant.ES_INDEX_WEEK, list);
                } else {
                    weekList.put(ESConstant.ES_INDEX_WEEK, list);
                }
            }
            if (weekList.containsKey(ESConstant.ES_INDEX_MONTH)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_MONTH);
                list.addAll(new ArrayList<>(searchTimes));
                weekList.put(ESConstant.ES_INDEX_MONTH, list);
            } else {
                weekList.put(ESConstant.ES_INDEX_MONTH, list);
            }
        } else {
            if (weekList.containsKey(ESConstant.ES_INDEX)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX);
                list.addAll(new ArrayList<>(searchTimes));
                weekList.put(ESConstant.ES_INDEX, list);
            } else {
                weekList.put(ESConstant.ES_INDEX, list);
            }
        }
    }

    public static Date findMaxDate(List<SearchTime> objs) {
        return objs.stream()
                .map(obj -> obj.getTime())
                .max(Date::compareTo)
                .orElse(null);
    }

    public static Date findMinDate(List<SearchTime> objs) {
        return objs.stream()
                .map(obj -> obj.getTime())
                .min(Date::compareTo)
                .orElse(null);
    }

//    /**
//     * 导出站内信列表
//     */
//    @PreAuthorize("@ss.hasPermi('module:notice:export')")
//    @Log(title = "站内信", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    public AjaxResult export(ByNotice byNotice) {
//        List<ByNotice> list = byNoticeService.selectByNoticeList(byNotice);
//        ExcelUtil<ByNotice> util = new ExcelUtil<ByNotice>(ByNotice. class);
//        return util.exportExcel(list, "notice");
//    }
//
//    /**
//     * 获取站内信详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('module:notice:query')")
//    @GetMapping(value = "/{noticeId}")
//    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId) {
//        return AjaxResult.success(byNoticeService.selectByNoticeById(noticeId));
//    }
//
//    /**
//     * 新增站内信
//     */
//    @PreAuthorize("@ss.hasPermi('module:notice:add')")
//    @Log(title = "站内信", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody ByNotice byNotice) {
//        return toAjax(byNoticeService.insertByNotice(byNotice));
//    }
//
//    /**
//     * 修改站内信
//     */
//    @PreAuthorize("@ss.hasPermi('module:notice:edit')")
//    @Log(title = "站内信", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody ByNotice byNotice) {
//        return toAjax(byNoticeService.updateByNotice(byNotice));
//    }
//
//    /**
//     * 删除站内信
//     */
//    @PreAuthorize("@ss.hasPermi('module:notice:remove')")
//    @Log(title = "站内信", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{noticeIds}")
//    public AjaxResult remove(@PathVariable Long[] noticeIds) {
//        return toAjax(byNoticeService.deleteByNoticeByIds(noticeIds));
//    }
}
