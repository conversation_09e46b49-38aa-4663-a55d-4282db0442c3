package com.boryou.web.module.notice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.domain.ByNoticeRela;

import java.util.List;

/**
 * 站内信Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface ByNoticeMapper extends BaseMapper<ByNotice> {
    /**
     * 查询站内信
     *
     * @param noticeId 站内信ID
     * @return 站内信
     */
    public ByNotice selectByNoticeById(Long noticeId);

    /**
     * 查询站内信列表
     *
     * @param byNotice 站内信
     * @return 站内信集合
     */
    public List<ByNotice> selectByNoticeList(ByNotice byNotice);

    public List<ByNotice> selectNoticeList(String userId);

    public int selectNoticeListCount(String userId);

    /**
     * 新增站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    public int insertByNotice(ByNotice byNotice);

    /**
     * 修改站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    public int updateByNotice(ByNotice byNotice);

    /**
     * 删除站内信
     *
     * @param noticeId 站内信ID
     * @return 结果
     */
    public int deleteByNoticeById(Long noticeId);

    /**
     * 批量删除站内信
     *
     * @param noticeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteByNoticeByIds(Long[] noticeIds);

    /**
     * 批量删除${subTable.functionName}
     *
     * @param customerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteByNoticeRelaByNoticeIds(Long[] noticeIds);

    /**
     * 批量新增${subTable.functionName}
     *
     * @param byNoticeRelaList ${subTable.functionName}列表
     * @return 结果
     */
    public int batchByNoticeRela(List<ByNoticeRela> byNoticeRelaList);


    /**
     * 通过站内信ID删除${subTable.functionName}信息
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteByNoticeRelaByNoticeId(Long noticeId);
}
