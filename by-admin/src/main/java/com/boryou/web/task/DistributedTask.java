package com.boryou.web.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-06-26 12:48
 */
@Component
public class DistributedTask {

    @Autowired
    private RedisTemplate redisTemplate;

    public boolean tryLock(String key, TimeUnit timeUnit, long timeout) {
        Boolean lockResult = redisTemplate.opsForValue().setIfAbsent(key, "lock", timeout, timeUnit);
        return Boolean.TRUE.equals(lockResult);
    }

    public void unlock(String key) {
        redisTemplate.delete(key);
    }
}

