package com.boryou.web.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.boryou.common.utils.ip.IpUtils;
import com.boryou.web.controller.common.entity.vo.HomeVO;
import com.boryou.web.module.home.service.HomeStatisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-30 14:28
 */
@EnableScheduling
@Configuration
@Slf4j
@RequiredArgsConstructor
public class HomeTask extends DistributedTask {
    private final HomeStatisService homeStatisService;


    private final String LOCK_KEY = "cacheHomeTask";
//    @PreDestroy
//    public void destroy(){
//        log.info("释放首页缓存定时任务锁key");
//        unlock(LOCK_KEY);
//    }

    //@Scheduled(fixedDelay = 30 * 60 * 1000)
    public void cacheHome() {
//        if (!tryLock(LOCK_KEY, TimeUnit.MINUTES,30)) {
//            log.info("首页缓存定时任务已被其他实例占用，本次跳过执行。");
//            return;
//        }
        String osName = System.getProperty("os.name").toLowerCase();
        String hostIp = IpUtils.getHostIp();
//        log.info("当前系统ip:{}", hostIp);
        if (osName.contains("windows") && !"*************".equals(hostIp)) {
            return;
        }
        List<String> areaCodeList = homeStatisService.getAreaCodeList();
        long start = System.currentTimeMillis();
        try {
            getWordCloud(areaCodeList);//热词云
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            areaOverview(areaCodeList);//辖区总览和总数一起缓存
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            updateEmontion(areaCodeList);//情感分布
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            updateCurve(areaCodeList);//曲线图
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            authorLineChat(areaCodeList);//活跃账号
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            updateLawyerMoment(areaCodeList);//律师动态
        } catch (Exception e) {
            e.printStackTrace();
        }
        //try {
        //    total(areaCodeList);//总数
        //} catch (Exception e) {
        //    e.printStackTrace();
        //}
        try {
            hotZJ();//浙江政法热点
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            List<Long> deptIdList = homeStatisService.getAllDeptIdList();
            if (CollUtil.isNotEmpty(deptIdList)) {
                warn(deptIdList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("首页缓存当天至30天数据结束：本次耗时：{}秒", ((System.currentTimeMillis() - start) / 1000));
//        unlock(LOCK_KEY);
    }

    public void updateEmontion(List<String> areaCodeList) {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.getEmontionStatis(homeVO, false);
            homeVO = get24();
            homeVO.setContentAreaCode(s);
            homeStatisService.getEmontionStatis(homeVO, false);
            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getEmontionStatis(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getEmontionStatis(homeVO, false);
        }

    }

    public void updateLawyerMoment(List<String> areaCodeList) {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.getLawyerMoment(homeVO, false);

            homeVO = get24();
            homeVO.setContentAreaCode(s);
            homeStatisService.getLawyerMoment(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getLawyerMoment(homeVO, false);
            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getLawyerMoment(homeVO, false);
        }
    }

    public void hotZJ() {
        HomeVO homeVO = getToday();
        homeStatisService.hotZJ(homeVO, false);

        homeVO = get24();
        homeStatisService.hotZJ(homeVO, false);

        homeVO = get7Day();
        homeStatisService.hotZJ(homeVO, false);

        homeVO = get30Day();
        homeStatisService.hotZJ(homeVO, false);

    }

    public void warn(List<Long> deptIdList) {
        for (Long dept : deptIdList) {
            HomeVO homeVO = getToday();
            homeVO.setDeptId(dept);
            homeStatisService.warn(homeVO, false);

            homeVO = get24();
            homeVO.setDeptId(dept);
            homeStatisService.warn(homeVO, false);

            homeVO = get7Day();
            homeVO.setDeptId(dept);
            homeStatisService.warn(homeVO, false);

            homeVO = get30Day();
            homeVO.setDeptId(dept);
            homeStatisService.warn(homeVO, false);
        }
    }

    public void updateCurve(List<String> areaCodeList) {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.curveStatis(homeVO, false);
            homeVO = get24();

            homeVO.setContentAreaCode(s);
            homeStatisService.curveStatis(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.curveStatis(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.curveStatis(homeVO, false);
        }
    }

    public void areaOverview(List<String> areaCodeList) {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.areaOverview(homeVO, false);
            homeVO = get24();

            homeVO.setContentAreaCode(s);
            homeStatisService.areaOverview(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.areaOverview(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.areaOverview(homeVO, false);
        }
    }

    public void authorLineChat(List<String> areaCodeList) {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.authorLineChat(homeVO, false);
            homeVO = get24();

            homeVO.setContentAreaCode(s);
            homeStatisService.authorLineChat(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.authorLineChat(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.authorLineChat(homeVO, false);
        }
    }

    public void getWordCloud(List<String> areaCodeList) throws IOException {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.getWordCloud(homeVO, false);
            homeVO = get24();

            homeVO.setContentAreaCode(s);
            homeStatisService.getWordCloud(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getWordCloud(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.getWordCloud(homeVO, false);
        }
    }


    public void total(List<String> areaCodeList) throws IOException {
        for (String s : areaCodeList) {
            HomeVO homeVO = getToday();
            homeVO.setContentAreaCode(s);
            homeStatisService.total(homeVO, false);
            homeVO = get24();

            homeVO.setContentAreaCode(s);
            homeStatisService.total(homeVO, false);

            homeVO = get7Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.total(homeVO, false);

            homeVO = get30Day();
            homeVO.setContentAreaCode(s);
            homeStatisService.total(homeVO, false);
        }
    }

    public void getMainPlan() throws IOException {
        HomeVO homeVO = getToday();
        homeStatisService.getMainPlan(homeVO, false);
        homeVO = get24();

        homeStatisService.getMainPlan(homeVO, false);

        homeVO = get7Day();
        homeStatisService.getMainPlan(homeVO, false);
        homeVO = get30Day();
        homeStatisService.getMainPlan(homeVO, false);
    }

    public void getCountryHotPlan() throws IOException {
        HomeVO homeVO = getToday();
        homeStatisService.getCountryHotPlan(homeVO, false);
        homeVO = get24();

        homeStatisService.getCountryHotPlan(homeVO, false);

        homeVO = get7Day();
        homeStatisService.getCountryHotPlan(homeVO, false);

        homeVO = get30Day();
        homeStatisService.getCountryHotPlan(homeVO, false);
    }

    public void getProvinceHotPlan() throws IOException {
        HomeVO homeVO = getToday();
        homeStatisService.getProvinceHotPlan(homeVO, false);
        homeVO = get24();

        homeStatisService.getProvinceHotPlan(homeVO, false);

        homeVO = get7Day();
        homeStatisService.getProvinceHotPlan(homeVO, false);

        homeVO = get30Day();
        homeStatisService.getProvinceHotPlan(homeVO, false);
    }


    public void casePaln() {
        HomeVO homeVO = getToday();
        homeStatisService.casePaln(homeVO, false);
        homeVO = get24();

        homeStatisService.casePaln(homeVO, false);

        homeVO = get7Day();
        homeStatisService.casePaln(homeVO, false);

        homeVO = get30Day();
        homeStatisService.casePaln(homeVO, false);
    }

    public HomeVO getToday() {
        Date date = new Date();
        HomeVO homeVO = new HomeVO();
        homeVO.setTimeType("1");
//        homeVO.setContentAreaCode("330000");
        homeVO.setStartTime(DateUtil.formatDate(date) + " 00:00:00");
        homeVO.setEndTime(DateUtil.formatDateTime(DateUtil.offsetMinute(date, 30)));
        return homeVO;
    }

    public HomeVO get24() {
        Date date = new Date();
        HomeVO homeVO = new HomeVO();
        homeVO.setTimeType("2");
//        homeVO.setContentAreaCode("330000");
        DateTime newDate5 = DateUtil.offsetDay(date, -1);
        homeVO.setStartTime(DateUtil.formatDateTime(newDate5));
        homeVO.setEndTime(DateUtil.formatDateTime(date));
        return homeVO;
    }

    public HomeVO get7Day() {
        Date date = new Date();
        HomeVO homeVO = new HomeVO();
        homeVO.setTimeType("3");
//        homeVO.setContentAreaCode("330000");
        homeVO.setStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(date, -7)));
        homeVO.setEndTime(DateUtil.formatDateTime(date));
        return homeVO;
    }

    public HomeVO get30Day() {
        Date date = new Date();
        HomeVO homeVO = new HomeVO();
        homeVO.setTimeType("4");
//        homeVO.setContentAreaCode("330000");
        homeVO.setStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(date, -30)));
        homeVO.setEndTime(DateUtil.formatDateTime(date));
        return homeVO;
    }

    public static void main(String[] args) {
        Date date = new Date();
        Date newDate = DateUtil.offset(date, DateField.DAY_OF_MONTH, 2);
        DateTime newDate2 = DateUtil.offsetDay(date, 3);
        DateTime newDate3 = DateUtil.offsetHour(date, -1);
        DateTime newDate4 = DateUtil.offsetMinute(date, 30);

//        System.out.println(DateUtil.formatDateTime(newDate));
//        System.out.println(DateUtil.formatDateTime(newDate2));
        System.out.println(DateUtil.formatDate(date) + " 00:00:00");
        System.out.println(DateUtil.formatDateTime(newDate4));

        System.out.println("=-===========");
        DateTime newDate5 = DateUtil.offsetDay(date, -7);
        System.out.println(DateUtil.formatDateTime(date));
        System.out.println(DateUtil.formatDateTime(newDate5));
    }
}

