package com.boryou.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;


@Data
public class Hot {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 地址
     */
    private String url;

    /**
     * 指数
     */
    private long indexNum;
    /**
     * 指数-带采集中文本的
     */
    private String indexNumStr;


    /**
     * 浏览量
     */
//    @JsonSerialize(using = ToStringSerializer.class)
    private String pageView;
    /**
     * 文章数
     */
    private int docNum;

    //    @JsonSerialize(using = ToStringSerializer.class)
    private String docId;
    /**
     * 榜单类型，AREA_BELLWETHER百度地域风向标,WEEK七日榜，DAY近日榜，REALTIME实时榜，WB_REALTIME微博实时榜，WB_NEWS微博要闻，DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜，TOUTIAO_HOT今日头条热点榜
     */
    private String type;

    private String typeName;

    /**
     * 指数状态，0下降，1保持不变，2上升
     */
    private Integer indexStatus;

    /**
     * 是否为新数据 1是 0否 2热 3沸
     */
    private Integer newInfo;

    /**
     * 排序字段，每个榜单的信息顺序
     */
    private Long sort;

    /**
     * 地域码
     */
    private String areaCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
