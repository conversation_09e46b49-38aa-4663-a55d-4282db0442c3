package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 方案对象 by_plan
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Data
@TableName("by_plan")
public class Plan {
    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long planId;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    private String planName;

    /**
     * 关键词1
     */
    @Excel(name = "关键词1")
    private String kw1;

    /**
     * 关键词2
     */
    @Excel(name = "关键词2")
    private String kw2;

    /**
     * 关键词3
     */
    @Excel(name = "关键词3")
    private String kw3;

    /**
     * 排除词
     */
    @Excel(name = "排除词")
    private String excludeWord;

    /**
     * 精准地域
     */
    @Excel(name = "精准地域")
    private String area;

    /**
     * 检索模式(0：普通模式，1：高级模式)
     */
    @Excel(name = "检索模式(0：普通模式，1：高级模式)")
    private Integer searchMode;

    /**
     * 检索位置(0：全部，1：标题，2：正文)
     */
    @Excel(name = "检索位置(0：全部，1：标题，2：正文)")
    private Integer searchPosition;

    /**
     * 文件夹id    -1是其他方案  -2是重点方案
     */
    @Excel(name = "文件夹id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long typeId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 作用开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "作用开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectStartTime;

    /**
     * 作用结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "作用结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectEndTime;

    /**
     * 排序值
     */
    @Excel(name = "排序值")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sort;

    /**
     * 是否删除(0：正常，1：已删除)
     */
    private Integer delFlag;

    /**
     * 是否启用(0：禁用，1：启用)
     */
    @Excel(name = "是否启用(0：禁用，1：启用)")
    private Integer enableFlag;

    /**
     * 高级监控关键词
     */
    @Excel(name = "高级监控关键词")
    private String highMonitorWord;

    /**
     * 高级排除关键词
     */
    @Excel(name = "高级排除关键词")
    private String highExcludeWord;

    /**
     * 高级精准地域
     */
    @Excel(name = "高级精准地域")
    private String highArea;

    /**
     * 历史方案 0 不是 1 是
     */
    @Excel(name = "历史方案 0 不是 1 是")
    private Integer historyFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否可删除
     */
    @TableField(exist = false)
    private boolean optionFlag;

    /**
     * 组织名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 方案类型名称
     */
    @TableField(exist = false)
    private String typeName;

    private Integer expire;

    /**
     * 是否追踪 0：否 1：是
     */
    private Integer tracking;

    /**
     * 是否追踪 0：否 1：是
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long trackingId;

    /**
     * 热搜关键词
     */
    @Excel(name = "热搜关键词")
    private String hotKw;
}
