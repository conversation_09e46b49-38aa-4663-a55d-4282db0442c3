package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 疫情公告数据表
 * <AUTHOR>
 */
@Data
@TableName("by_epidemic_announcement")
public class ApidemicAnnouncement {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 省（包括自治区、直辖市）
     */
    private String province;

    /**
     * 市（包括自治州、盟、省辖市、直辖市辖区(县)）
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 传染病名称
     */
    private String epidemicName;

    /**
     * 新增确诊病例数
     */
    private Integer newConfirmed;

    /**
     * 新增本土确诊病例数
     */
    private Integer newLocalConfirmed;

    /**
     * 新增境外输入确诊病例数
     */
    private Integer newImportedConfirmed;

    /**
     * 新增无症状转确诊病例数
     */
    private Integer newAsymptomaticConfirmed;

    /**
     * 新增本土无症状转确诊病例数
     */
    private Integer newLocalAsymptomaticConfirmed;

    /**
     * 新增境外输入无症状转确诊病例数
     */
    private Integer newImportedAsymptomaticConfirmed;

    /**
     * 新增无症状感染者数
     */
    private Integer newAsymptomatic;

    /**
     * 公告日期（可根据实际情况添加，用于记录该数据对应的公告时间）
     */
    private Date announcementTime;

    /**
     * 数据更新时间
     */
    private Date updateTime;

    /**
     * 统计开始日期
     */
    private Date statStartTime;

    /**
     *统计截止日期
     */
    private Date statEndTime;

    /**
     * 累计病例数
     */
    private Integer cumulatedCases;

    /**
     * 死亡病例数
     */
    private Integer cumulatedDeaths;

    /**
     * 境外输入风险等级(低,中,高)
     */
    private String importedRisk;

    /**
     * 海外旅游风险等级(低,中,高)
     */
    private String overseasTravelRisk;


}
