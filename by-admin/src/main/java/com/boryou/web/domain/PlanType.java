package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 方案文件夹对象 by_plan_type
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@TableName(value = "by_plan_type")
public class PlanType {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long typeId;

    /**
     * 文件夹名称
     */
    @Excel(name = "文件夹名称")
    private String typeName;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 排序值
     */
    @Excel(name = "排序值")
    private Long sort;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 组织名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 组织id
     */
    @TableField(exist = false)
    private String deptId;

}
