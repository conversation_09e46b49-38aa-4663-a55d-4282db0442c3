package com.boryou.web.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 热词云热词
 *
 * @TableName by_cloud_word
 */
@Data
public class CloudWordVO {
    /**
     *
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String ids;

    /**
     * 作为查询条件 判断是否查询所属模块 1数据概览 2舆情监测
     */
    private Integer isPlan;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;

    /**
     * 搜索词
     */
    private String word;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 状态 0 正常词 1 过滤词
     */
    private String state;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;

}
