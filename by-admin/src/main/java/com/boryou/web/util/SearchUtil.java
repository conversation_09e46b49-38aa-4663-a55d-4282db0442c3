package com.boryou.web.util;

import cn.hutool.core.util.StrUtil;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;

/**
 * <AUTHOR>
 */
public class SearchUtil {


    public static void getCourtNamesFromText(String str, StringBuilder builder, String subStr) {
        if (str.contains(subStr)) {
            if (str.indexOf(subStr) > 20) {
                builder.append(str, str.indexOf(subStr) - 20, str.indexOf(subStr) + 2);
            } else {
                builder.append(str, 0, str.indexOf(subStr) + 2);
            }
            str = str.substring(str.indexOf(subStr) + 2);
            getCourtNamesFromText(str, builder, subStr);
        }
    }

    public static String getSearchKeyWord(EsSearchBO bo) {
        //关键词标红
        String keyWord = "";
        if (bo.getConfigSelect() == 1 && StrUtil.isNotEmpty(bo.getProWord())) {
            keyWord = bo.getProWord().replaceAll("\\|", " ").replaceAll("\\+", " ")
                    .replaceAll("\\(", " ").replaceAll("\\)", " ");
        } else {
            if (StrUtil.isNotEmpty(bo.getKeyWord1())) {
                keyWord += bo.getKeyWord1() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord2())) {
                keyWord += bo.getKeyWord2() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord3())) {
                keyWord += bo.getKeyWord3() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord4())) {
                keyWord += bo.getKeyWord4() + " ";
            }
            if (StrUtil.isNotEmpty(keyWord)) {
                keyWord = keyWord.substring(0, keyWord.length() - 1);
            }
        }
        if (StrUtil.isNotEmpty(bo.getQuadraticWord())) {
            keyWord = keyWord + " " + bo.getQuadraticWord().replaceAll("\\|", " ").replaceAll("\\+", " ")
                    .replaceAll("\\(", " ").replaceAll("\\)", " ");
        }
        return keyWord;
    }

    public static String getSearchKeyWordPro(EsSearchBO bo) {
        String keyWord = "";
        if (bo.getConfigSelect() == 1 && StrUtil.isNotEmpty(bo.getProWord2())) {
            keyWord = bo.getProWord2().replaceAll("\\|", " ").replaceAll("\\+", " ")
                    .replaceAll("\\(", " ").replaceAll("\\)", " ");
        }
        if (StrUtil.isNotEmpty(bo.getQuadraticWord())) {
            keyWord = keyWord + " " + bo.getQuadraticWord().replaceAll("\\|", " ").replaceAll("\\+", " ")
                    .replaceAll("\\(", " ").replaceAll("\\)", " ");
        }
        return keyWord;
    }

    public static void main(String[] args) {
        String str = "这些视频画面血腥，且配有离婚证、法院等元素，迅速在网络上引起了轩然大波。然而，安徽合肥警方在调查后发现，倪某发布的视频中存在多处疑点。例如，部分画面显示为合肥高新区人民法院，但发布者的位置却显示在江苏。";
        StringBuilder builder = new StringBuilder();
        getCourtNamesFromText(str, builder, "法院");
        str = builder.toString();
        builder = new StringBuilder();
        System.out.println(str);
    }
}
