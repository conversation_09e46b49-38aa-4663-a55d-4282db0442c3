package com.boryou.web.util.poi.custom;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;

/**
 * POI操作word中的表格工具类
 *
 * <AUTHOR>
 * @date 2018-01-11 16:52:13
 */
public class TableUtil {

    private static void setCellContent(XWPFTableCell cell, String bgColor, int width, String text, XWPFParagraph xwpfParagraph) {
        CTTc cttc = cell.getCTTc();
        CTTcPr ctPr = cttc.addNewTcPr();
        CTShd ctshd = ctPr.addNewShd();
        ctPr.addNewTcW().setW(BigInteger.valueOf(width));
        if (bgColor == null) {
            ctshd.setFill("#FFFFFF");
        } else {
            ctshd.setFill(bgColor);
        }
        ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
        if (xwpfParagraph == null) {
            cell.setText(text);
        } else {
            cell.setParagraph(xwpfParagraph);
        }
    }

    /**
     * 合并单元格
     *
     * @param xwpfTable word中表格对象
     * @param fromRow   从哪一行开始，包括此行
     * @param toRow     到哪一行结束，不包含此行
     * @param fromCol   从那一列开始，包括此列
     * @param toCol     到哪一列结束，不包含此列
     * <AUTHOR>
     * @date 2018-01-11 17:10:34
     */
    public static void mergeCells(XWPFTable xwpfTable, int fromRow, int toRow, int fromCol, int toCol) {
        //先合并每一行
        if (toCol - fromCol > 1) {
            for (int rowIndex = fromRow; rowIndex < toRow; rowIndex++) {
                mergeCellsInOneRow(xwpfTable, rowIndex, fromCol, toCol);
            }
        }
        //再合并列
        if (toRow - fromRow > 1) {
            mergeCellsInOneCol(xwpfTable, fromCol, fromRow, toRow);
        }
    }

    /**
     * 合并同一行内的单元格
     *
     * @param xwpfTable word中表格对象
     * @param rowIndex  要合并的行的索引
     * @param fromCol   从那一列开始，包括此列
     * @param toCol     到哪一列结束，不包含此列
     * <AUTHOR>
     * @date 2017-11-03 09:20:30
     */
    public static void mergeCellsInOneRow(XWPFTable xwpfTable, int rowIndex, int fromCol, int toCol) {
        for (int cellIndex = fromCol; cellIndex < toCol; cellIndex++) {
            XWPFTableCell cell = xwpfTable.getRow(rowIndex).getCell(cellIndex);
            if (cellIndex == fromCol) {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 合并同一列内的单元格
     *
     * @param xwpfTable word中表格对象
     * @param colIndex  要合并的列的索引
     * @param fromRow   从哪一行开始，包括此行
     * @param toRow     到哪一行结束，不包含此行
     * <AUTHOR>
     * @date 2018-01-11 17:21:42
     */
    public static void mergeCellsInOneCol(XWPFTable xwpfTable, int colIndex, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex < toRow; rowIndex++) {
            XWPFTableCell cell = xwpfTable.getRow(rowIndex).getCell(colIndex);
            if (rowIndex == fromRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置单元格宽度（此方法适用于初始创建的cell单元格，即未设置过宽度的单元格）
     *
     * <AUTHOR>
     * @date 2018-01-12 15:39:21
     */
    public static void setCellWidth(XWPFTableCell cell, int width) {
        cell.getCTTc().addNewTcPr().addNewTcW().setW(BigInteger.valueOf(width));
    }

    /**
     * 设置整个表格宽度
     *
     * <AUTHOR>
     * @date 2018-01-11 17:22:23
     */
    public static void setTableWidth(XWPFTable table, int width) {
        CTTbl ctTbl = table.getCTTbl();
        CTTblPr tblPr = ctTbl.getTblPr() == null ? ctTbl.addNewTblPr() : ctTbl.getTblPr();
        //设置表格居中，重要
        CTJc cTJc = tblPr.addNewJc();
        cTJc.setVal(STJc.Enum.forString("center"));
        CTTblWidth tblWidth = tblPr.isSetTblW() ? tblPr.getTblW() : tblPr.addNewTblW();
        tblWidth.setType(STTblWidth.DXA);
        //设置宽度
        tblWidth.setW(BigInteger.valueOf(width));
    }

    /**
     * 设置所有表格行不能分割
     * <p><b>请在表格操作完成后调用</b></p>
     *
     * <AUTHOR>
     * @date 2018-01-19 14:28:52
     */
    public static void setCantSplitRow(XWPFTable xwpfTable) {
        for (XWPFTableRow row : xwpfTable.getRows()) {
            row.setCantSplitRow(true);
        }
    }

    /**
     * 设置单元格内文字
     *
     * <AUTHOR>
     * @date 2017-11-03 09:19:07
     */
    public void setCellText(XWPFTableCell cell, String text, String bgColor, int width) {
        setCellContent(cell, bgColor, width, text, null);
    }

    /**
     * 设置单元格内段落
     *
     * <AUTHOR>
     * @date 2017-11-03 09:45:00
     */
    public void setCellParagraph(XWPFTableCell cell, XWPFParagraph xwpfParagraph, String bgColor, int width) {
        setCellContent(cell, bgColor, width, null, xwpfParagraph);
    }

}
