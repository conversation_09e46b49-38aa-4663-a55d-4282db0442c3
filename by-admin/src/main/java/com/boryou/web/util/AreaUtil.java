//package com.boryou.web.util;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.io.resource.ClassPathResource;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.http.HttpUtil;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.baomidou.mybatisplus.extension.api.R;
//import com.boryou.common.utils.StringUtils;
//import com.boryou.submit.vo.AreaTreeNew;
//import com.boryou.web.constant.AreaConstant;
//import com.boryou.web.module.area.entity.AreaTree;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.google.common.collect.BiMap;
//import com.google.common.collect.HashBiMap;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.*;
//
/// **
// * @author: Young
// * @Date: 2023/8/10
// */
//@Component
//@Slf4j
//public class AreaUtil {
//
//}
