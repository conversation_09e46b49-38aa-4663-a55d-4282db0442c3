package com.boryou.web.util;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * redis锁工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisLock {

    public void executeWithLock4j(String lockKey, Runnable runnable) {
        LockTemplate lockTemplate = SpringUtil.getBean(LockTemplate.class);
        final LockInfo lockInfo = lockTemplate.lock(lockKey, 10 * 60 * 1000L, 3000L);
        if (null == lockInfo) {
            return;
        }
        try {
            runnable.run();
        } catch (Exception e) {
            log.warn("{}, 外层锁失败: {}", lockKey, e.getMessage());
        } finally {
            lockTemplate.releaseLock(lockInfo);
            log.warn("{}, 解锁:  {}", lockKey, Thread.currentThread().getId());
        }
    }

}
