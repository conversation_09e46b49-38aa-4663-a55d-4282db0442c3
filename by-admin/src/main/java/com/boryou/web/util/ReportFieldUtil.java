package com.boryou.web.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.utils.DictUtils;
import com.boryou.web.module.report.entity.ReportTemplate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ReportFieldUtil {

    public static List<String> setTemplateParamSort(ReportTemplate reportTemplate) {
        List<String> params = new ArrayList<>();
        if (reportTemplate == null) {
            AjaxResult.error("报告模板异常");
        }
        List<Field> fields = getField(ReportTemplate.class);
        try {
            Map<String, Integer> entryMap = new HashMap<>();
            for (Field field : fields) {
                field.setAccessible(true);
                Integer value = (Integer) field.get(reportTemplate);
                // 排除值为0的字段
                if (!(value != null && ((Number) value).intValue() == 0)) {
                    entryMap.put(field.getName(), value);
                }
            }
            List<Map.Entry<String, Integer>> sortedEntries = entryMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).collect(Collectors.toList());
            // 提取字段名
            params = sortedEntries.stream()
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return params;
    }

    public static void getTemplateParamSort(ReportTemplate reportTemplate) {
        if (CollUtil.isNotEmpty(reportTemplate.getInputComponents())) {
            List<String> inputComponents = reportTemplate.getInputComponents();
            for (int i = 1; i <= inputComponents.size(); i++) {
                try {
                    Field field = BeanUtil.getBeanDesc(ReportTemplate.class).getField(inputComponents.get(i - 1));
                    field.setAccessible(true);
                    if (field.getType() == int.class || field.getType() == Integer.class) {
                        field.set(reportTemplate, i);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static List<Field> getField(Class<?> clazz) {
        List<Field> fields;
        List<SysDictData> dictCache = DictUtils.getDictCache("report_components");
        if (CollUtil.isNotEmpty(dictCache)) {
            fields = dictCache.stream()
                    .map(d -> getFieldSafely(clazz, d.getDictLabel()))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
        } else {
            try {
                fields = new ArrayList<>(Arrays.asList(
                        clazz.getDeclaredField("reportIntro"),
                        clazz.getDeclaredField("suggest"),
                        clazz.getDeclaredField("overview"),
                        clazz.getDeclaredField("mediaStatistics"),
                        clazz.getDeclaredField("emotionAnalysis"),
                        clazz.getDeclaredField("mediaDetails"),
                        clazz.getDeclaredField("charCloud"),
                        clazz.getDeclaredField("mainInfo"),
                        clazz.getDeclaredField("infoIntro"),
                        clazz.getDeclaredField("mediaTrendChart")));
            } catch (NoSuchFieldException e) {
                return new ArrayList<>();
            }
        }
        return fields;
    }

    private static Optional<Field> getFieldSafely(Class<?> clazz, String fieldName) {
        try {
            return Optional.of(clazz.getDeclaredField(fieldName));
        } catch (NoSuchFieldException e) {
            return Optional.empty();
        }
    }
}
