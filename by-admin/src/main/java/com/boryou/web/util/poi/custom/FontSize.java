package com.boryou.web.util.poi.custom;

/**
 * 字体大小
 *
 * <AUTHOR>
 * @date 2018-01-15 10:37:32
 */
public enum FontSize {

    One("一号", 52),

    <PERSON><PERSON><PERSON><PERSON>("小一", 48),

    <PERSON>("二号", 44),

    <PERSON><PERSON><PERSON><PERSON>("小二", 36),

    <PERSON>("三号", 32),

    <PERSON><PERSON><PERSON><PERSON>("小三", 30),

    <PERSON>("四号", 28),

    <PERSON><PERSON><PERSON><PERSON>("小四", 24),

    <PERSON>("五号", 21),

    <PERSON><PERSON><PERSON><PERSON>("小五", 18);

    private String name;
    private int size;

    FontSize(String name, int size) {
        this.name = name;
        this.size = size;
    }

    public String getName() {
        return name;
    }

    public int getSize() {
        return size;
    }
}
