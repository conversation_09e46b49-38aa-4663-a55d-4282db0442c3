package com.boryou.web.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HtmlUtil;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 豆包工具类
 * @date 2024/7/25 9:28
 */

@Slf4j
public class DoubaoUtil {

    //API-KEY
    public static final String API_KEY = "f829290f-bb20-4692-904a-b812e0da770b";
    //接入点endpoint可以到火山引擎官网配置  https://console.volcengine.com
    public static final String END_POINT_32K = "ep-20240725154455-nvw7w";
    public static final String END_POINT_128K = "ep-20240725174520-fcs9f";
    private static final ArkService service = new ArkService(API_KEY);


    /**
     * 根据正文生成摘要-本期要点
     *
     * @param content 正文
     * @return 返回值
     */
    public static String summary(String content, int deep) {
        content = HtmlUtil.cleanHtmlTag(content);
//        String question = "帮我把下面最主要最典型的内容生成摘要，摘要充实完整，只返回摘要内容就可以了，不需要提示以下是生成的摘要内容，需要包含并提示该事件的日期、地点、人物主体、事件、原因、过程，过程占大部分的摘要内容。最多200个字。" + content;
        String question = "帮我把下面的内容提炼关键信息，必须一一列出时间、地点、人物主体、事件、原因、过程各个字段内容。最多200个字。" + content;
        String s = singleChat(question);
        s = s.replace("**：", "：");
        if (s.contains("内容：")) {
            s = s.substring(s.indexOf("内容：") + 3);
        }
        if (s.contains("内容：")) {
            s = s.substring(s.indexOf("内容：") + 3);
        }
        if (s.contains("信息：")) {
            s = s.substring(s.indexOf("信息：") + 3);
        }
        if (s.contains("思路：")) {
            s = s.substring(s.indexOf("思路：") + 3);
        }
        if (s.contains("梳理：")) {
            s = s.substring(s.indexOf("梳理：") + 3);
        }
        if (s.contains("信息如下：")) {
            s = s.substring(s.indexOf("信息如下：") + 5);
        }
        //返回方式1
        if (s.contains("1. ")) {
            s = s.replace("15. ", " ");
            s = s.replace("14. ", " ");
            s = s.replace("13. ", " ");
            s = s.replace("12. ", " ");
            s = s.replace("11. ", " ");
            s = s.replace("10. ", " ");
            s = s.replace("1. ", " ");
            s = s.replace("2. ", " ");
            s = s.replace("3. ", " ");
            s = s.replace("4. ", " ");
            s = s.replace("5. ", " ");
            s = s.replace("6. ", " ");
            s = s.replace("7. ", " ");
            s = s.replace("8. ", " ");
            s = s.replace("9. ", " ");
        }
        s = s.replaceAll("\\*\\*", "");
        //返回方式2
        if (s.contains("| -- | -- | -- | -- | -- | -- |") || s.contains("|--|--|--|--|--|--|")
                || s.contains("|---|---|---|---|---|---|")) {
            s = s.replace("| -- | -- | -- | -- | -- | -- |", "")
                    .replace("|--|--|--|--|--|--|", "").replace("|---|---|---|---|---|---|", "");
            if (s.contains("|过程|")) {
                s = s.substring(s.indexOf("|过程|") + 4);
                if (s.contains("|")) {
                    s = s.replaceFirst("\\|", "\n时间：").replaceFirst("\\|", "\n地点：");
                    s = s.replaceFirst("\\|", "\n人物主体：").replaceFirst("\\|", "\n事件：");
                    s = s.replaceFirst("\\|", "\n原因：").replaceFirst("\\|", "\n过程：");
                    s = s.replace("|", "");
                }
            }
            if (s.contains("| 过程 |")) {
                s = s.substring(s.indexOf("| 过程 |") + 6);
                if (s.contains("|")) {
                    s = s.replaceFirst("\\| ", "\n时间：").replaceFirst("\\| ", "\n地点：");
                    s = s.replaceFirst("\\| ", "\n人物主体：").replaceFirst("\\| ", "\n事件：");
                    s = s.replaceFirst("\\| ", "\n原因：").replaceFirst("\\| ", "\n过程：");
                    s = s.replace("|", "");
                }
            }
        }
        //返回方式3
        if (s.contains("详情|") || s.contains("详情 |") || s.contains("|--")) {
            s = s.substring(s.indexOf("|--") + 3);
            s = s.replaceAll("\\|--", "");
            s = s.replace("|时间|", "\n时间：").replace("| 时间 |", "\n时间：");
            s = s.replace("|地点|", "\n地点：").replace("| 地点 |", "\n地点：");
            s = s.replace("|人物主体|", "\n人物主体：").replace("| 人物主体 |", "\n人物主体：");
            s = s.replace("|事件|", "\n事件：").replace("| 事件 |", "\n事件：");
            s = s.replace("|原因|", "\n原因：").replace("| 原因 |", "\n原因：");
            s = s.replace("|过程|", "\n过程：").replace("| 过程 |", "\n过程：");
            s = s.replaceAll("\\|", "");
            s = s.replaceAll("--", "");
        }
        //返回方式4
        if (s.contains("时间是")&&s.contains("地点是")&&s.contains("人物主体是")&&s.contains("事件是")&&s.contains("原因是")&&s.contains("过程是")) {
            s = s.replaceFirst("时间是","\n时间：").replaceFirst("地点是","\n地点：").replaceFirst("人物主体是","\n人物主体：")
                    .replaceFirst("事件是","\n事件：").replaceFirst("原因是","\n原因：").replaceFirst("过程是","\n过程：");
        }
        s = s.replaceAll("<br>", "").replaceAll(" -", " ").replaceAll("- ", " ");
        System.out.println(s);
        if (!s.contains("时间：")) {
            if (s.trim().startsWith("你提供")) {
                deep = 2;
            }
            if (deep < 2) {
                deep++;
                s = summary(content, deep);
            }
        }
        s = s.replaceAll("\\n", "<br/>").replaceAll("<br/><br/>", "<br/>").replaceAll("<br/><br/>", "<br/>").trim();
        if (s.startsWith("<br/>")) {
            s = s.replaceFirst("<br/>", "");
        }
        if (!s.contains("时间：")) {
            s = "";
        }
        return s;
    }

    /**
     * 单条问题*
     *
     * @param question 问题
     * @return 返回值
     */
    public static String singleChat(String question) {
        final List<ChatMessage> messages = new ArrayList<>();
        String endPoint;

        if (question.length() > 16000) {
            endPoint = END_POINT_128K;
        } else {
            endPoint = END_POINT_32K;
        }
        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(question).build();
        messages.add(systemMessage);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(endPoint)
                .messages(messages)
                .build();
        ChatCompletionResult chatCompletion = new ChatCompletionResult();
        try {
            chatCompletion = service.createChatCompletion(chatCompletionRequest);
        } catch (Exception e) {
            try {
                chatCompletion = service.createChatCompletion(chatCompletionRequest);
            } catch (Exception e1) {
                log.warn("分析文章内容接口超时！");
            }
        }
        List<ChatCompletionChoice> choices = chatCompletion.getChoices();
        return CollUtil.isNotEmpty(choices) ? choices.get(0).getMessage().getContent().toString() : "error";
    }


}
