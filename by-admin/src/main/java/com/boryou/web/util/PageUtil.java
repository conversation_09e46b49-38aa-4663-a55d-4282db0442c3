package com.boryou.web.util;

import com.boryou.common.core.page.PageDomain;
import com.boryou.common.core.page.TableSupport;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.sql.SqlUtil;
import com.github.pagehelper.PageHelper;

/**
 * 解决多个startPage()方法调用时，只有第一个生效的问题
 *
 * <AUTHOR>
 * @date 2024/1/3 10:43
 */
public class PageUtil {

    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 设置请求分页数据
     */
    public static void startPage(int pageNum, int pageSize) {
        if (pageSize == 0) {
            pageSize = Integer.MAX_VALUE;
        }
        PageHelper.startPage(pageNum, pageSize);
    }

}
