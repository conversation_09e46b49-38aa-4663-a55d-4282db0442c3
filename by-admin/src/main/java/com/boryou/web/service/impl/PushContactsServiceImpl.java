package com.boryou.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.web.domain.PushContacts;
import com.boryou.web.domain.vo.PushContactsVO;
import com.boryou.web.mapper.PushContactsMapper;
import com.boryou.web.service.IPushContactsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
@Slf4j
public class PushContactsServiceImpl extends ServiceImpl<PushContactsMapper, PushContacts> implements IPushContactsService {
    @Resource
    private PushContactsMapper pushContactsMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public List<PushContacts> selectByPushContactsList(PushContactsVO pushContacts) {
        //同一个部门的账号可以看到设置的联系人
        Long deptId = SecurityUtils.getLoginUser().getUser().getDept().getDeptId();
        //List<Long> deptIds = sysDeptMapper.selectManageDept(deptId);

        String username = pushContacts.getUsername();
        LambdaQueryWrapper<PushContacts> eq = new LambdaQueryWrapper<PushContacts>()
                .eq(PushContacts::getDelFlag, 0)
                .like(CharSequenceUtil.isNotBlank(username), PushContacts::getUsername, username)
                .eq(PushContacts::getDeptId, deptId)
                .orderByDesc(PushContacts::getUpdateTime);

        Integer type = pushContacts.getType();
        switch (type) {
            case 1:
                eq.ne(PushContacts::getEmail, "");
                eq.isNotNull(PushContacts::getEmail);
                break;
            case 2:
                eq.ne(PushContacts::getWxOpenId, "");
                eq.isNotNull(PushContacts::getWxOpenId);
                break;
            case 0:
            default:
                eq.ne(PushContacts::getPhone, "");
                eq.isNotNull(PushContacts::getPhone);
        }
        return this.list(eq);
    }

    @Override
    public int insertByPushContacts(PushContacts pushContacts) {
        pushContacts.setDelFlag(0);
        return pushContactsMapper.insertByPushContacts(pushContacts);
    }

    @Override
    public int updateByPushContacts(PushContacts pushContacts) {
        return pushContactsMapper.updateByPushContacts(pushContacts);
    }

    @Override
    public boolean deleteByPushContactsByIds(List<String> ids) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        //List<Long> deptIds = sysDeptMapper.selectManageDept(deptId);
        return this.update(new UpdateWrapper<PushContacts>()
                .set("del_flag", 1)
                .in("id", ids)
                .eq("dept_id", deptId));
    }

    @Override
    public boolean changeContacts(PushContactsVO pushContacts) {
        Integer type = pushContacts.getType();
        switch (type) {
            case 1:
                String email = pushContacts.getEmail();
                if (CharSequenceUtil.isBlank(email)) {
                    throw new CustomException("邮箱不能为空!");
                }
                break;
            case 2:
                String wxOpenId = pushContacts.getWxOpenId();
                if (CharSequenceUtil.isBlank(wxOpenId)) {
                    throw new CustomException("微信不能为空!");
                }
                break;
            case 0:
            default:
                String phone = pushContacts.getPhone();
                if (CharSequenceUtil.isBlank(phone)) {
                    throw new CustomException("手机号不能为空!");
                }
        }
        Long id = pushContacts.getId();
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        String user = sysUser.getUserName();
        Long userId = sysUser.getUserId();
        Long deptId = sysUser.getDeptId();
        //List<Long> deptIds = sysDeptMapper.selectManageDept(deptId);
        DateTime date = DateUtil.date();
        if (id == null) {
            //新增
            pushContacts.setDelFlag(0);
            pushContacts.setCreateBy(user);
            pushContacts.setCreateTime(date);
            pushContacts.setUpdateBy(user);
            pushContacts.setUpdateTime(date);
            pushContacts.setDeptId(deptId);
            pushContacts.setUserId(userId);
            return this.save(pushContacts);
        }
        PushContacts byId = this.getById(id);
        if (byId == null) {
            throw new CustomException("联系人不存在");
        }
        pushContacts.setUpdateBy(user);
        pushContacts.setUpdateTime(date);
        return this.updateById(pushContacts);
    }

    @Override
    public Map<Long, PushContacts> selectPushContactsMapByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return MapUtil.newHashMap();
        }
        Long deptId = SecurityUtils.getLoginUser().getUser().getDept().getDeptId();
        List<PushContacts> pushContacts = this.list(new LambdaQueryWrapper<PushContacts>().in(PushContacts::getId, ids)
                .eq(PushContacts::getDelFlag, 0)
                .eq(PushContacts::getDeptId, deptId));
        if (CollUtil.isEmpty(pushContacts)) {
            return MapUtil.newHashMap();
        }
        return pushContacts.stream().collect(Collectors.toMap(PushContacts::getId, v -> v, (k1, k2) -> k1));
    }

    @Override
    public Map<Long, PushContacts> selectAllPushContactsMapByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return MapUtil.newHashMap();
        }
        List<PushContacts> pushContacts = this.list(new LambdaQueryWrapper<PushContacts>().in(PushContacts::getId, ids)
                .eq(PushContacts::getDelFlag, 0));
        if (CollUtil.isEmpty(pushContacts)) {
            return MapUtil.newHashMap();
        }
        return pushContacts.stream().collect(Collectors.toMap(PushContacts::getId, v -> v, (k1, k2) -> k1));
    }

}
