//package com.boryou.web.core.config;
//
//import cn.hutool.core.text.CharSequenceUtil;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.Config;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
/// **
// * redisson配置
// *
// * <AUTHOR>
// */
//@Configuration
//public class RedissonConfig {
//
//    @Value("${spring.redis.redisson.mode}")
//    private String mode;
//    /**
//     * 仅仅用于sentinel模式。
//     */
//    //@Value("${spring.redis.redisson.masterName:}")
//    //private String masterName;
//
//    @Value("${spring.redis.redisson.address}")
//    private String address;
//    @Value("${spring.redis.username:}")
//    private String username;
//    @Value("${spring.redis.password:}")
//    private String password;
//    /**
//     * 数据库默认0
//     */
//    @Value("${spring.redis.database:0}")
//    private Integer database;
//
//    @Bean(destroyMethod = "shutdown")
//    @ConditionalOnMissingBean(RedissonClient.class)
//    public RedissonClient redissonClient() {
//        if (CharSequenceUtil.isBlank(password)) {
//            password = null;
//        }
//        if (CharSequenceUtil.isBlank(username)) {
//            username = null;
//        }
//        Config config = new Config();
//        if ("single".equals(mode)) {
//            config.useSingleServer()
//                    .setDatabase(database)
//                    .setUsername(username)
//                    .setPassword(password)
//                    .setAddress(address);
//        } else if ("cluster".equals(mode)) {
//            String[] clusterAddresses = address.split(",");
//            config.useClusterServers()
//                    //集群模式不支持多个数据库概念，默认db 0
//                    .setUsername(username)
//                    .setPassword(password)
//                    .addNodeAddress(clusterAddresses);
//        } else if ("sentinel".equals(mode)) {
//            //String[] sentinelAddresses = address.split(",");
//            //config.useSentinelServers()
//            //        .setDatabase(database)
//            //        .setPassword(password)
//            //        .setMasterName(masterName)
//            //        .addSentinelAddress(sentinelAddresses);
//        } else if ("master-slave".equals(mode)) {
//            String[] masterSlaveAddresses = address.split(",");
//            if (masterSlaveAddresses.length == 1) {
//                throw new IllegalArgumentException(
//                        "redis.redisson.address MUST have multiple redis addresses for master-slave mode.");
//            }
//            String[] slaveAddresses = new String[masterSlaveAddresses.length - 1];
//            System.arraycopy(masterSlaveAddresses, 1, slaveAddresses, 0, slaveAddresses.length);
//            config.useMasterSlaveServers()
//                    .setDatabase(database)
//                    .setPassword(password)
//                    .setMasterAddress(masterSlaveAddresses[0])
//                    .addSlaveAddress(slaveAddresses);
//        } else {
//            throw new IllegalArgumentException(mode);
//        }
//        return Redisson.create(config);
//    }
//}
