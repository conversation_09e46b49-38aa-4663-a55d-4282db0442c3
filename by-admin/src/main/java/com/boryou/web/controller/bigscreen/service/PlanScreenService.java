package com.boryou.web.controller.bigscreen.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DictUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.controller.bigscreen.entity.ScreenCount;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.*;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.area.service.AreaService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchAnalyseService;
import com.boryou.web.service.SearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PlanScreenService {
    private final PlanService planService;
    private final ConvertHandler convertHandler;
    private final SearchService searchService;
    private final AreaService areaService;
    private final SearchAnalyseService searchAnalyseService;

    public Integer total(SearchVO searchVO) {
        this.buildTime(searchVO, -1);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        return searchService.getRealTimeInfoCount(searchVO);
    }

    public JSONArray wordsAnalyse(SearchVO searchVO) {
        try {
            this.buildTime(searchVO, -1);
            this.buildType(searchVO);
            this.buildCommon(searchVO);
            searchVO.setPageSize(200);
            EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
            return searchAnalyseService.wordAnalyse(bo, searchVO.getPlanId());
        } catch (Exception e) {
            log.warn("PlanScreenService.wordsAnalyse错误, 传参: {}, 错误: {}", searchVO, e.getMessage());
            return JSONUtil.createArray();
        }
    }

    public List<ScreenCount> emotionAnalyse(SearchVO searchVO) {
        this.buildTime(searchVO, -1);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        Map<String, Integer> map = EsSearchUtil.emotionAnalyse(bo);
        List<ScreenCount> screenCountList = new ArrayList<>();
        String neutral = EmotionEnum.NEUTRAL.getValue().toString();
        if (map.containsKey(neutral)) {
            ScreenCount screenCount = new ScreenCount();
            screenCount.setName(EmotionEnum.NEUTRAL.getName());
            Integer i = map.get(neutral);
            screenCount.setValue(i);
            screenCountList.add(screenCount);
        }
        String negative = EmotionEnum.NEGATIVE.getValue().toString();
        if (map.containsKey(negative)) {
            ScreenCount screenCount = new ScreenCount();
            screenCount.setName(EmotionEnum.NEGATIVE.getName());
            Integer i = map.get(negative);
            screenCount.setValue(i);
            screenCountList.add(screenCount);
        }
        String positive = EmotionEnum.POSITIVE.getValue().toString();
        if (map.containsKey(positive)) {
            ScreenCount screenCount = new ScreenCount();
            screenCount.setName(EmotionEnum.POSITIVE.getName());
            Integer i = map.get(positive);
            screenCount.setValue(i);
            screenCountList.add(screenCount);
        }
        return screenCountList;
    }

    public List<ScreenCount> map(SearchVO searchVO) {
        this.buildTime(searchVO, -1);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        bo.setContentAreaCode(null);
        Map<String, Integer> map = EsSearchUtil.areaMap(bo);
        if (MapUtil.isEmpty(map)) {
            return Collections.emptyList();
        }
        List<Area> nextAreaList = areaService.getNextArea("0");
        if (CollUtil.isEmpty(nextAreaList)) {
            return Collections.emptyList();
        }
        List<ScreenCount> screenCountList = new ArrayList<>();
        for (Area area : nextAreaList) {
            int id = area.getId();
            String shortName = area.getShortName();
            String idStr = String.valueOf(id);
            long value = 0L;
            if (map.containsKey(idStr)) {
                value = map.get(idStr);
            }
            ScreenCount screenCount = new ScreenCount();
            screenCount.setName(shortName);
            screenCount.setValue(value);
            screenCount.setAreaCode(idStr);
            screenCountList.add(screenCount);
        }
        screenCountList.sort((o1, o2) -> Math.toIntExact(o2.getValue() - o1.getValue()));
        return screenCountList;
    }

    private void buildTime(SearchVO searchVO, int offset) {
        String startTime = searchVO.getStartTime();
        String endTime = searchVO.getEndTime();
        if (StrUtil.isBlankIfStr(startTime) || StrUtil.isBlankIfStr(endTime)) {
            DateTime date = DateUtil.date();
            String endTime1 = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
            String startTime1 = DateUtil.format(DateUtil.offsetDay(date, offset), "yyyy-MM-dd HH:mm:ss");
            searchVO.setStartTime(startTime1);
            searchVO.setEndTime(endTime1);
        }
    }

    public List<Plan> plan() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return planService.list(new LambdaQueryWrapper<Plan>().in(Plan::getUserId, userIds)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1)
                .eq(Plan::getHistoryFlag, 0)
                .orderByDesc(Plan::getUpdateTime));
    }

    public ScreenModelVO weekType(SearchVO searchVO) {
        this.buildTime(searchVO, -7);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        GraphModelVO graphModelVO = searchAnalyseService.timeType(bo);
        if (graphModelVO == null) {
            return null;
        }
        List<String> xxData = graphModelVO.getXs();
        List<SeriesVO<String>> seriesList = graphModelVO.getSeriesList();
        int size = seriesList.size();
        List<AimDataVO> aimData = new ArrayList<>();
        List<List<String>> yyData = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            SeriesVO<String> seriesVO = seriesList.get(i);
            String name = seriesVO.getName();
            List<String> data = seriesVO.getData();
            yyData.add(data);
            AimDataVO aimDataVO = new AimDataVO();
            aimDataVO.setName(name);
            aimDataVO.setId(i + 1);
            aimData.add(aimDataVO);
        }
        ScreenModelVO screenModelVO = new ScreenModelVO();
        screenModelVO.setAimData(aimData);
        screenModelVO.setXxData(xxData);
        screenModelVO.setYyData(yyData);
        return screenModelVO;
    }

    private void buildType(SearchVO searchVO) {
        List<SysDictData> sysMediaType = DictUtils.getDictCache("sys_media_type");
        if (CollUtil.isEmpty(sysMediaType)) {
            return;
        }
        List<String> collect = sysMediaType.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        String join = CollUtil.join(collect, ",");
        searchVO.setType(join);
    }

    public List<EsBean> hotInfo(SearchVO searchVO) {
        this.buildTime(searchVO, -1);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        searchVO.setPageNum(1);
        searchVO.setPageSize(50);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return EsSearchUtil.relatedHotArticle(bo);
    }

    public List<EsBean> realTime(SearchVO searchVO) {
        Integer offset = searchVO.getOffset();
        if (offset == null) {
            offset = -1;
        }
        this.buildTime(searchVO, offset);
        this.buildType(searchVO);
        this.buildCommon(searchVO);
        searchVO.setPageNum(1);
        searchVO.setPageSize(100);
        searchVO.setForward("1");
        PageResult<EsBean> pageResult = new PageResult<>();
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan != null) {
            EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
            pageResult = searchService.search(bo);
        }
        //在代码使用MD5去重
        return pageResult.stream()
                .collect(Collectors.toMap(
                        EsBean::getMd5, // 键是md5
                        item -> item, // 值是record本身
                        // 合并函数，保留time较晚的record
                        (existing, replacement) -> existing.getPublishTime().after(replacement.getPublishTime()) ? existing : replacement
                ))
                .values() // 获取值的集合
                .stream() // 对values集合创建stream
                .sorted(Comparator.comparing(EsBean::getPublishTime)) // 根据时间排序
                .collect(Collectors.toList()); // 转换回List
    }

    private void buildCommon(SearchVO searchVO) {
        this.judgePlan(searchVO);
        searchVO.setIsOriginal(false);
        searchVO.setSort(3);
        searchVO.setNoSpam("0");
    }

    private void judgePlan(SearchVO searchVO) {
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan == null) {
            throw new CustomException("方案不存在!");
        }
    }

}
