package com.boryou.web.controller.common;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.config.BoryouConfig;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.utils.FtpJSch;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.file.FileUtils;
import com.boryou.framework.config.ServerConfig;
import com.boryou.manage.domain.ByUploadFile;
import com.boryou.manage.service.IByUploadFileService;
import com.boryou.submit.vo.AreaTree;
import com.boryou.utils.AreaUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController {
    public static final String AUDIO_PATH = "/data/webapps/zongzhi/zz-cctv-check/data/";
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);
    @Autowired
    private ServerConfig serverConfig;
    @Autowired
    private IByUploadFileService iByUploadFileService;
    @Resource
    private FtpJSch ftpJSch;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = BoryouConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

//    /**
//     * 通用上传请求
//     */
//    @PostMapping("/common/upload")
//    public AjaxResult uploadFile(MultipartFile file) throws Exception {
//        try {
//            ByUploadFile byUploadFile = new ByUploadFile();
//            byUploadFile.setId(IdUtil.nextLong());
//            byUploadFile.setFileSize(file.getSize());
//            String fileName = file.getContentType().substring(file.getContentType().indexOf("/")).replace("/", ".");
//            byUploadFile.setFileType(fileName);
//            byUploadFile.setFileName(file.getOriginalFilename());
//            // 上传文件路径
//            String filePath = BoryouConfig.getUploadPath();
//            // 上传并返回新文件名称
//            fileName = FileUploadUtils.upload(filePath, file);
//            String url = serverConfig.getUrl() + fileName;
//            byUploadFile.setAddress(fileName);
//            iByUploadFileService.insertByUploadFile(byUploadFile);
//            AjaxResult ajax = AjaxResult.success();
//            JSONObject data = new JSONObject();
//            data.putOnce("fileId",byUploadFile.getId().toString());
//            data.putOnce("fileName",byUploadFile.getFileName());
//            data.putOnce("url",url);
//            ajax.put("data", data);
//            return ajax;
//        } catch (Exception e) {
//            return AjaxResult.error(e.getMessage());
//        }
//    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            ByUploadFile byUploadFile = new ByUploadFile();
            String name = file.getOriginalFilename();
            assert name != null;
            String substring = name.substring(name.lastIndexOf("."));
            InputStream inputStream = file.getInputStream();
            String filePath = AUDIO_PATH + IdUtil.getSnowflakeNextId() + substring;
            File fileGet = new File(filePath);
            FileUtil.writeFromStream(inputStream, fileGet);
            String upload = ftpJSch.upload(filePath);
            byUploadFile.setFileName(name);
            byUploadFile.setId(IdUtil.getSnowflakeNextId());
            byUploadFile.setFileSize(file.getSize());
            String fileName = file.getContentType().substring(file.getContentType().indexOf("/")).replace("/", ".");
            byUploadFile.setFileType(fileName);
            String url = "https://file.boryou.com/neter/" + upload;
            byUploadFile.setAddress(url);
            iByUploadFileService.insertByUploadFile(byUploadFile);
            AjaxResult ajax = AjaxResult.success();
            JSONObject data = new JSONObject();
            data.putOnce("fileId", byUploadFile.getId().toString());
            data.putOnce("fileName", byUploadFile.getFileName());
            data.putOnce("url", url);
            ajax.put("data", data);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/uploads")
    public AjaxResult uploadFile(MultipartFile[] file) throws Exception {
        try {
            AjaxResult ajax = AjaxResult.success();
            JSONArray array = new JSONArray();
            for (MultipartFile aFile : file) {
                ByUploadFile byUploadFile = new ByUploadFile();
                String name = aFile.getOriginalFilename();
                assert name != null;
                String substring = name.substring(name.lastIndexOf("."));
                InputStream inputStream = aFile.getInputStream();
                String filePath = AUDIO_PATH + IdUtil.getSnowflakeNextId() + substring;
                File fileGet = new File(filePath);
                FileUtil.writeFromStream(inputStream, fileGet);
                String upload = ftpJSch.upload(filePath);
                byUploadFile.setFileName(name);
                byUploadFile.setId(IdUtil.getSnowflakeNextId());
                byUploadFile.setFileSize(aFile.getSize());
                String fileName = aFile.getContentType().substring(aFile.getContentType().indexOf("/")).replace("/", ".");
                byUploadFile.setFileType(fileName);
                String url = "https://file.boryou.com/neter/" + upload;
                byUploadFile.setAddress(url);
                iByUploadFileService.insertByUploadFile(byUploadFile);
                JSONObject data = new JSONObject();
                data.putOnce("fileId", byUploadFile.getId().toString());
                data.putOnce("fileName", byUploadFile.getFileName());
                data.putOnce("url", url);
                array.add(data);
            }

            ajax.put("data", array);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

//    /**
//     * 通用上传请求
//     */
//    @PostMapping("/common/uploads")
//    public AjaxResult uploadFile(MultipartFile[] file) throws Exception {
//        try {
//            AjaxResult ajax = AjaxResult.success();
//            JSONArray array = new JSONArray();
//            for (MultipartFile aFile : file) {
//                ByUploadFile byUploadFile = new ByUploadFile();
//                byUploadFile.setId(IdUtil.nextLong());
//                byUploadFile.setFileSize(aFile.getSize());
//                String fileName = aFile.getContentType().substring(aFile.getContentType().indexOf("/")).replace("/", ".");
//                byUploadFile.setFileType(fileName);
//                byUploadFile.setFileName(aFile.getOriginalFilename());
//                // 上传文件路径
//                String filePath = BoryouConfig.getUploadPath();
//                // 上传并返回新文件名称
//                fileName = FileUploadUtils.upload(filePath, aFile);
//                String url = serverConfig.getUrl() + fileName;
//                byUploadFile.setAddress(fileName);
//                iByUploadFileService.insertByUploadFile(byUploadFile);
//
//                JSONObject data = new JSONObject();
//                data.putOnce("fileId", byUploadFile.getId().toString());
//                data.putOnce("fileName", byUploadFile.getFileName());
//                data.putOnce("url", url);
//                array.add(data);
//            }
//
//            ajax.put("data", array);
//            return ajax;
//        } catch (Exception e) {
//            return AjaxResult.error(e.getMessage());
//        }
//    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = BoryouConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 根据组织等级和地域查询地域树,一次返回所有的*
     */
    @GetMapping("/deptAreaTreeOld/{areaId}")
    public AjaxResult getDeptAreaTree(@PathVariable("areaId") String areaId) {
        int deep = 3;
        AjaxResult treeListR = AreaUtil.getTreeList(areaId, deep);
//        AreaUtil.improveTree(treeListR);
        return AjaxResult.success(JSONUtil.toList(JSONUtil.toJsonStr(treeListR.getData()), AreaTree.class));
    }


    /**
     * 自定义完善树内容---可调整
     */
    @GetMapping("/deptAreaTree/{areaId}")
    public AjaxResult deptAreaTreeNew(@PathVariable("areaId") String areaId) {
        return AjaxResult.success(AreaUtil.areaTreeResult);
    }

    //图片转换接口
    @GetMapping("/common/url/image")
    public void convertImg(String url, HttpServletResponse response)
            throws Exception {
        try {
            HttpRequest httpRequest = HttpRequest.get(url);
            if (url.contains("sina")) {
                httpRequest.header("Referer", "https://www.weibo.com/");//只有微博需要加referer
            }
            response.setDateHeader("Expires", 0);
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
            response.addHeader("Cache-Control", "post-check=0, pre-check=0");
            response.setHeader("Pragma", "no-cache");
            response.setContentType("image/jpeg");
            HttpResponse httpResponse = httpRequest.execute();
            byte[] imageData = httpResponse.bodyBytes();
            response.setContentType("image/jpeg");  // 根据图片类型设置
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(imageData);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("下载文件失败", e);
        }
    }

}
