package com.boryou.web.controller.common.entity.vo;

import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description ES数据导出
 * @date 2024/4/22 16:38
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EsBeanExportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标题*
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 正文*
     */
    @Excel(name = "正文")
    private String text;

    /**
     * 媒体类型名称
     */
    @Excel(name = "媒体类型")
    private String typeName;

    /**
     * 发文时间*
     */
    @Excel(name = "发文时间")
    private String publishTime;

    /**
     * 原文链接*
     */
    @Excel(name = "原文链接")
    private String url;

    /**
     * host*
     */
    @Excel(name = "来源网站")
    private String host;

    /**
     * (账号/作者)昵称*
     */
    @Excel(name = "作者")
    private String author;

//    /**
//     * 站点地域
//     */
//    @Excel(name = "站点地域")
//    private String siteAreaCodeName;

    /**
     * 站点标签
     */
    @Excel(name = "站点标签")
    private String siteMeta;

    /**
     * 情感标识
     */
    @Excel(name = "信息属性")
    private String emotionFlag;

    /**
     * 是否为原创
     */
    @Excel(name = "原创/转发")
    private String isOriginal;

    /**
    * 精准地域
    */
    @Excel(name = "精准地域")
    private String contentAreaCodeName;

    /**
    * 信源级别   用 \ 代表未定义级别
    */
    @Excel(name = "信源级别")
    private String accountLevel;

    /**
     * 相似文章数*
     */
    @Excel(name = "相似文章数")
    private Integer similarCount;

    /**
     * 涉及关键词
     */
    @Excel(name = "涉及关键词")
    private String hitWords;

    /**
     * 粉丝数*
     */
    @Excel(name = "粉丝数")
    private Integer fansNum;

    /**
     * 转发数*
     */
    @Excel(name = "转发数")
    private Integer reprintNum;

    /**
     * 评论数*
     */
    @Excel(name = "评论数")
    private Integer commentNum;

    /**
     * 点赞数*
     */
    @Excel(name = "点赞数")
    private Integer likeNum;

}
