package com.boryou.web.controller.common.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;


/**
 * http工具类
 * 建议所有请求都加上默认的超时时间设置和链接超时，根据alibaba开发建议
 *
 * <AUTHOR>
 * @date 2024-08-16 10:52
 */
@Slf4j
public class HttpUtils {
    /**
     * 用于设置从连接中读取数据的超时时间
     */
    public final static int readTimeOut = 5000;
    /**
     * 用于设置建立连接的超时时间
     */
    public final static int connectTimeout = 2000;

    /**
     * 最简单的get请求
     */
    public static String get(String url) {
        return get(url, readTimeOut, connectTimeout);
    }

    /**
     * 指定超时和读取时间的get请求
     */
    public static String get(String url, int readTimeOut, int connectTimeout) {
        try {
            return HttpUtil.createGet(url).setReadTimeout(readTimeOut).setConnectionTimeout(connectTimeout).execute().body();
        } catch (Exception e) {
            log.error("网络通信异常:{}", e.getMessage());
            throw new CustomException("网络异常");
        }
    }

    /**
     * get请求返回JSONobject对象
     */
    public static JSONObject getObj(String url) {
        return JSONUtil.parseObj(get(url));
    }

    /**
     * get请求返回指定class对象
     */
    public static <T> T getObj(String url, Class<T> beanClass) {
        return JSONUtil.toBean(get(url), beanClass);
    }

    /**
     * get请求返回指定List对象
     */
    public static <T> List<T> getList(String url, Class<T> beanClass) {
        return JSONUtil.toList(get(url), beanClass);
    }

    /**
     * get请求取Json中的key数组集合返回
     */
    public static <T> List<T> getList(String url, Class<T> beanClass, String getArrKey) {
        return JSONUtil.toList(getObj(url).getJSONArray(getArrKey), beanClass);
    }

    /**
     * 最简单的post请求
     */
    public static HttpRequest post(String url) {
        return post(url, readTimeOut, connectTimeout);
    }

    /**
     * 最简单的post请求  带读取和读取超时时间参数
     */
    public static HttpRequest post(String url, int readTimeout, int connectTimeout) {
        try {
            return HttpUtil.createPost(url).header("contentType", "application/json").setReadTimeout(readTimeout).setConnectionTimeout(connectTimeout);
        } catch (Exception e) {
            log.error("网络通信异常:{}", e.getMessage());
            throw new CustomException("网络异常");
        }
    }

    /**
     * 最简单的post请求 带post的json的字符串格式参数
     */
    public static String post(String url, String jsonParamStr) {
        HttpRequest postRequest = post(url);
        postRequest.body(jsonParamStr);
        return postRequest.execute().body();
    }


    public static JSONObject postObj(String url, String jsonParamStr) {
        return JSONUtil.parseObj(post(url, jsonParamStr));
    }

    public static Map postMap(String url, String jsonParamStr) {
        return JSONUtil.toBean(post(url, jsonParamStr), Map.class);
    }


}

