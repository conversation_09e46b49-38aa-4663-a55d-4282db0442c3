package com.boryou.web.controller.system;

import cn.hutool.core.map.MapUtil;
import com.boryou.common.annotation.Log;
import com.boryou.common.constant.UserConstants;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.vo.OpenCustomizationVO;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.service.ISysDeptService;
import com.boryou.web.module.message.domain.vo.DeptMessage;
import com.boryou.web.module.message.service.IMessageTemplateService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private IMessageTemplateService messageTemplateService;

    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        List<DeptMessage> deptTemplatesByIds = messageTemplateService.getDeptTemplatesByIds(depts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        Map<Long, Long> collect = deptTemplatesByIds.stream().collect(Collectors.toMap(DeptMessage::getDeptId, DeptMessage::getTemplateId));
        if (MapUtil.isNotEmpty(collect)) {
            for (SysDept sysDept : depts) {
                sysDept.setMessageTemplateId(collect.get(sysDept.getDeptId()));
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        Iterator<SysDept> it = depts.iterator();
        while (it.hasNext()) {
            SysDept d = (SysDept) it.next();
            if (d.getDeptId().intValue() == deptId
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + "")) {
                it.remove();
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId) {
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptListAuth(dept);
        return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
    }

    /**
     * 加载对应角色部门列表树
     */
    @GetMapping(value = "/roleDeptTreeselect/{roleId}")
    public AjaxResult roleDeptTreeselect(@PathVariable("roleId") Long roleId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.buildDeptTreeSelect(depts));
        return ajax;
    }

    /**
     * 新增部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        dept.setCreateBy(SecurityUtils.getUsername());

        OpenCustomizationVO vo = new OpenCustomizationVO();
        BeanUtils.copyProperties(dept, vo);
        return toAjax(deptService.insertDept(dept, vo));
    }

    /**
     * 修改部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(dept.getDeptId())) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())
                && deptService.selectNormalChildrenDeptById(dept.getDeptId()) > 0) {
            return AjaxResult.error("该部门包含未停用的子部门！");
        }
        dept.setUpdateBy(SecurityUtils.getUsername());
        OpenCustomizationVO vo = new OpenCustomizationVO();
        BeanUtils.copyProperties(dept, vo);
        return toAjax(deptService.updateDept(dept, vo));
    }

    /**
     * 删除部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return AjaxResult.error("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return AjaxResult.error("部门存在用户,不允许删除");
        }
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表--仅限当前人员的所在部门及子部门
     */
    @GetMapping("/treeselectForPermi")
    public AjaxResult treeselectForPermi() {
        List<SysDept> depts = deptService.selectPermiDeptList();
        return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
    }

    /**
     * 获取部门下拉树列表--仅限当前人员的所在部门及子部门
     */
    @GetMapping("/treeselectOrgForPerson")
    public AjaxResult treeselectOrgForPerson() {
        List<TreeSelect> depts = deptService.selectTreeOrgForPerson();
        return AjaxResult.success(depts);
    }

    /**
     * 获取部门下拉树列表--仅限当前人员的所在部门及子部门--只获取下级一级
     */
    @GetMapping("/treeselectOrgForPersonTo")
    public AjaxResult treeselectOrgForPersonTo() {
        List<TreeSelect> depts = deptService.selectTreeOrgForPerson();
        //去获取当前用户的组织机构及下级机构--团队模式下
        for (TreeSelect treeSelect : depts) {
            List<TreeSelect> child2 = treeSelect.getChildren();
            //将顶级组织下的人员删除
            Iterator<TreeSelect> it2 = child2.iterator();
            TreeSelect ts2;
            while (it2.hasNext()) {
                ts2 = it2.next();
                if (ts2.getPanelPointFlag() == 1) {
                    it2.remove();
                }
            }
            for (TreeSelect treeSelect2 : child2) {
                List<TreeSelect> child3 = treeSelect2.getChildren();
                if (StringUtils.isNotNull(child3)) {
                    Iterator<TreeSelect> it = child3.iterator();
                    TreeSelect ts;
                    while (it.hasNext()) {
                        ts = it.next();
                        if (ts.getPanelPointFlag() == 0) {
                            it.remove();
                        }
                    }
                }
            }
        }
        return AjaxResult.success(depts);
    }

    /**
     * 获取当前传入人员部门的父级部门的子级列表
     */
    @GetMapping("/parentList")
    public AjaxResult parentList(Long userId) {
        List<SysDept> depts = deptService.selectDeptListForParent(userId);
        return AjaxResult.success(depts);
    }

    /**
     * 获取当前传入人员的部门的子级列表
     */
    @GetMapping("/currentList")
    public AjaxResult currentList(Long userId) {
        List<SysDept> depts = deptService.selectDeptListForPerson(userId);
        return AjaxResult.success(depts);
    }
}
