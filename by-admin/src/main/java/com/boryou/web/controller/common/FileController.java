package com.boryou.web.controller.common;

import com.boryou.common.annotation.Secret;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.manage.domain.File;
import com.boryou.manage.domain.vo.FileV2VO;
import com.boryou.manage.service.FileService;
import com.boryou.manage.service.MinioFileService;
import com.boryou.service.SFileService;
import io.minio.errors.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/20 14:55
 */
@RestController
@RequestMapping("file")
public class FileController extends BaseController {
    @Resource
    private MinioFileService minioFileService;
    @Resource
    private SFileService sfileService;
    @Resource
    private FileService fileService;
    @Value("${minio.bucket-name}")
    private String bucket;

    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestPart("file") MultipartFile[] file) {
        List<String> fileTag = minioFileService.uploadFiles(file);
        return AjaxResult.success(fileTag);
    }

    @GetMapping("/get")
    public void fileName(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        File file = fileService.selectFileByFileName(fileName);
        minioFileService.downloadFile(file, response);
    }

    /**
     * 普通文件上传
     */
    @PostMapping("/uploadMinio")
    public AjaxResult uploadFileMinio(@RequestParam("file") MultipartFile file) {
        try {
            return AjaxResult.success("上传成功", sfileService.uploadFile(file));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 下载 minio 文件
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/downloadFile/{id}")
    public void downloadFile(@PathVariable String id, HttpServletResponse response) throws Exception {
        sfileService.downloadFile(id, response);
    }

    /**
     * 根据文件 id 获取文件预览地址，默认七天
     *
     * @param id
     * @return
     */
    @GetMapping("/getUrlById/{id}")
    public AjaxResult getUrlById(@PathVariable("id") Long id) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return AjaxResult.success(fileService.getUrlById(id));
    }

    /**
     * 根据文件 id 获取文件预览地址，一天后过期
     *
     * @param fileIds
     * @return
     */
    @Secret(Long[].class)
    @PostMapping("/getUrlByFileIds/{fileIds}")
    public TableDataInfo getUrlByFileIds(@PathVariable("fileIds") Long[] fileIds) {
        return getDataTable(sfileService.getUrlByFileIds(fileIds));
    }

    @PostMapping("/getUrlByIds")
    public AjaxResult getUrlByFileIds(@RequestBody List<String> files) {
        List<FileV2VO> fileVOS = fileService.getUrlByFileIds(files);
        return AjaxResult.success(fileVOS);
    }

    @GetMapping("/public/query/{fileId}")
    public void filePublic(@PathVariable(name = "fileId") String fileId, HttpServletResponse response) {
        fileService.filePublic(fileId, response);
    }

}
