package com.boryou.web.controller.xtest;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.domain.dto.SimilarityDTO;
import com.boryou.web.service.impl.SimilarityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/xtest/similar")
public class XtestController {

    private final SimilarityService similarityService;

    @PostMapping("/test1")
    public AjaxResult test1(@RequestBody SimilarityDTO similarityDTO) {
        String text = similarityDTO.getText();
        List<String> textList = similarityDTO.getTextList();
//        List<Boolean> similarityList = similarityService.getSimilarityList(text, textList, null);
        List<Boolean> similarityList = similarityService.getSimilarityList(text, textList, 0.6);
        return AjaxResult.success(similarityList);
    }

    @PostMapping("/test2")
    public AjaxResult test2(@RequestBody SimilarityDTO similarityDTO) {
        String text = similarityDTO.getText();
        List<String> textList = similarityDTO.getTextList();
        List<Double> similarityValueList = similarityService.getSimilarityValueList(text, textList);
        return AjaxResult.success(similarityValueList);
    }

}
