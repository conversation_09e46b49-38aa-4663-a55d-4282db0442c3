package com.boryou.web.controller.plan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.boryou.common.annotation.Log;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.dto.PlanManageQueryDTO;
import com.boryou.web.domain.vo.*;
import com.boryou.web.service.PlanService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方案Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
public class PlanController extends BaseController {
    @Resource
    private PlanService planService;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @PostMapping("/plan/list")
    public AjaxResult planList(@RequestBody Plan plan) {
        List<Plan> planList = planService.selectByPlanList(plan);
        return AjaxResult.success(planList);
    }

    @PostMapping("/plan/one")
    public AjaxResult planOne(@RequestBody PlanVO planVO) {
        Plan plan = planService.planOne(planVO);
        return AjaxResult.success(plan);
    }

    /**
     * 重点案件是plan_main的type=1，并且by_plan表的type为-2的，所有用户重点案件的文件夹都是-2
     *
     * @return com.boryou.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2024/10/11 16:33
     **/
    @PostMapping("/plan/tree")
    public AjaxResult planTree(@RequestBody PlanVO planVO) {
        List<PlanTreeVO> planTreeVO = planService.planTree(planVO);
        return AjaxResult.success(planTreeVO);
    }

    @Log(title = "新增方案", businessType = BusinessType.INSERT)
    @PostMapping("/plan/add")
    public AjaxResult addPlan(@RequestBody PlanVO plan) {
        if ((plan.getSearchMode() == 0 && StringUtils.isAllEmpty(plan.getKw1(), plan.getKw2(), plan.getKw3()))
                || (plan.getSearchMode() == 1 && StringUtils.isAllEmpty(plan.getHighExcludeWord(), plan.getHighMonitorWord()))) {
            return AjaxResult.error("关键词不能为空");
        }
        if (planService.addPlan(plan)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @Log(title = "方案设置更新/删除", businessType = BusinessType.UPDATE)
    @PostMapping("/plan/update")
    public AjaxResult updatePlan(@RequestBody PlanVO plan) {
        if (planService.updatePlan(plan)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/plan/movePlan")
    public AjaxResult movePlan(@RequestBody PlanVO plan) {
        return AjaxResult.check(planService.movePlan(plan));
    }

    @PostMapping("/plan/move")
    public AjaxResult sortPlan(@RequestBody SortVO sortVO) {
        if (planService.sortPlan(sortVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 移入重点案件
     */
    @PostMapping("/planMain/update")
    public AjaxResult updatePlanMain(@RequestBody PlanMainVO planMainVO) {
        if (planService.updatePlanMain(planMainVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 用户偏好设置
     *
     * @return
     */
    @GetMapping("plan/userHabits")
    public AjaxResult getUserHabits() {
        String key = Constants.USER_HABITS_KEY + SecurityUtils.getUserId();

        String jsonStr = redisTemplate.opsForValue().get(key);
        JSONObject res = JSONObject.parseObject(jsonStr);

        if (ObjectUtils.isEmpty(res)) {
            Map<String, Object> initExportHabit = initExportHabit();
            redisTemplate.opsForValue().set(key, JSON.toJSONString(initExportHabit));
            return AjaxResult.success(initExportHabit);
        }

        return AjaxResult.success(res);
    }

    /**
     * 用户偏好设置
     *
     * @return
     */
    @PostMapping("plan/userHabits")
    public AjaxResult updateUserHabits(@RequestBody Map<String, Object> userHabits) {
        String key = Constants.USER_HABITS_KEY + SecurityUtils.getUserId();
        redisTemplate.delete(key);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(userHabits));
        return AjaxResult.success();
    }

    /**
     * 初始化用户导出习惯
     *
     * @return
     */
    private Map<String, Object> initExportHabit() {
        Map<String, Object> res = new HashMap<>();
        Map<String, Object> now = new HashMap<>();
        Map<String, Object> history = new HashMap<>();

        // 当前方案
        List<UserHabitsVO> nowList = new ArrayList<>();
        nowList.add(new UserHabitsVO("舆情数据", "1", true));
        nowList.add(new UserHabitsVO("统计分析", "2", true));
        nowList.add(new UserHabitsVO("方案设置", "3", true));
        nowList.add(new UserHabitsVO("定向信源", "4", true));
        nowList.add(new UserHabitsVO("预警设置", "5", true));
//        nowList.add(new UserHabitsVO("报告设置", "6", true));
//        nowList.add(new UserHabitsVO("事件脉络", "6", true));
        nowList.add(new UserHabitsVO("报告设置", "7", true));
        now.put("list", nowList);
        now.put("fold", false);

        // 历史方案去除定向信源
        List<UserHabitsVO> historyList = new ArrayList<>();
        historyList.add(new UserHabitsVO("舆情数据", "1", true));
        historyList.add(new UserHabitsVO("统计分析", "2", true));
        historyList.add(new UserHabitsVO("方案设置", "3", true));
        historyList.add(new UserHabitsVO("预警设置", "5", true));
//        historyList.add(new UserHabitsVO("事件脉络", "6", true));
        history.put("list", historyList);
        history.put("fold", false);


        res.put("now", now);
        res.put("history", history);
        return res;
    }

    /**
     * 方案管理方案分类查询
     *
     * @param query
     * @return
     */
    @PostMapping("/plan/manage/list")
    public TableDataInfo manageList(@RequestBody PlanManageQueryDTO query) {
        return getDataTable(planService.manageList(query));
    }

    /**
     * 复制方案
     *
     * @param planId
     * @return
     */
    @GetMapping("/plan/manage/copyPlan")
    public AjaxResult copyPlan(Long planId) {
        return AjaxResult.optionResult(planService.copyPlan(planId));
    }

    /**
     * 移动方案位置-不能跨文件夹
     */
    @Log(title = "拖拽移动方案位置", businessType = BusinessType.INSERT)
    @PostMapping("/plan/updatePlanInfo")
    public AjaxResult updatePlanInfo(@RequestBody PlanVO plan) {
        return AjaxResult.check(planService.updatePlanInfo(plan));
    }

    @PostMapping("/plan/manage/all")
    public AjaxResult allPlan() {
        return AjaxResult.success(planService.selectAllPlan());
    }

    /**
     * 历史方案-事件追踪
     *
     * @return
     */
    @GetMapping("/plan/tracking/{id}")
    public AjaxResult planTracking(@PathVariable(value = "id") String id) {
        return AjaxResult.success(planService.planTracking(id));
    }
}
