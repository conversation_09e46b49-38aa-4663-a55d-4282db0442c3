//package com.boryou.web.controller.bigscreen.bo;
//
//import lombok.Data;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * @description ES搜索BO
// * <AUTHOR>
// * @date 2024/4/22 14:00
// */
//@Data
//public class EsSearchBO {
//    private static final long serialVersionUID = 1L;
//    /**
//     * 文章id*
//     */
//    private String id;
//    /**
//     * 排除id*
//     */
//    private String excludeId;
//    /**
//     * 媒体类型
//     */
//    private String type;
//    /**
//     * 情感倾向
//     */
//    private String emotionFlag;
//    /**
//     * 时间类型（0发文时间，1采集时间）
//     */
//
//    private int timeType;
//    /**
//     * 开始时间
//     */
//    private String startTime;
//    /**
//     * 结束时间
//     */
//    private String endTime;
//    /**
//     * 站点地域地域码
//     */
//    private String siteAreaCode;
//    /**
//     * 内容地域 contentAreaCode
//     */
//    private String contentAreaCode;
//    /**
//     * 是否原创 true false null
//     */
//    private Boolean isOriginal;
//    /**
//     * 是否垃圾 true false null
//     */
//    private Boolean isSpam;
//    /**
//     *关键词1
//     */
//    private String keyWord1;
//    /**
//     *关键词2
//     */
//    private String keyWord2;
//    /**
//     *关键词3
//     */
//    private String keyWord3;
//    /**
//     *关键词4
//     */
//    private String keyWord4;
//
//    /**
//     * 二次搜索词
//     */
//    private String quadraticWord;
//    /**
//     * 二次搜索过滤词
//     */
//    private String quadraticFilterWord;
//    /**
//     * 排除词
//     */
//    private String excludeWord;
//    /**
//     * 指定站点
//     */
//    private String host;
//    /**
//     * 指定短视频站点
//     */
//    private String videoHost;
//    /**
//     * 排除站点
//     */
//    private String excludeHost;
//    /**
//     * 关键词位置
//     */
//    private String searchPosition;
//    /**
//     * 排序方式
//     */
//    private String sortType;
//
//
//    /**
//     * 作者
//     */
//    private String author;
//    /**
//     * 大V级别 0达人  1蓝v  2红v  3橙v  4普通用户
//     */
//    private String accountLevel;
//    /**
//     * 内容类型（文本，图片，视频，链接，音频，游戏，文件）
//     */
//    private String contentForm;
//
//    /**
//     * 站点标签
//     */
//    private String siteMeta;
//
//    /**
//     * 内容标签
//     */
//    private String contentMeta;
//
//    /**
//     * 搜索方式 1 专业模式  0 普通模式
//     */
//    private int configSelect;
//
//    /**
//     * 专业搜索词
//     */
//    private String proWord;
//
//    /**
//    * 定向信源设置 0 不使用  1 定向选择  2 定向排除
//    */
//    private String sourceSetting;
//
//    /**
//    * 定向信源
//    */
//    private Map<String, String> sourceMap;
//    /**
//    * 使用精确查询
//    */
//    private Boolean accurate = true;
//
//    private String url;
//
//    /**
//     *  需要分组的字段数量
//     */
//    private int aggSize=10;
//
//    private int pageSize = 10;
//
//    private int pageNum = 1;
//    /**
//     * 项目接口调用标识
//     */
//    private Integer projectType;
//}
