package com.boryou.web.controller.holiday;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.service.HolidayService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class HolidayController extends BaseController {
    @Resource
    private HolidayService holidayService;

    @GetMapping("/holiday/save")
    public AjaxResult holidaySave() {
        boolean b = holidayService.saveHoliday();
        return toAjax(b);
    }

}
