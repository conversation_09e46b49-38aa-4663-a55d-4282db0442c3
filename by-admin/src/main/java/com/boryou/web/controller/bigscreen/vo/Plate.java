package com.boryou.web.controller.bigscreen.vo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户版块（首页监测规则）
 *
 * <AUTHOR>
 * @date 2016-9-18 下午6:50:50
 */
public class Plate {

    private String id;
    private String userId; // 用户ID
    private String name; // 版块名称
    private int sortNum; // 排序号
    private int state = -1; // 状态 0-禁用 1-启用
    private int timeRegion = 1; // 时间范围
    private int orderType; // 排序方式
    private String zfflag; // 倾向性（正负面）
    private String mediaType; // 媒体类型，1-新闻、2-政府网站、3-高校网站
    private int quchong; // 去重 1-是 0-否
    private int trash; // 去垃圾 1-是 0-否
    private int read; // 去已读 1-是 0-否
    private String needHost;    //指定站点
    private String filterHost;  //过滤站点
    private String filterBeanIds; //过滤信息
    private int bigV; //千万大v

    private String regexArea; // 地域词
    private String regexSubject; // 主体词（单位/人物）
    private String regexEvent; // 事件词
    private String regexNo; // 屏蔽词
    private String regexRegion; // 关键词位置

    private Date createTime; // 创建时间
    private Date updateTime; // 更新时间

    private String plateGroupId; //版块分组id

    private String isPlateGroup; //是否版块分组：0否 1是

    // 查询字段，不入库
    private String secondWord; // 二次搜索关键词

    private String globeFilterSite; //全局过滤站点

    private String sourceSetting; //信源设置 0 不使用信源  1 定向选择  2 定向排除

    private Map<String, String> sourceMap; //信源信息 定向信源
//    private List<SourceSettingBO> sourceSettingBO; //信源设置

    private Map<String, String> sourceLabelSiteMap; //信源信息 信源标签

    private String planId; //关联的短视频方案id

    private String sourceLabelId; //信源标签Id
    //地域码
    private String areaCode;
    private String provinceCode;
    private String cityCode;
    private String contentType;  //微博内容
    private String original;  //微博类型
    private String weiBoLevel;  //微博等级
    private String startTime;  //监测开始时间
    private String endTime;  //监测结束时间
    private String secretKey; //密钥
    private String periodRegion; // 周期时间范围
    private String videoTypeHost; // 短视频



    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getWeiBoLevel() {
        return weiBoLevel;
    }

    public void setWeiBoLevel(String weiboLevel) {
        this.weiBoLevel = weiboLevel;
    }

    private int configSelect;//配置选择

    private String monitorWords;//监控关键词

    private String excludeWords;//排除关键词

    //板块相关信息数量
    private int infoNum;

    private int plateType;  //板块类别  0文本板块 1视频板块

    /**
     * 视频搜索结果的平台及对应信息数量json字符串
     */
    private Map<String, Integer> platform;

    //简报相关字段
    private int reportStatus = 0;  //是否启用 1启用 0禁用[默认]

    private String weekDate;  //生成日期

    private String detailTime;  //生成具体时间

    private String receiverEmails;  //接收人邮箱

    private int reportTemplateId;  //报告模板id

    private String reportType; //报告类别 WEEK周报 DAY日报

    private int sendStatus; //报告发送状态  1已发送 0未发送

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getTimeRegion() {
        return timeRegion;
    }

    public void setTimeRegion(int timeRegion) {
        this.timeRegion = timeRegion;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public String getZfflag() {
        return zfflag;
    }

    public void setZfflag(String zfflag) {
        this.zfflag = zfflag;
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public String getRegexArea() {
        return regexArea;
    }

    public void setRegexArea(String regexArea) {
        this.regexArea = regexArea;
    }

    public String getRegexSubject() {
        return regexSubject;
    }

    public void setRegexSubject(String regexSubject) {
        this.regexSubject = regexSubject;
    }

    public String getRegexEvent() {
        return regexEvent;
    }

    public void setRegexEvent(String regexEvent) {
        this.regexEvent = regexEvent;
    }

    public String getRegexNo() {
        return regexNo;
    }

    public void setRegexNo(String regexNo) {
        this.regexNo = regexNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getQuchong() {
        return quchong;
    }

    public void setQuchong(int quchong) {
        this.quchong = quchong;
    }

    public String getRegexRegion() {
        return regexRegion;
    }

    public void setRegexRegion(String regexRegion) {
        this.regexRegion = regexRegion;
    }

    public int getTrash() {
        return trash;
    }

    public void setTrash(int trash) {
        this.trash = trash;
    }

    public String getSecondWord() {
        return secondWord;
    }

    public void setSecondWord(String secondWord) {
        this.secondWord = secondWord;
    }

    public String getSourceSetting() {
        return sourceSetting;
    }

    public void setSourceSetting(String sourceSetting) {
        this.sourceSetting = sourceSetting;
    }

    public Map<String, String> getSourceMap() {
        return sourceMap;
    }

    public void setSourceMap(Map<String, String> sourceMap) {
        this.sourceMap = sourceMap;
    }

    public String getNeedHost() {
        return needHost;
    }

    public void setNeedHost(String needHost) {
        this.needHost = needHost;
    }

    public String getFilterHost() {
        return filterHost;
    }

    public void setFilterHost(String filterHost) {
        this.filterHost = filterHost;
    }

    public String getPlateGroupId() {
        return plateGroupId;
    }

    public void setPlateGroupId(String plateGroupId) {
        this.plateGroupId = plateGroupId;
    }

    public String getIsPlateGroup() {
        return isPlateGroup;
    }

    public void setIsPlateGroup(String isPlateGroup) {
        this.isPlateGroup = isPlateGroup;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getGlobeFilterSite() {
        return globeFilterSite;
    }

    public void setGlobeFilterSite(String globeFilterSite) {
        this.globeFilterSite = globeFilterSite;
    }

    public int getInfoNum() {
        return infoNum;
    }

    public void setInfoNum(int infoNum) {
        this.infoNum = infoNum;
    }

    public int getConfigSelect() {
        return configSelect;
    }

    public void setConfigSelect(int configSelect) {
        this.configSelect = configSelect;
    }

    public String getMonitorWords() {
        return monitorWords;
    }

    public void setMonitorWords(String monitorWords) {
        this.monitorWords = monitorWords;
    }

    public String getExcludeWords() {
        return excludeWords;
    }

    public void setExcludeWords(String excludeWords) {
        this.excludeWords = excludeWords;
    }

    public int getPlateType() {
        return plateType;
    }

    public void setPlateType(int plateType) {
        this.plateType = plateType;
    }

    public Map<String, Integer> getPlatform() {
        return platform;
    }

    public void setPlatform(Map<String, Integer> platform) {
        this.platform = platform;
    }

    public int getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(int reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getWeekDate() {
        return weekDate;
    }

    public void setWeekDate(String weekDate) {
        this.weekDate = weekDate;
    }

    public String getDetailTime() {
        return detailTime;
    }

    public void setDetailTime(String detailTime) {
        this.detailTime = detailTime;
    }

    public String getReceiverEmails() {
        return receiverEmails;
    }

    public void setReceiverEmails(String receiverEmails) {
        this.receiverEmails = receiverEmails;
    }

    public int getReportTemplateId() {
        return reportTemplateId;
    }

    public void setReportTemplateId(int reportTemplateId) {
        this.reportTemplateId = reportTemplateId;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public int getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(int sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getPeriodRegion() {
        return periodRegion;
    }

    public void setPeriodRegion(String periodRegion) {
        this.periodRegion = periodRegion;
    }

    public String getVideoTypeHost() {
        return videoTypeHost;
    }

    public void setVideoTypeHost(String videoTypeHost) {
        this.videoTypeHost = videoTypeHost;
    }

    public int getRead() {
        return read;
    }

    public void setRead(int read) {
        this.read = read;
    }

    public String getFilterBeanIds() {
        return filterBeanIds;
    }

    public void setFilterBeanIds(String filterBeanIds) {
        this.filterBeanIds = filterBeanIds;
    }

    public String getSourceLabelId() {
        return sourceLabelId;
    }

    public void setSourceLabelId(String sourceLabelId) {
        this.sourceLabelId = sourceLabelId;
    }

    public Map<String, String> getSourceLabelSiteMap() {
        return sourceLabelSiteMap;
    }

    public void setSourceLabelSiteMap(Map<String, String> sourceLabelSiteMap) {
        this.sourceLabelSiteMap = sourceLabelSiteMap;
    }

    public int getBigV() {
        return bigV;
    }

    public void setBigV(int bigV) {
        this.bigV = bigV;
    }

//    public List<SourceSettingBO> getSourceSettingBO() {
//        return sourceSettingBO;
//    }
//
//    public void setSourceSettingBO(List<SourceSettingBO> sourceSettingBO) {
//        this.sourceSettingBO = sourceSettingBO;
//    }

    /**
     * 获取关键词
     *
     * <AUTHOR>
     * @date 2017-06-15 下午13:49:31
     */
    public String getKeywords() {
        StringBuilder sb = new StringBuilder();
        if (regexArea != null) {
            sb.append(regexArea).append(" ");
        }
        if (regexSubject != null) {
            sb.append(regexSubject).append(" ");
        }
        if (regexEvent != null) {
            sb.append(regexEvent).append(" ");
        }
        return sb.toString().trim();
    }

    @Override
    public String toString() {
        return "Plate [id=" + id + ", name=" + name + ", secondWord=" + secondWord + "]";
    }


}
