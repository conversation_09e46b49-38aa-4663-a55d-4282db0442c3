package com.boryou.web.controller.common.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-23 10:26
 */
@Data
public class BoryouBean {

    private int weiBoLevel;

    private int province;

    private int city;

    private String biz;

    private String id = "";

    private String title = "";

    private String text = "";

    private String host;

    private String url = "";

    private String author = "";

    private int date;

    private String time = "";

    private String submitTime = "";

    private int type = -1;

    private boolean original = true;

    private int language = 0;

    private int emotional = 0;

    private String siteAddress = "";

    private List<String> textAddress = new ArrayList();

    private boolean spamFlag;

    private List<String> siteMeta = new ArrayList();

    private List<String> picUrl = new ArrayList();

    private List<Integer> algorithmClassify = new ArrayList();

    private String businessType = null;

    private String authorPortraitLink;

    private boolean deleted;

    private String articleSource;

    private String cover;
    private int collect;

    private Integer readNum;

    private Integer commentNum;

    private Integer commentGoodsNum;

    private Integer reprintNum;

    private String qqGroupName;

    private String qqGroupId;

    private String qqId;

    private String weixinFunc;

    private Integer avType;

    private String parentUrl;

    private String picDescription;

    private List<Integer> contentType;

    private Float price;

    private String commodity;

    private Float averageScore;

    private List<String> commentTag = new ArrayList();

    private Integer poorNum;

    private Float poorRate;

    private Integer generalNum;

    private Float generalRate;

    private Integer goodNum;

    private Float goodRate;

    private Integer afterNum;

    private Integer recruitingNum;

    private String workPlace;

    private String jobNature;

    private String companyName;

    private String companyNature;

    private String companySize;

    private String companyIndustry;

    private String companyAddress;

    private String companyIntroduction;

    private Integer bid;

    private Integer bidType;

    private String inviteUnit;

    private Float budget;

    private String winUnit;

    private String bidOpenTime;

    private String bidEndTime;

    private String bidStartTime;

    private String tel;

    private Integer personnelChangeType;

    private List<Integer> customer = new ArrayList();

    private Integer updateStatus;

    private Integer childType;

    private List<Long> simhashs;

    private String MD5;

    private String mapPath;

    private int trash;

    private String sourceUrl;

    private Long sourceWbId;

    private Long wbAuthorId;

    private String sector;

    private boolean forward;

    private String authorId;
    private String titleMD5;
    private String untreatedTitle;
    private String untreatedText;
    private String hostName;
    private boolean haveRead;
    private boolean haveWarned;

    private List<Integer> siteArea;

    private List<Integer> textArea;

    private Boolean wbOriginal;
    private Integer order;
    private String spreadImpact;
    private String mediaId;
    private String mediaName;
    private String mediaType;
    private String platformType;

}

