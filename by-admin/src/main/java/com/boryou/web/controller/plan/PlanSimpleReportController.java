package com.boryou.web.controller.plan;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.domain.PlanSimpleReport;
import com.boryou.web.domain.vo.PlanSimpleReportVO;
import com.boryou.web.module.report.service.PlanSimpleReportService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 方案定时简报Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
public class PlanSimpleReportController extends BaseController {

    @Resource
    PlanSimpleReportService planSimpleReportService;

    @GetMapping("/planReport/selectPage")
    public TableDataInfo selectPage(@ModelAttribute PlanSimpleReportVO vo) {
        startPage();
        List<PlanSimpleReport> list = planSimpleReportService.getList(vo);
        if (CollUtil.isNotEmpty(list)) {
            List<PlanSimpleReportVO> vos = new ArrayList<>();
            for (PlanSimpleReport planSimpleReport : list) {
                PlanSimpleReportVO vo1 = new PlanSimpleReportVO();
                BeanUtil.copyProperties(planSimpleReport, vo1);
                if (StrUtil.isNotEmpty(planSimpleReport.getReceiverEmails()) && planSimpleReport.getReceiverEmails().startsWith("[")) {
                    JSONArray array = JSONUtil.parseArray(planSimpleReport.getReceiverEmails());
                    vo1.setUsers(array);
                }
                vos.add(vo1);
            }
            return getDataTable(vos);
        }
        return getDataTable(list);
    }

    @PostMapping("/planReport/insert")
    public AjaxResult insert(@RequestBody PlanSimpleReport planSimpleReport) {
        return AjaxResult.success(planSimpleReportService.insert(planSimpleReport));
    }

    @PostMapping("/planReport/update")
    public AjaxResult update(@RequestBody PlanSimpleReportVO vo) {
        PlanSimpleReport planSimpleReport = new PlanSimpleReport();
        BeanUtil.copyProperties(vo, planSimpleReport);
        if (CollUtil.isNotEmpty(vo.getUsers())) {
            JSONArray array = new JSONArray();
            array.addAll(vo.getUsers());
            planSimpleReport.setReceiverEmails(array.toString());
        }

        //新增
        if (planSimpleReport.getId() == null) {
            return AjaxResult.success(planSimpleReportService.insert(planSimpleReport));
        }
        return AjaxResult.success(planSimpleReportService.update(planSimpleReport));
    }

}
