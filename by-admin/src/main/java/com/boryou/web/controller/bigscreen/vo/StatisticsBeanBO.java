package com.boryou.web.controller.bigscreen.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-05-11 13:23
 */
@Data
public class StatisticsBeanBO implements Serializable {

    // 关键词词组1-3 多个以英文逗号或空格分隔
    private String keywords1;
    private String keywords2;
    private String keywords3;
    // 排除词词组
    private String excludeWords;
    // 开始和结束时间
    private String startTime;
    //日志时间ID
    private String logTimeId;
    private String endTime;
    // 时间段数
    private int count;
    // 开始页码和页大小
    private int pageNum = 1;
    private int pageSize = 10;
    private Integer emotion;//情感类型 -1代表全部，0：代表中性，1：代表负面，2：代表正面
    private int sort; // 排序方式
    private Integer original = 0;//当值为1时表示去重，0不去重
    // 查询词的限定范围 all：包含标题与正文，title：标题，text：正文
    private String keywordsPosition = "all";
    private String range = "all";
    //信息类型  8,9数字那种
    private String mediaType;
    private String hotName;
    private String url;
    private String title;
    private String host;
    private String excludeHosts;
    private int timeType;
    private String author;
    private Integer spamFlag;  //0是去除垃圾
    private String videoType; //短视频媒体平台
//    private int type;
    //排序字段
    private String sortField;
    //ASC,DESC
    private String sortType;

    private Integer aggSize = 10;

    private String md5;

    private String[] siteMeta;

    /**
     * 内容地域 contentAreaCode
     */
    private String contentAreaCode;

    //TODO 关键词为且的搜索方式   备注：我自己写的commsearch搜索方法的的临时type区分，后面需要优化这种处理方式,应该要废弃掉
    private String searchKewordType;

    /**
     * 搜索方式 1 专业模式  0 普通模式
     */
    private int configSelect;

    /**
     * 专业搜索词
     */
    private String proWord;

    /**
     * 项目接口调用标识
     */
    private Integer projectType;


    //es的写法是空格分开每个关键词，是模糊查询
    public void setKeywords1(String keywords1) {
        if (StrUtil.isNotEmpty(keywords1)&&keywords1.contains(",")){
            keywords1=keywords1.replaceAll(","," ");
        }
        this.keywords1 = keywords1;
    }

    public void setKeywords2(String keywords2) {
        if (StrUtil.isNotEmpty(keywords2)&&keywords2.contains(",")){
            keywords2=keywords2.replaceAll(","," ");
        }
        this.keywords2 = keywords2;
    }

    public void setKeywords3(String keywords3) {
        if (StrUtil.isNotEmpty(keywords3)&&keywords3.contains(",")){
            keywords3=keywords3.replaceAll(","," ");
        }
        this.keywords3 = keywords3;
    }

    public void setExcludeWords(String excludeWords) {
        if (StrUtil.isNotEmpty(excludeWords)&&excludeWords.contains(",")){
            excludeWords=excludeWords.replaceAll(","," ");
        }
        this.excludeWords = excludeWords;
    }
}

