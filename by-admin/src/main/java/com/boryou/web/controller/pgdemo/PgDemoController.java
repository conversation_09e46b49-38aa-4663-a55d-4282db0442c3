package com.boryou.web.controller.pgdemo;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.domain.pg.PgDemo;
import com.boryou.web.service.PgDemoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/xtest/pgDemo")
public class PgDemoController {
    private final PgDemoService pgDemoService;

    @PostMapping("/add")
    public AjaxResult add(@RequestBody PgDemo pgDemo) {
        int i = pgDemoService.insertOne(pgDemo);
        return AjaxResult.success(i);
    }

    @GetMapping("/list")
    public AjaxResult list() {
        List<PgDemo> pgDemoList = pgDemoService.getList();
        return AjaxResult.success(pgDemoList);
    }

}
