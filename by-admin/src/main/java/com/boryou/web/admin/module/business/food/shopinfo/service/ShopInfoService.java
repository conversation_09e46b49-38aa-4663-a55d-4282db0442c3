package com.boryou.web.admin.module.business.food.shopinfo.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.web.admin.module.business.food.shopinfo.constant.FoodLicenseEnum;
import com.boryou.web.admin.module.business.food.shopinfo.dao.ShopInfoDao;
import com.boryou.web.admin.module.business.food.shopinfo.domain.entity.ShopInfoEntity;
import com.boryou.web.admin.module.business.food.shopinfo.domain.form.ShopInfoAddForm;
import com.boryou.web.admin.module.business.food.shopinfo.domain.form.ShopInfoExcelForm;
import com.boryou.web.admin.module.business.food.shopinfo.domain.form.ShopInfoQueryForm;
import com.boryou.web.admin.module.business.food.shopinfo.domain.form.ShopInfoUpdateForm;
import com.boryou.web.admin.module.business.food.shopinfo.domain.vo.ShopExcelVO;
import com.boryou.web.admin.module.business.food.shopinfo.domain.vo.ShopInfoVO;
import com.boryou.web.admin.module.business.food.shopinfo.manager.ShopInfoManager;
import com.boryou.web.base.common.domain.PageResult;
import com.boryou.web.base.common.domain.ResponseDTO;
import com.boryou.web.base.common.exception.BusinessException;
import com.boryou.web.base.common.util.SmartBeanUtil;
import com.boryou.web.base.common.util.SmartEnumUtil;
import com.boryou.web.base.common.util.SmartPageUtil;
import com.boryou.web.base.common.util.SmartRequestUtil;
import com.boryou.web.base.module.support.dict.service.DictCacheService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺信息表 Service
 *
 * <AUTHOR>
 * @Date 2025-02-27
 */

@Service
public class ShopInfoService {

    @Resource
    private ShopInfoDao shopInfoDao;

    @Resource
    private DictCacheService dictCacheService;

    @Resource
    private ShopInfoManager shopInfoManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ShopInfoVO> queryPage(ShopInfoQueryForm queryForm) {
        queryForm.setDeletedFlag(false);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ShopInfoVO> list = shopInfoDao.queryPage(page, queryForm);
        PageResult<ShopInfoVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ShopInfoAddForm addForm) {
        ShopInfoEntity shopInfoEntity = SmartBeanUtil.copy(addForm, ShopInfoEntity.class);
        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        shopInfoEntity.setShopId(snowflakeNextId);
        Long requestUserId = SmartRequestUtil.getRequestUserId();
        shopInfoEntity.setCreateUserId(requestUserId);
        shopInfoEntity.setUpdateUserId(requestUserId);
        shopInfoEntity.setDeletedFlag(false);
        shopInfoDao.insert(shopInfoEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ShopInfoUpdateForm updateForm) {
        ShopInfoEntity shopInfoEntity = SmartBeanUtil.copy(updateForm, ShopInfoEntity.class);
        Long requestUserId = SmartRequestUtil.getRequestUserId();
        shopInfoEntity.setUpdateUserId(requestUserId);
        shopInfoDao.updateById(shopInfoEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        shopInfoDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        shopInfoDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    public List<ShopExcelVO> getShopExcel(ShopInfoExcelForm excelForm) {
        List<Long> ids = excelForm.getIds();
        List<ShopInfoVO> shopInfoEntityList = new ArrayList<>();
        if (CollUtil.isNotEmpty(ids)) {
            List<ShopInfoEntity> shopInfoEntityList1 = shopInfoManager.listByIds(ids);
            shopInfoEntityList = SmartBeanUtil.copyList(shopInfoEntityList1, ShopInfoVO.class);
        } else {
            // queryForm.setDeletedFlag(false);
            // Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
            // shopInfoEntityList = shopInfoDao.queryPage(page, queryForm);
            throw new BusinessException("勾选数据不能为空");
        }

        return shopInfoEntityList.stream()
                .map(e ->
                        ShopExcelVO.builder()
                                .shopName(e.getShopName())
                                .shopPlatform(dictCacheService.selectValueNameByValueCode(String.valueOf(e.getShopPlatform())))
                                .enterpriseName(e.getEnterpriseName())
                                .enterpriseType(e.getEnterpriseType())
                                .legalRepresentative(e.getLegalRepresentative())
                                .businessScope(e.getBusinessScope())
                                .registeredAddress(e.getRegisteredAddress())
                                .foodLicense(SmartEnumUtil.getEnumDescByValue(e.getFoodLicense(), FoodLicenseEnum.class))
                                .build()
                )
                .collect(Collectors.toList());
    }

}
