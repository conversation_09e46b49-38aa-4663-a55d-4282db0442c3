package com.boryou.web.admin.module.system.support;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import com.boryou.web.base.common.controller.SupportBaseController;
import com.boryou.web.base.common.domain.PageResult;
import com.boryou.web.base.common.domain.ResponseDTO;
import com.boryou.web.base.constant.SwaggerTagConst;
import com.boryou.web.base.module.support.heartbeat.HeartBeatService;
import com.boryou.web.base.module.support.heartbeat.domain.HeartBeatRecordQueryForm;
import com.boryou.web.base.module.support.heartbeat.domain.HeartBeatRecordVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 心跳记录
 *
 * <AUTHOR> 卓大
 * @Date 2022-01-09 20:57:24
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Tag(name = SwaggerTagConst.Support.HEART_BEAT)
@RestController
public class AdminHeartBeatController extends SupportBaseController {

    @Resource
    private HeartBeatService heartBeatService;

    @PostMapping("/heartBeat/query")
    @Operation(summary = "查询心跳记录 <AUTHOR>
    public ResponseDTO<PageResult<HeartBeatRecordVO>> query(@RequestBody @Valid HeartBeatRecordQueryForm pageParam) {
        return heartBeatService.pageQuery(pageParam);
    }

}
