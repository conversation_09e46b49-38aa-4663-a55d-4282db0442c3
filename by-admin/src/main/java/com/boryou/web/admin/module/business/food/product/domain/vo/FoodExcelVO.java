package com.boryou.web.admin.module.business.food.product.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * excel商品
 *
 * <AUTHOR> 胡克
 * @Date 2021-10-25 20:26:54
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FoodExcelVO {

    @ExcelProperty("图片")
    private String productImage;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("规格")
    private String specification;

    @ExcelProperty("价格")
    private String price;

    @ExcelProperty("销量")
    @Schema(description = "累计销量")
    private Long sales;

    @ExcelProperty("店铺")
    private String shopName;

}
