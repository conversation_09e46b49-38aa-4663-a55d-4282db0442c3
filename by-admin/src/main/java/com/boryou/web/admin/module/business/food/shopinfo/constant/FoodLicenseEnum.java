package com.boryou.web.admin.module.business.food.shopinfo.constant;


import com.boryou.web.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 店铺信息表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-27
 */


@AllArgsConstructor
@Getter
public enum FoodLicenseEnum implements BaseEnum {

    /**
     * 1 商品
     */
    HAS(0, "有"),

    /**
     * 2 自定义
     */
    NO(1, "无"),

    ;

    private final Integer value;

    private final String desc;
}
