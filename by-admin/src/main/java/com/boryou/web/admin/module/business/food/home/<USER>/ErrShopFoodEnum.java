package com.boryou.web.admin.module.business.food.home.constant;


import com.boryou.web.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 店铺信息表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-27
 */


@AllArgsConstructor
@Getter
public enum ErrShopFoodEnum implements BaseEnum {

    NORMALSHOP(0, "正常店铺"),

    ERRSHOP(1, "问题店铺"),

    NORMALFOOD(2, "正常食品"),

    ERRFOOD(3, "问题食品"),

    ;

    private final Integer value;

    private final String desc;
}
