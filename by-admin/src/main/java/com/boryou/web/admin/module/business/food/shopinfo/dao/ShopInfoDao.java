package com.boryou.web.admin.module.business.food.shopinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.web.admin.module.business.food.shopinfo.domain.entity.ShopInfoEntity;
import com.boryou.web.admin.module.business.food.shopinfo.domain.form.ShopInfoQueryForm;
import com.boryou.web.admin.module.business.food.shopinfo.domain.vo.ShopInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 店铺信息表 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-27
 */

@Mapper
@Component
public interface ShopInfoDao extends BaseMapper<ShopInfoEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ShopInfoVO> queryPage(Page page, @Param("queryForm") ShopInfoQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id") Long id, @Param("deletedFlag") boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") boolean deletedFlag);


}
