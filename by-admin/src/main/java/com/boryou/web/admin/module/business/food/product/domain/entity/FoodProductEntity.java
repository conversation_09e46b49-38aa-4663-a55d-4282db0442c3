package com.boryou.web.admin.module.business.food.product.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 食品商品表（含地理信息） 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-27
 */

@Data
@TableName(value = "b_food_product", autoResultMap = true)
public class FoodProductEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 食品ID
     */
    private Long foodId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品主图URL
     */
    private String productImage;

    /**
     * 原文url
     */
    private String url;

    /**
     * 完整标题（含品牌+商品名+卖点）
     */
    private String title;

    /**
     * 销售价
     */
    private String price;

    /**
     * 累计销量
     */
    private Long sales;

    /**
     * 品牌名称
     * <p>示例：三只松鼠</p>
     */
    private String brand;

    /**
     * 商品品名
     * <p>示例：每日坚果</p>
     */
    private String productName;

    /**
     * 产品系列
     * <p>示例：混合果仁系列</p>
     */
    private String series;

    /**
     * 商品所在省份
     * <p>示例：浙江省</p>
     */
    private String province;

    /**
     * 商品所在城市
     * <p>示例：杭州市</p>
     */
    private String city;

    /**
     * 产地详细信息
     * <p>示例：中国安徽</p>
     */
    private String originDetail;

    /**
     * 生产厂家名称
     * <p>示例：三只松鼠股份有限公司</p>
     */
    private String manufacturer;

    /**
     * 厂家详细地址
     * <p>示例：安徽省芜湖市弋江区</p>
     */
    private String factoryAddress;

    /**
     * 厂家联系方式
     * <p>示例：400-123-4567</p>
     */
    private String factoryNumber;

    /**
     * 生产许可证编号
     * <p>示例：SC123456789</p>
     */
    private String licenseNumber;

    /**
     * 包装方式
     * <p>示例：定量包装</p>
     */
    private String packagingMethod;

    /**
     * 包装规格描述
     * <p>示例：30袋/箱</p>
     */
    private String specification;

    /**
     * 包装种类
     * <p>示例：礼盒装</p>
     */
    private String packagingStyle;

    /**
     * 商品详情页图片（JSON数组存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> descImg;

    /**
     * 逻辑删除(0:正常 1:删除)
     */
    private Integer deletedFlag;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateUserId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
