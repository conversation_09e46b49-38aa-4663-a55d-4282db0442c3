package com.boryou.web.admin.module.business.food.product.service;

import com.boryou.web.admin.module.business.food.product.domain.entity.FoodEvidenceEntity;
import com.boryou.web.admin.module.business.food.product.manager.FoodEvidenceManager;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FoodEvidenceService {
    @Resource
    private FoodEvidenceManager foodEvidenceManager;

    public void save(FoodEvidenceEntity foodEvidenceEntityAdd) {
        foodEvidenceManager.saveOrUpdate(foodEvidenceEntityAdd);
    }

    public List<FoodEvidenceEntity> getByFoodId(Long id) {
        return foodEvidenceManager.lambdaQuery().eq(FoodEvidenceEntity::getFoodId, id).list();
    }

}
