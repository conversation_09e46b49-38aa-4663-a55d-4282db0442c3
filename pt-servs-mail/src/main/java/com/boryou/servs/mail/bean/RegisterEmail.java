package com.boryou.servs.mail.bean;

public class RegisterEmail extends Email {

    public RegisterEmail() {

    }

    public RegisterEmail(String toAddress, String title, String content, int num) {
        super(toAddress, title, content, num);
    }

    @Override
    public void init(int num) {
        this.mailServerHost = "smtp.ym.163.com";
        this.mailServerPort = "25";
        this.fromAddress = "info" + num + "@boryou.com";
        this.userName = "info" + num + "@boryou.com";
        this.password = "Z8x402eq690q3#";

    }

}
