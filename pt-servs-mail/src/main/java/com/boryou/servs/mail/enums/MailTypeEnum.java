package com.boryou.servs.mail.enums;

import java.util.Objects;

/**
 * @description T邮件类型枚举类
 * <AUTHOR>
 * @date 2024/7/9 14:16
 */

public enum MailTypeEnum {

    //文本
    TEXT("1"),
    //HTML
    HTML("2");

    String value;

    MailTypeEnum(String value) {
        this.value = value;
    }

    public  String getValue(){
        return value;
    }

    public static MailTypeEnum getByValue(String value){
        for(MailTypeEnum x:values()){
            if(Objects.equals(x.value, value)){
                return x;
            }
        }
        return TEXT;

    }
}
