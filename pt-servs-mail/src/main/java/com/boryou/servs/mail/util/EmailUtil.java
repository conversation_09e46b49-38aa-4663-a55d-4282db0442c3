package com.boryou.servs.mail.util;

import cn.hutool.core.util.RandomUtil;
import com.boryou.servs.mail.bean.Email;
import com.boryou.servs.mail.bean.MyAuthenticator;
import com.boryou.servs.mail.bean.RegisterEmail;
import com.boryou.servs.mail.enums.MailTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 邮箱工具类
 * @date 2024/7/9 13:57
 */

public class EmailUtil {

    private EmailUtil() {
    }

    ;

    /**
     * 验证邮箱是否合法
     */
    public static boolean isMail(String mail) {
        boolean flag = false;
        try {
            String check = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
            Pattern regex = Pattern.compile(check);
            Matcher matcher = regex.matcher(mail);
            flag = matcher.matches();
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }


    /**
     * 普通邮件(无附件)
     * @param receiver 接收人邮箱
     * @param theme    主题
     * @param content  邮件内容
     * @return 返回值
     */
    public static boolean sendMail(String receiver, String theme, String content, String mailType) {
        Email mailInfo = new RegisterEmail(receiver, theme, content, RandomUtil.randomInt(9));
        // 判断是否需要身份认证
        MyAuthenticator authenticator = null;
        Properties pro = mailInfo.getProperties();
        if (mailInfo.isValidate()) {
            // 如果需要身份认证，则创建一个密码验证器
            authenticator = new MyAuthenticator(mailInfo.getUserName(), mailInfo.getPassword());
        }
        // 根据邮件会话属性和密码验证器构造一个发送邮件的session
        Session sendMailSession = Session.getInstance(pro, authenticator);
        try {
            Message mailMessage = createMessage(mailInfo, sendMailSession);
            // 设置邮件消息的主要内容
            if (MailTypeEnum.HTML.getValue().equals(mailType)) {
                // MiniMultipart类是一个容器类，包含MimeBodyPart类型的对象
                Multipart mainPart = new MimeMultipart();
                // 创建一个包含HTML内容的MimeBodyPart
                BodyPart html = new MimeBodyPart();
                // 设置HTML内容
                html.setContent(mailInfo.getContent(), "text/html; charset=utf-8");
                mainPart.addBodyPart(html);
                // 将MiniMultipart对象设置为邮件内容
                mailMessage.setContent(mainPart);
            } else {
                String mailContent = mailInfo.getContent();
                mailMessage.setText(mailContent);
            }
            //保存发送
            mailMessage.saveChanges();//后来加的
            // 发送邮件
            Transport.send(mailMessage);
            return true;
        } catch (MessagingException ex) {
            ex.printStackTrace();
        }
        return false;
    }

    private static Message createMessage(Email mailInfo, Session sendMailSession) throws MessagingException {
        // 根据session创建一个邮件消息
        Message mailMessage = new MimeMessage(sendMailSession);
        // 创建邮件发送者地址
        Address from = new InternetAddress(mailInfo.getFromAddress());
        // 设置邮件消息的发送者
        mailMessage.setFrom(from);
        // 创建邮件的接收者地址，并设置到邮件消息中
        Address to = new InternetAddress(mailInfo.getToAddress());
        // Message.RecipientType.TO属性表示接收者的类型为TO
        mailMessage.setRecipient(Message.RecipientType.TO, to);
        // 设置邮件消息的主题
        mailMessage.setSubject(mailInfo.getTitle());
        // 设置邮件消息发送的时间
        mailMessage.setSentDate(new Date());
        return mailMessage;
    }


    /**
     * 带附件的邮件*
     * @param receiver
     * @param theme
     * @param content
     * @param multipartFiles
     * @param mailType
     * @return
     */
    public static boolean sendMail(String receiver, String theme, String content, List<MultipartFile> multipartFiles, String mailType) {
        Email mailInfo = new RegisterEmail(receiver, theme, content, RandomUtil.randomInt(9));
        // 判断是否需要身份认证
        MyAuthenticator authenticator = null;
        Properties pro = mailInfo.getProperties();
        if (mailInfo.isValidate()) {
            // 如果需要身份认证，则创建一个密码验证器
            authenticator = new MyAuthenticator(mailInfo.getUserName(), mailInfo.getPassword());
        }
        // 根据邮件会话属性和密码验证器构造一个发送邮件的session
        Session sendMailSession = Session.getInstance(pro, authenticator);
        List<File> needRemoveFiles = new ArrayList<>();
        try {
            // 根据session创建一个邮件消息
            Message mailMessage = createMessage(mailInfo, sendMailSession);
            // 设置邮件消息的主要内容
            Multipart mainPart = new MimeMultipart();
            if (MailTypeEnum.HTML.getValue().equals(mailType)) {
                // MiniMultipart类是一个容器类，包含MimeBodyPart类型的对象
                // 创建一个包含HTML内容的MimeBodyPart
                BodyPart html = new MimeBodyPart();
                // 设置HTML内容
                html.setContent(mailInfo.getContent(), "text/html; charset=utf-8");
                mainPart.addBodyPart(html);

            } else {
                BodyPart bodyPart = new MimeBodyPart();
                String mailContent = mailInfo.getContent();
                bodyPart.setText(mailContent);
                mainPart.addBodyPart(bodyPart);
            }
            for (MultipartFile multipartFile : multipartFiles) {
                MimeBodyPart messageBodyPart = new MimeBodyPart();
                String filePath = System.getProperty("user.dir") + File.separator + "tempPath" + File.separator + multipartFile.getOriginalFilename();
                File file = new File(filePath);
                multipartFile.transferTo(file);
                FileDataSource source = new FileDataSource(file);
                messageBodyPart.setDataHandler(new DataHandler(source));
                messageBodyPart.setFileName(file.getName());
                mainPart.addBodyPart(messageBodyPart);
                needRemoveFiles.add(file);
            }
            mailMessage.setContent(mainPart);
            //保存发送
            mailMessage.saveChanges();//后来加的
            // 发送邮件
            Transport.send(mailMessage);
            return true;
        } catch (MessagingException | IOException ex) {
            ex.printStackTrace();
        }finally {
            for (File file : needRemoveFiles) {
                file.deleteOnExit();
            }
        }
        return false;
    }


    public static void main(String[] args2) {
        String content = "<b>报告标题：</b>" + "2024年7月9日日报" + "<br>" + "<b>原文链接：</b><a href=\""
                + "https://www.iesdouyin.com/share/video/7389487317565656355/?schema_type=37" + "\">" + "https://www.iesdouyin.com/share/video/7389487317565656355/?schema_type=37" + "</a>"
                + "<br><br><br>-- 此邮件由舆情系统发出，请勿直接回复 --";
        boolean b = sendMail("<EMAIL>", "测试邮件功能", content, "2");
        System.out.println(b);

    }

}
