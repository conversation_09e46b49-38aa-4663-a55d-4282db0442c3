package com.boryou.servs.mail.service.impl;

import cn.hutool.core.date.DateTime;
import com.boryou.servs.common.util.IdUtil;
import com.boryou.servs.mail.mapper.LogMapper;
import com.boryou.servs.mail.pojo.po.LogPO;
import com.boryou.servs.mail.service.LogService;
import com.diboot.core.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: Young
 */
@Service
public class LogServiceImpl extends BaseServiceImpl<LogMapper, LogPO> implements LogService {
    @Resource
    private LogMapper logMapper;

    @Override
    public void insertMailLog(LogPO logPO) {
        logPO.setId(IdUtil.nextLong());
        logPO.setCtime(DateTime.now());  //落表时间
        logMapper.insert(logPO);
    }
}
