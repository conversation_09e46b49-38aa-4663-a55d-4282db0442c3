package com.boryou.servs.mail.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.mail.pojo.bo.SendBO;
import com.boryou.servs.mail.pojo.po.LogPO;
import com.boryou.servs.mail.pojo.po.TemplatePO;
import com.boryou.servs.mail.pojo.sb.TemplateSB;
import com.boryou.servs.mail.service.LogService;
import com.boryou.servs.mail.service.SendService;
import com.boryou.servs.mail.service.TemplateService;
import com.boryou.servs.mail.util.EmailUtil;
import com.diboot.core.util.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.*;

/**
 * @author: Young
 * @Date: 2021/12/14
 */
@Service
public class SendServiceImpl implements SendService {

    @Override
    public boolean sendMail(SendBO sendBO) {
        String receiver = sendBO.getReceiver();
        String mailType = sendBO.getMailType();
        String theme = sendBO.getTheme();
        String content = sendBO.getContent();
        List<MultipartFile> files = sendBO.getFiles();
        return CollUtil.isNotEmpty(files)? EmailUtil.sendMail(receiver, theme, content, files, mailType):EmailUtil.sendMail(receiver, theme, content, mailType);
    }
}
