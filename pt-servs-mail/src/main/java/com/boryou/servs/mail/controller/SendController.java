package com.boryou.servs.mail.controller;

import com.boryou.servs.mail.pojo.bo.SendBO;
import com.boryou.servs.mail.service.SendService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 邮件发送服务控制层
 *
 * @author: Young
 */
@RestController
@RequestMapping("/mail")
public class SendController extends AheadController {
    @Resource
    private SendService sendService;

    /**
     * 发送邮件
     */
    @PostMapping("/sendMail")
    public boolean sendMail(SendBO sendBO) {
        return sendService.sendMail(sendBO);
    }




}
