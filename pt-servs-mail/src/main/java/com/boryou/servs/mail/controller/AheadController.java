package com.boryou.servs.mail.controller;

import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.boryou.servs.common.util.RedisUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: Young
 */
public class AheadController {
    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 验证Authorization
     */
    private String verifyAuth() {
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isEmptyIfStr(authorization) || StrUtil.isEmptyIfStr(getTemplateData(authorization))) {
            throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "无权限");
        }
        //验证此token发送短信次数是否超出限制
//        String sendTimes = redisUtil.get(RedisConstant.SEND_TIMES_PREFIX + authorization);
//        if (!StrUtil.isEmptyIfStr(sendTimes)) {
//            Long currentSendTimes = Long.valueOf(sendTimes);
//            if (currentSendTimes >= getMaxSendTimes()){
//                throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "已超出最大发送量");
//            }
//        }
        return authorization;
    }


    /**
     * 获取模板ID（tplId）
     */
    protected Long getTplId(String type) {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        Object getTplId = templateData.get(type);
        if (getTplId == null) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        } else {
            return Long.valueOf(getTplId.toString());
        }
    }

    /**
     * 获取租户标识（tenantFlag）
     */
    protected String getTenantFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("tenantFlag").toString();
    }


    /**
     * 获取项目标识（projFlag）
     */
    protected String getProjFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("projFlag").toString();
    }

    /**
     * 获取业务标识（bizFlag）
     */
    protected String getBizFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("bizFlag").toString();
    }

    /**
     * 获取权限最大发送量（sendTimes）
     */
    protected Long getMaxSendTimes() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        Object maxSendTimes = templateData.get("maxSendTimes");
        return Long.valueOf(maxSendTimes.toString());
    }


    /**
     * 模拟token与短信数据对应关系
     */
    private static Map<String, Object> getTemplateData(String token) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgBACKPWD", 9L);   //舆情找回密码验证模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgEXPIRE", 7L);   //舆情用户账号到期通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgREPORT", 6L);   //舆情报告推送模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgWARN", 5L);   //舆情预警通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgPUSH", 4L);   //舆情信息报送通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgOPERATE", 3L);   //舆情用户运维通知模板id映射


        tokenMap1.put("projFlag", "博约舆情");      //项目标记
        tokenMap1.put("tenantFlag", "内部用户");    //租户标记
        tokenMap1.put("bizFlag", "普通业务");   //业务标记
        tokenMap1.put("maxSendTimes", 3);   //最大发送次数
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM", tokenMap1);
        return map.get(token);
    }

}
