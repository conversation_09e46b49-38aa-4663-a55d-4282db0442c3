package com.boryou.servs.mail.bean;

import java.util.Properties;
import java.util.Random;

/**
 * 邮箱父类
 * <AUTHOR>
 * @date 2017-4-19 上午9:05:09
 */
public abstract class Email {

    // 发送邮件的服务器的IP和端口
    protected String mailServerHost;
    protected String mailServerPort;
    // 邮件发送者的地址
    protected String fromAddress;
    // 邮件接收者的地址
    protected String toAddress;
    // 登录邮件发送服务器的用户名和密码
    protected String userName;
    protected String password;
    // 是否需要身份验证
    protected boolean validate = true;
    // 邮件主题
    protected String title;
    // 邮件的文本内容
    protected String content;
    // 邮件附件的文件名
    protected String[] attachFileNames;

    public abstract void init(int num);

    public Properties getProperties() {
        Properties p = new Properties();
        p.put("mail.smtp.host", this.mailServerHost);
        p.put("mail.smtp.port", this.mailServerPort);
        p.put("mail.smtp.auth", validate ? "true" : "false");
        return p;
    }

    public Email() {
        Random r = new Random();
        int num = r.nextInt(9);
        init(num);
    }


    public Email(String toAddress, String title, String content, int num) {
        init(num);
        this.toAddress = toAddress;
        this.title = title;
        this.content = content;
    }

    public String getMailServerHost() {
        return mailServerHost;
    }

    public void setMailServerHost(String mailServerHost) {
        this.mailServerHost = mailServerHost;
    }

    public String getMailServerPort() {
        return mailServerPort;
    }

    public void setMailServerPort(String mailServerPort) {
        this.mailServerPort = mailServerPort;
    }

    public String getFromAddress() {
        return fromAddress;
    }

    public void setFromAddress(String fromAddress) {
        this.fromAddress = fromAddress;
    }

    public String getToAddress() {
        return toAddress;
    }

    public void setToAddress(String toAddress) {
        this.toAddress = toAddress;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isValidate() {
        return validate;
    }

    public void setValidate(boolean validate) {
        this.validate = validate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String[] getAttachFileNames() {
        return attachFileNames;
    }

    public void setAttachFileNames(String[] attachFileNames) {
        this.attachFileNames = attachFileNames;
    }


}
