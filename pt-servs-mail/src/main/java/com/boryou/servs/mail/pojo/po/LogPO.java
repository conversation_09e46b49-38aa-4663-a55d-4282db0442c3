package com.boryou.servs.mail.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 邮件日志
 * @author: Young
 */
@Data
@TableName("ml_log")
public class LogPO {
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 项目标记
     */
    private String projFlag;

    /**
     * 租户标记
     */
    private String tenantFlag;

    /**
     * 业务标识
     */
    private String bizFlag;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reqTime;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date respTime;

    /**
     * 响应状态码
     */
    private String respCode;

    /**
     * 响应信息
     */
    private String respMsg;

    /**
     * 记录落表时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ctime;
}
