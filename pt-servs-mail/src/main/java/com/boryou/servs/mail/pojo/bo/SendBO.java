package com.boryou.servs.mail.pojo.bo;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @description 发送邮件BO
 * <AUTHOR>
 * @date 2024/7/10 17:01
 */

@Data
public class SendBO {
    private static final long serialVersionUID = 1L;

    /**
     * 接收人
     */
    private String receiver;
    /**
     * 主题
     */
    private String theme;
    /**
     * 邮件内容
     */
    private String content;
    /**
     * 邮件类型 1文本 2 html
     */
    private String mailType;
    /**
     * 附件列表*
     */
    private List<MultipartFile> files;
}
