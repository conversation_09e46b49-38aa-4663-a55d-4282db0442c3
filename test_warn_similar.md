# WarnSimilar 功能测试说明

## 功能描述
在 `warnSimilar` 方法中实现了一个 ES 查询，用于统计指定时间范围内各个 md5 的数量。

## 实现要点

### 1. 查询条件
- **md5 过滤**: 使用 `terms` 查询只查询指定的 md5 列表
- **时间范围过滤**: 使用 `range` 查询过滤 `publishTime` 在 `warnDateStart` 和 `warnDateEnd` 之间的数据

### 2. 聚合统计
- 使用 `terms` 聚合按 md5 分组
- 统计每个 md5 的文档数量

### 3. 返回结果
- 返回 `Map<String, Integer>` 格式
- Key: md5 值
- Value: 该 md5 在指定时间范围内的数量

## API 接口

### 请求地址
```
POST /api/warn/similar
```

### 请求参数 (WarnDataVO)
```json
{
  "warnDateStart": "2024-01-01 00:00:00",
  "warnDateEnd": "2024-01-31 23:59:59",
  "md5s": ["md5_1", "md5_2", "md5_3"]
}
```

### 响应示例
```json
{
  "md5_1": 5,
  "md5_2": 3,
  "md5_3": 8
}
```

## 测试建议

1. **正常情况测试**
   - 提供有效的时间范围和 md5 列表
   - 验证返回的统计数量是否正确

2. **边界情况测试**
   - 空的 md5 列表
   - 无效的时间范围
   - 不存在的 md5 值

3. **性能测试**
   - 大量 md5 列表的处理
   - 长时间范围的查询

## 注意事项

1. 确保 ES 索引中存在 `md5` 和 `publishTime` 字段
2. 时间格式需要与 ES 中存储的格式一致
3. 建议对 md5 列表大小进行限制，避免查询过大
