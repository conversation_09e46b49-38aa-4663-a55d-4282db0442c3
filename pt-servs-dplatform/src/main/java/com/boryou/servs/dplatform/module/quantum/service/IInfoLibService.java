package com.boryou.servs.dplatform.module.quantum.service;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibBO;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibSearchBO;
import com.boryou.servs.dplatform.module.quantum.bo.SearchInfoLibBO;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-07-30 09:38
 */
public interface IInfoLibService {

    PageResult<InfoLibBO> list(SearchInfoLibBO esSearchBO) throws IOException;

    Map<Long,String> add(List<InfoLibBO> infoLibBOs);
    Map<Long,String> edit(List<InfoLibBO> infoLibBOs);

    boolean delete(List<InfoLibBO> infoLibBO) throws IOException;

    boolean batchDelete(SearchInfoLibBO urlList) throws IOException;

    List<InfoLibBO> getInfoLibListById(InfoLibSearchBO infoLibSearchBO) throws IOException;

    public boolean libInfoClassUpdate(List<String> esIds, Integer infoTag,List<String>indexs);

    boolean removeInfoLibTagids(List<String> ids);
}

