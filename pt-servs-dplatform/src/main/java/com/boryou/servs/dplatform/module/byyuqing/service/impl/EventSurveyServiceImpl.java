package com.boryou.servs.dplatform.module.byyuqing.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.impl.EsCommonServiceImpl;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.module.byyuqing.service.EventSurveyService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.SortBO;
import com.boryou.servs.dplatform.util.CommonUtil;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-08 13:32
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EventSurveyServiceImpl implements EventSurveyService {

    private final ElasticsearchClient esClient;

    @Override
    public PageResult<EsBean> getSiteMetaInfo(EsSearchBO esSearchBO) {
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        String siteMeta = esSearchBO.getSiteMeta();
        if (StrUtil.isNotEmpty(siteMeta)) {
            List<String> siteMetas = Arrays.asList(siteMeta.split(","));
            if (!siteMetas.isEmpty()) {
                List<FieldValue> fieldValues = siteMetas.stream().map(FieldValue::of).collect(Collectors.toList());
                builder.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.SITE_META.getFieldName()).terms(q -> q.value(fieldValues))));
            }
        }
        List<SortOptions> sorts=new ArrayList<>();
        List<SortBO> sortField = esSearchBO.getSorts();
        if (CollectionUtil.isNotEmpty(sortField)) {
            for (SortBO sortBO : sortField) {
                sorts.add(SortOptionsBuilders.field(f -> f.field(sortBO.getSortField()).order(CommonUtil.getSort(sortBO.getSortType()))));
            }
        }
        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(EsUtil.getIndexes(esSearchBO))
                .query(builder.build()._toQuery()).
                highlight(EsUtil.getHighlight(-1))
                .size(esSearchBO.getPageSize())
                .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                .sort(sorts)
        );

        // 执行查询
        List<EsBean> results = new ArrayList<>();
        try {
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            EsCommonServiceImpl.getBeansFromHighlight(results, response,110);
            pageResult.setRecords(results);
            pageResult.setTotal(response.hits().total().value());
        } catch (IOException e) {
            log.error("searchByAuthor查询有问题");
        }
        return pageResult;
    }
}

