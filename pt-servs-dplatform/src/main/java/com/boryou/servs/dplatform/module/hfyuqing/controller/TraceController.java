package com.boryou.servs.dplatform.module.hfyuqing.controller;

import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-05-11 16:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/weiboTrace/analyze")
public class TraceController {


    private final AreaDistributionService areaDistributionService;

    /**
     * 获取相似度数据
     *
     * @param params
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/26 14:56
     **/
    @PostMapping("/getSimilarData")
    public PageInfo<IndexResultBean> getSimilarData(@RequestBody Map<String, Object> params) {
        StatisticsBean statisBean = JSONObject.parseObject(JSONObject.toJSONString(params), StatisticsBean.class);
        return areaDistributionService.getPropagationListData(statisBean);
    }
    /**
     *获取作者地域分布下的信息列表
     *  @param params
     * <AUTHOR>
     * @date 2024/5/13 10:29
     * @return com.boryou.servs.dplatform.pojo.bean.PageInfo<com.boryou.servs.dplatform.module.external.bo.IndexResultBean>
     **/
    @PostMapping("/getAreaAuthorInfo")
    public PageInfo<IndexResultBean> getAreaAuthorInfo(@RequestBody Map<String, Object> params) {
        List<String> areaCodes = null;
        if (params.get("areaCodes") != null) {
            areaCodes = (List<String>) params.get("areaCodes");
        }
        StatisticsBean statisBean = JSONObject.parseObject(JSONObject.toJSONString(params.get("statisBean")), StatisticsBean.class);
        return areaDistributionService.getAreaAuthorInfo(statisBean, statisBean.getAggName(), areaCodes);
    }

}

