package com.boryou.servs.dplatform.module.external.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class IndexResultBean {

    private String id;

    private String title;

    private String text;

    private String host;

    private String biz;

    private String url;

    private String author;

    private Long wbAuthorId;

    private String authorId;

    private String time;

    private String submitTime;

    private Integer type;

    private Integer emotional;

    private Boolean spamFlag;

    private List<String> picUrl;

    private List<String> vidUrl;

    private Boolean deleted;

    private String MD5;

    private String siteAddress = "";

    private String articleSource;// 文章来源

    private Integer commentGoodsNum;// 点赞数

    private String authorPortraitLink; // 主贴作者头像链接

    private int trash;
    private List<String> simHashes;

    private String sourceUrl;

    private List<Integer> siteArea;

    private Long sourceWbId;

    private List<Integer> textArea;

    private Integer date;

    private Integer childType;

    private List<Integer> contentType;

    private Integer province;

    private List<String> siteMeta;
    private List<String> hitWord;
    private String pcUrl;
     //评论数
    private Integer commentNum;
     //转发数
    private Integer reprintNum;
     //阅读数
    private Integer readNum;
     //媒体地域/作者地域 编码
    private Integer city;
     //内容提及地域
    private List<String> textAddress;
     //源码
    private String mapPath;
     //音视频链接
    private String audioFile;
     //作者性别 0女1男2未知（默认未知）
    private Integer authorSex;
     //作者认证  值为4为未认证，其他值则为已认证，0达人，1蓝v，2红v，3橙v
    private Integer weiBoLevel;
     //作者粉丝数
    private Integer fansCount;
     //是否原创
    private Boolean original;

    private List<String> keywords;

    private String siteName;
    private List<String> provinceNames = new ArrayList<>();
    private List<String> cityNames = new ArrayList<>();
    //媒体地域/作者地域
    private String provinceLabelName;
    //媒体地域/作者地域
    private String cityLabelName;
    //短视频媒体类型
    private String videoType;
}
