package com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Like;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.SearchService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

/**
 * <AUTHOR>
 * @date 2024-07-08 13:14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SearchServiceImpl implements SearchService {
    private final ElasticsearchClient esClient;

    @SneakyThrows
    @Override
    public PageResult<EsBean> similarityQuery(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        List<String> indexes = EsUtil.getIndexes(esSearchBO);
        String id = esSearchBO.getId();
        ArrayList<Like> objects = new ArrayList<>();
        esSearchBO.setId(null);
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        for (String index : indexes) {
            objects.add(Like.of(s->s.document(t-> t.index(index).id(id))));
        }
        builder.must(s->s.moreLikeThis(c->c.fields("title","text").like(objects).
                minTermFreq(1).maxQueryTerms(20).minDocFreq(2).minimumShouldMatch("95%")));
        SearchRequest searchRequest = SearchRequest.of(
                s -> s.index(indexes).query(builder.build()._toQuery())
                        .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                        .size(esSearchBO.getPageSize())
                        .sort(sortOptions).trackTotalHits(t -> t.enabled(true))
        );

        SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),searchRequest,passedTime);
        }
        List<EsBean> dataToMove = response.hits().hits().stream().map(Hit::source).collect(Collectors.toCollection(ArrayList::new));
        pageResult.setRecords(dataToMove);
        pageResult.setTotal(response.hits().total().value());
        return  pageResult;
    }
}

