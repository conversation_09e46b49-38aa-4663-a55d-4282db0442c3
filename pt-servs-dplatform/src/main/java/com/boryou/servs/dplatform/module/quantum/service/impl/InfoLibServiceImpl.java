package com.boryou.servs.dplatform.module.quantum.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.*;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.EsCommonService;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.quantum.bean.InfoLibESBean;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibBO;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibSearchBO;
import com.boryou.servs.dplatform.module.quantum.bo.SearchInfoLibBO;
import com.boryou.servs.dplatform.module.quantum.bo.SearchTime;
import com.boryou.servs.dplatform.module.quantum.service.IInfoLibService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

/**
 * <AUTHOR>
 * @date 2024-07-30 09:39
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class InfoLibServiceImpl implements IInfoLibService {
    private final ElasticsearchClient esClient;

    private final EsCommonService esCommonService;
    private final ExecutorService executorService = new ThreadPoolExecutor(
            12, // 核心线程数
            200, // 最大线程数
            10L, // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(600) // 任务队列容量
    );

    @Override
    public PageResult<InfoLibBO> list(SearchInfoLibBO infoLibBO) throws IOException {
//        SearchResponse<InfoLibBO> response = getSearchList(infoLibBO);
        PageResult<InfoLibBO> response = getSearchList(infoLibBO);
//        HitsMetadata<InfoLibBO> hits = response.hits();
        List<InfoLibBO> newData = new ArrayList<>(10);
        for (InfoLibBO bean : response.getRecords()) {
//            InfoLibBO bean = hit.source();
            if (bean != null) {
                String title = HtmlUtil.cleanHtmlTag(bean.getTitle());
                bean.setTitle(title);
//                String text = HtmlUtil.cleanHtmlTag(bean.getText());
//                bean.setText(text);
            }
            newData.add(bean);
        }
        return new PageResult<>(newData,response.getTotal());
    }


    private PageResult<InfoLibBO> getSearchList(SearchInfoLibBO infoLibBO) throws IOException {
        long start = System.currentTimeMillis();
        EsSearchBO esSearchBO = infoLibBO.getEsSearchBO();
        InfoLibBO infoLib = infoLibBO.getInfoLib();
        BoolQuery.Builder builder = QueryBuilders.bool();
        if (CollUtil.isNotEmpty(infoLib.getIds())) {
            builder.must(s -> s.terms(t -> t.field("id").terms(q -> q.value(Arrays.stream(infoLib.getIds().toArray(new String[]{})).map(FieldValue::of).collect(Collectors.toList())))));
        }
        if (null != infoLib.getPlanId()) {
            builder.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.PLANIDS.getFieldName())
                    .terms(q -> q.value(Arrays.stream(new String[]{String.valueOf(infoLib.getPlanId())}).map(FieldValue::of).collect(Collectors.toList())))));
        }
        List<Query>must=new ArrayList<>();
        if (CollUtil.isNotEmpty(infoLib.getTargetTag())) {
            builder.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TAG_IDS.getFieldName())
                    .terms(q -> q.value(Arrays.stream(infoLib.getTargetTag().toArray(new String[]{})).map(FieldValue::of).collect(Collectors.toList())))));
        }else{
//            String targetTag = infoLibBO.getEsSearchBO().getTargetTag();
//            if (StrUtil.isNotEmpty(targetTag)) {
//                if ("-1".equals(targetTag)) {
                    //pc端默认查未分类的数据，排除掉分类过的数据
                    must.add(QueryBuilders.exists(s -> s.field(EsBeanFieldEnum.TAG_IDS.getFieldName())));
                    must.add(QueryBuilders.exists(s -> s.field(EsBeanFieldEnum.PLANIDS.getFieldName())));
//                } else {
//                    builder.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TAG_IDS.getFieldName()).terms(q -> q.value(Arrays.stream(targetTag.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
//                }
//            }
        }
        if (StrUtil.isNotEmpty(infoLib.getKeyWord())) {
            QueryStringQuery.Builder keyWordQb1 = EsUtil.buildKeyWord(EsUtil.quotedEveryKeyWord(infoLib.getKeyWord(), true), "1,2");
            builder.filter(keyWordQb1.build()._toQuery());
        }


        if (null != infoLib.getStartTime() && null != infoLib.getEndTime()) {
            Query range = QueryBuilders.range(r -> r.field("publishTime").from(esSearchBO.getStartTime()));
            builder.filter(range);
            Query range1 = QueryBuilders.range(r -> r.field("publishTime").to(esSearchBO.getEndTime()));
            builder.filter(range1);
        }
        String type = infoLib.getTypes();
        if (StrUtil.isNotEmpty(type)) {
            builder.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName())
                    .terms(q -> q.value(Arrays.stream(type.split(",")).map(FieldValue::of).collect(Collectors.toList())))));


        }
        if (!must.isEmpty()){
            builder.must(QueryBuilders.bool().should(must).build()._toQuery());
        }

    //信息库页面默认展示去重数据

        final String countAgg = "countAgg";
        final String md5Agg = "md5Agg";
        final String topAgg = "topAgg";
        final String sortTimeAgg = "sortTimeAgg";
        NamedValue<SortOrder> namedValue  = NamedValue.of(sortTimeAgg, SortOrder.Desc);
        Aggregation aggSort ;
            //取时间最早的一条
            aggSort = AggregationBuilders.max(t -> t.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()));

        SortOptions sortOptions  = SortOptionsBuilders.field(f -> f.field("publishTime").order(SortOrder.Desc));
        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(EsUtil.getIndexes(esSearchBO))
                .query(builder.build()._toQuery())
                .size(0)
                .aggregations(countAgg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())))
                .aggregations(md5Agg, a -> a.terms(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())
                                .size(esSearchBO.getPageNum() != 0 && esSearchBO.getPageSize() != 0 ? esSearchBO.getPageNum() * esSearchBO.getPageSize() : 1)
                                .order(namedValue))
                        .aggregations(topAgg, top -> top.topHits(t -> t.size(1).sort(sortOptions)))
                        .aggregations( sortTimeAgg , aggSort)));




//        SearchRequest searchRequest = SearchRequest.of(
//                s -> s.index(esSearchBO.getIndexs()).query(builder.build()._toQuery())
//                        .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
//                        .size(esSearchBO.getPageSize())
//                        .sort(sortOptions).trackTotalHits(t -> t.enabled(true))
//        );
        SearchResponse<InfoLibBO> response = esClient.search(searchRequest, InfoLibBO.class);
        List<InfoLibBO> results = new ArrayList<>();
        PageResult<InfoLibBO> pageResult = new PageResult<>();
        if (response != null) {
            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate aggregate1 = aggregations.get(md5Agg);
            List<StringTermsBucket> array1 = ((StringTermsAggregate) aggregate1._get()).buckets().array();
            InfoLibBO bean;
            for (StringTermsBucket bucket : array1) {
                HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                for (Hit<JsonData> hit : hits.hits()) {
                    if (hit.source() != null) {
                        bean = hit.source().to(InfoLibBO.class);
                        bean.setSimilarCount((int) hits.total().value());
                        results.add(bean);
                    }
                }
            }
            results = results.subList((esSearchBO.getPageNum() -1)*esSearchBO.getPageSize(), results.size());
            pageResult.setRecords(results);
            Aggregate aggregate = aggregations.get(countAgg);
            pageResult.setTotal(((CardinalityAggregate) aggregate._get()).value());
        }
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        if (passedTime > INTERFACE_TIME_OUT) {
        }
        return pageResult;
    }

    public static Date findMaxDate(List<SearchTime> objs) {
        return objs.stream()
                .map(obj -> obj.getTime())
                .max(Date::compareTo)
                .orElse(null);
    }

    public static Date findMinDate(List<SearchTime> objs) {
        return objs.stream()
                .map(obj -> obj.getTime())
                .min(Date::compareTo)
                .orElse(null);
    }


    private static void getQueryTypes(InfoLibBO notice, Map<String, List<SearchTime>> weekList) {
        Date startTime = notice.getStartTime();
        Date now = new Date();
        Date oneWeekAgo = DateUtil.offsetDay(now, -7);
        Date oneWeekTemp = DateUtil.offsetDay(now, -8);
        Date oneMonthAgo = DateUtil.offsetDay(now, -30);
        if (startTime.after(oneWeekAgo)) {
            List<SearchTime> list= new ArrayList<>(Collections.singletonList(new SearchTime(notice.getDocIndexId(), startTime)));
            // 发文开始结束时间都在一周内
            if (weekList.containsKey(ESConstant.ES_INDEX_WEEK)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_WEEK);
                ArrayList<SearchTime> c = new ArrayList<>(searchTimes);
                list.addAll(c);
                weekList.put(ESConstant.ES_INDEX_WEEK, list);
            } else {
                weekList.put(ESConstant.ES_INDEX_WEEK, list);
            }
        } else if (startTime.after(oneMonthAgo)) {
            if (startTime.after(oneWeekTemp)){
                List<SearchTime> list = new ArrayList<>(Collections.singletonList(new SearchTime(notice.getDocIndexId(), startTime)));
                //为了解决7天前的数据还没迁移到月节点，做的一次周、月节点双查的兼容性查询，保证一定能查到
                if (weekList.containsKey(ESConstant.ES_INDEX_WEEK)) {
                    List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_WEEK);
                    ArrayList<SearchTime> c = new ArrayList<>(searchTimes);
                    list.addAll(c);
                    weekList.put(ESConstant.ES_INDEX_WEEK, list);
                } else {
                    weekList.put(ESConstant.ES_INDEX_WEEK, list);
                }
        }
            List<SearchTime> list= new ArrayList<>(Collections.singletonList(new SearchTime(notice.getDocIndexId(), startTime)));
            if (weekList.containsKey(ESConstant.ES_INDEX_MONTH)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX_MONTH);
                list.addAll(new ArrayList<>(searchTimes));
                weekList.put(ESConstant.ES_INDEX_MONTH, list);
            } else {
                weekList.put(ESConstant.ES_INDEX_MONTH, list);
            }
        } else {
            List<SearchTime> list= new ArrayList<>(Collections.singletonList(new SearchTime(notice.getDocIndexId(), startTime)));
            if (weekList.containsKey(ESConstant.ES_INDEX)) {
                List<SearchTime> searchTimes = weekList.get(ESConstant.ES_INDEX);
                list.addAll(new ArrayList<>(searchTimes));
                weekList.put(ESConstant.ES_INDEX, list);
            } else {
                weekList.put(ESConstant.ES_INDEX, list);
            }
        }
    }
    /**
     *量子的元数据添加到方案管理里，10.8号确定可以不用分三大分开查，全库查询
     *  @param infoLibBO
     * <AUTHOR>
     * @date 2024/10/8 9:18
     **/
    @Override
    public Map<Long, String> add(List<InfoLibBO> infoLibBO) {
        Map<Long, String> addMaps = new HashMap<>();
        if (CollUtil.isEmpty(infoLibBO)) {
            return addMaps;
        }
        long start1=System.currentTimeMillis();
        Map<String, List<SearchTime>> weekList = new HashMap<>();
        try {
        List<EsBean> search = new ArrayList<>();
            List<SearchTime> lists=new ArrayList<>();
        for (InfoLibBO libBO : infoLibBO) {
            if (null == libBO.getStartTime()) {
                continue;
            }
//            getQueryTypes(libBO, weekList);
            Date startTime = libBO.getStartTime();
            List<SearchTime> list = new ArrayList<>(Collections.singletonList(new SearchTime(libBO.getDocIndexId(), startTime)));
            // 发文开始结束时间都在一周内
            lists.addAll(list);
        }
        weekList.put(ESConstant.JOIN_NETXMAN_YQ_ALL, lists);
        Set<String> weekSet = weekList.keySet();
        List<String>weekStr=new ArrayList<>();
        for (String string : weekSet) {
            List<SearchTime> searchTimes = weekList.get(string);
            if (CollUtil.isEmpty(searchTimes)){
                continue;
            }
            weekStr.add(string);
            EsSearchBO bo = new EsSearchBO();
            bo.setId(CollUtil.join(searchTimes.stream().map(SearchTime::getId).collect(Collectors.toList()), ","));
            bo.setIndexs(Arrays.asList(string.split(",")));
//            bo.setStartTime(DateUtil.formatDateTime(findMinDate(searchTimes)));
//            bo.setEndTime(DateUtil.formatDateTime(findMaxDate(searchTimes)));
//            bo.setPageNum(1);
//            bo.setPageSize(9999);
            PageResult<EsBean> search1 = esCommonService.queryScrollAllData(bo);
            search.addAll(search1.getRecords());
        }
        Map<Long, EsBean> collect1 = search.stream().collect(Collectors.toMap(c -> Long.parseLong(c.getId()), s -> s));
        if (MapUtil.isEmpty(collect1)) {
            return addMaps;
        }
           log.info("add查询耗时：-----"+(System.currentTimeMillis()-start1)/1000);
            long start2=System.currentTimeMillis();
             BulkRequest bulkRequest = BulkRequest.of(b -> {
            for (InfoLibBO data : infoLibBO) {
                Long docIndexId = data.getDocIndexId();
                EsBean warnEs = collect1.get(docIndexId);
                if (null == warnEs) {
                    continue;
                }
                InfoLibESBean bean = JSONUtil.toBean(JSONUtil.parseObj(warnEs, true), InfoLibESBean.class);
//                bean.setCreateTime(new Date());
//                bean.setPlanId(data.getPlanId());
//                String id = String.valueOf(IdUtil.getSnowflake().nextId());
//                bean.setId(id);
//                bean.setInfoId(docIndexId);
//                List<String> contentMeta = bean.getContentMeta();
//                List<String> targetTag = data.getTargetTag();
//                contentMeta.addAll(targetTag);
                //更新关联方案和二级所属分类
                List<String> targetTag = data.getTargetTag();
                if (CollUtil.isEmpty(targetTag)){
                    targetTag=data.getTagIds();
                    if (CollUtil.isEmpty(targetTag)){
                        throw new RuntimeException("参数异常！");
                    }
                }
                bean.setAddType("0");
                bean.setMarkSource(data.getMarkSource()==null?0:data.getMarkSource());//元数据打标标记，方便infolib回到元数据中,默认是0，只有一种方案管理中修改时导致的add就改为1，标记之前是从方案管理中添加的。
                bean.setTagIds(targetTag);
                bean.setPlanIds(data.getPlanIds());
                bean.setAddTime(new Date()); //更新不生效
                addMaps.put(docIndexId, String.valueOf(docIndexId));
                b.operations(op -> op.index(idx -> idx.index(ESConstant.ES_INDEX_INFOLIB).id(String.valueOf(docIndexId)).document(bean)));
            }
            return b;
        });

            BulkResponse bulk = esClient.bulk(bulkRequest);
            if (bulk.errors()) {
                long took = bulk.took();
                List<BulkResponseItem> items = bulk.items();
                for (BulkResponseItem item : items) {
                    ErrorCause error = item.error();
                    if (error != null) {
                        if (item.id() != null) {
                            addMaps.remove(item.id());
                        }
                        log.info("/quantum/lib/add 入库失败, took：{},error:{}", took, error);
                    }
                }
            }
            List<String>ids=new ArrayList<>();
            Set<Long> longs = addMaps.keySet();
            for (Long aLong : longs) {
                ids.add(String.valueOf(aLong));
            }
            log.debug("esClient.bulk耗时{}-----添加:{}",(System.currentTimeMillis()-start2)/1000,addMaps.size());
            libInfoClassUpdate(ids,1, ESConstant.ALL_INDEX);
            return addMaps;
        } catch (Exception e) {
            log.error("/quantum/lib/add 入库失败: {}", e.getMessage());
            return null;
        }
    }


    @Override
    public boolean removeInfoLibTagids(List<String> esIds) {
        if (CollUtil.isEmpty(esIds)) {
            return false;
        }
        try {
            UpdateByQueryRequest.Builder builder = new UpdateByQueryRequest.Builder();
            EsUtil.simpleTermQuery(esIds, builder, "id");
            InlineScript.Builder script = new InlineScript.Builder();
            script.source("ctx._source.remove('tagIds');ctx._source.remove('planIds')");
            builder.script(item -> item.inline(script.build()));
            builder.index(ESConstant.ES_INDEX_INFOLIB);
            UpdateByQueryRequest build = builder.build();
            UpdateByQueryResponse updateByQueryResponse = esClient.updateByQuery(build);
            Long updated = updateByQueryResponse.updated();
            log.info("更新结果{}", updated);
            //refresh 方法用于强制刷新索引，使得新添加、更新或删除的文档能够立即被搜索到。
            esClient.indices().refresh(s -> s.index(ESConstant.ES_INDEX_INFOLIB));
            ThreadUtil.sleep(50);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     *元数据-方案的打标区分
     *  @param esIds
     * @param infoTag
     * <AUTHOR>
     * @date 2024/10/9 10:18
     * @return boolean
     **/
    public boolean libInfoClassUpdate(List<String> esIds, Integer infoTag,List<String>indexs) {
        if (CollUtil.isEmpty(esIds) || infoTag == null) {
            return false;
        }
        try {
            UpdateByQueryRequest.Builder builder = new UpdateByQueryRequest.Builder();
            EsUtil.simpleTermQuery(esIds, builder, "id");
            InlineScript.Builder script = new InlineScript.Builder();
            script.source("ctx._source.infoTag = " + infoTag);
//            script.source("if (ctx._source.containsKey('infoTag')) { ctx._source.infoTag = "+infoTag+"; } else { ctx._source.infoTag = "+infoTag+"; }");
            builder.script(item -> item.inline(script.build()));
            builder.index(indexs);
            UpdateByQueryRequest build = builder.build();
            UpdateByQueryResponse updateByQueryResponse = esClient.updateByQuery(build);
            Long flag = updateByQueryResponse.updated();
            log.info("更新结果:{}", flag > 0);
            //refresh 方法用于强制刷新索引，使得新添加、更新或删除的文档能够立即被搜索到。
            esClient.indices().refresh(s -> s.index(indexs));
            ThreadUtil.sleep(50);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
    /**
     *infolib二级分类更新
     * <AUTHOR>
     * @date 2024/9/29 9:52
     * @@see https://www.cnblogs.com/xzshare/p/14215295.html
     **/
    public boolean infolibTagUpdate(List<String> esIds, InfoLibESBean infoLib) {
        if (CollUtil.isEmpty(esIds) || infoLib == null) {
            return false;
        }
        try {
            UpdateByQueryRequest.Builder builder = new UpdateByQueryRequest.Builder();
            EsUtil.simpleTermQuery(esIds, builder, "id");
            InlineScript.Builder script = new InlineScript.Builder();
            String updateSql="";
            if (null!=infoLib.getMarkSource()&&infoLib.getMarkSource()==1){
                updateSql="ctx._source.markSource='1';";    //如果是从方案进来的，更新markSource=1，方便infolib数据回到方案管理中
            }
            script.source("ctx._source.addTime ='"+DateUtil.formatDateTime(new Date())+"';"+updateSql+"def tags= ctx._source.tagIds;def newTag=params.tagInfo;def tags1= ctx._source.planIds;def newTag1=params.planIds;if (tags == null) {  ctx._source.tagIds = params.tagInfo;} else { tags.removeAll(tags);for(def t : newTag){ tags.add(t);}}if (tags1 == null) {  ctx._source.planIds = params.planIds;} else { tags1.removeAll(tags1);for(def t : newTag1){ tags1.add(t);}}");
            HashMap<String, JsonData> map = new HashMap<>();
            map.put("tagInfo",JsonData.fromJson(JSONUtil.parseArray(infoLib.getTagIds()).toString()) );
            map.put("planIds",JsonData.fromJson(JSONUtil.parseArray(infoLib.getPlanIds()).toString()) );
            script.params(map);
            builder.script(item -> item.inline(script.build()));
            builder.index(ESConstant.ES_INDEX_INFOLIB);
            UpdateByQueryRequest build = builder.build();
            UpdateByQueryResponse updateByQueryResponse = esClient.updateByQuery(build);
            Long updated = updateByQueryResponse.updated();
//            if (CollUtil.isNotEmpty(weekStr)) {
                //refresh 方法用于强制刷新索引，使得新添加、更新或删除的文档能够立即被搜索到。
                esClient.indices().refresh(s -> s.index(ESConstant.ES_INDEX_INFOLIB));
                ThreadUtil.sleep(50);
//            }
            return updated!=null&&updated>0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }



    @Override
    public boolean delete(List<InfoLibBO> infoLibBO) throws IOException {
        if (CollUtil.isEmpty(infoLibBO)) {
            return false;
        }
        BulkRequest deleteRequest = BulkRequest.of(b -> {
            for (InfoLibBO id : infoLibBO) {
                b.operations(op -> op.delete(del -> del.index(ESConstant.ES_INDEX_INFOLIB).id(id.getId())));
            }
            return b;
        });
        try {
            BulkResponse bulk = esClient.bulk(deleteRequest);
            long took = bulk.took();
            List<BulkResponseItem> items = bulk.items();
            for (BulkResponseItem item : items) {
                ErrorCause error = item.error();
                if (error != null) {
                    log.info("/quantum/lib/delete 删除失败, took：{},error:{}", took, error);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("/quantum/lib/delete 删除失败: {}", e.getMessage());
        }
        return true;
    }

    @Override
    public boolean batchDelete(SearchInfoLibBO searchInfoLibBO) throws IOException {
//        SearchResponse<InfoLibBO> response = getSearchList(searchInfoLibBO);
        PageResult<InfoLibBO> response = getSearchList(searchInfoLibBO);
        // 用线程池执行批量删除
        CompletableFuture<BulkResponse> bulkDeleteFuture = CompletableFuture.supplyAsync(() -> {
            List<String> idsToDelete = response.getRecords().stream()
                    .map(InfoLibBO::getId).collect(Collectors.toList());
            BulkRequest deleteRequest = BulkRequest.of(b -> {
                for (String id : idsToDelete) {
                    b.operations(op -> op.delete(del -> del.index(ESConstant.ES_INDEX_INFOLIB).id(id)));
                }
                return b;
            });
            try {
                log.info("批量删除{}个", idsToDelete.size());
                return esClient.bulk(deleteRequest);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, executorService);
        // 等待所有操作完成
        CompletableFuture.allOf(bulkDeleteFuture).join();
        return true;
    }

    @Override
    public List<InfoLibBO> getInfoLibListById(InfoLibSearchBO idlList) throws IOException {
//        String planId = idlList.getPlanId();
        List<String> urlList = idlList.getInfoIdList();
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.terms(s -> s.field("id").terms(t -> t.value(urlList.stream().map(FieldValue::of).collect(Collectors.toList())))));
//        if (null != planId) {
//            builder.must(QueryBuilders.term(s -> s.field("planId").value(planId)));
//        }
//        SortOptions sortOptions = EsUtil.getSortOptions("-1");
        SearchRequest searchRequest = SearchRequest.of(
                s -> s.index(ESConstant.ES_INDEX_INFOLIB).query(builder.build()._toQuery())
                        .size(1000)
                       /* .sort(sortOptions)*/.trackTotalHits(t -> t.enabled(false))
        );
        SearchResponse<InfoLibBO> response = esClient.search(searchRequest, InfoLibBO.class);
        List<InfoLibBO> dataToMove = response.hits().hits().stream().map(Hit::source).collect(Collectors.toCollection(ArrayList::new));
        for (InfoLibBO infoLibBO : dataToMove) {
            infoLibBO.setText(null);//减少流量带宽
            infoLibBO.setTitle(null);
        }
        return dataToMove;
    }

    /**
     *infolib的二级标签修改
     * 传入数据id集合，先进行查找所有相同md5数据，然后进行更新tagIds分类和planId 字段
     *  @param infoLibBO
     * <AUTHOR>
     * @date 2024/10/8 9:18
     **/
    @Override
    public Map<Long, String> edit(List<InfoLibBO> infoLibBO) {
        Map<Long, String> addMaps = new HashMap<>();
        if (CollUtil.isEmpty(infoLibBO)) {
            return addMaps;
        }
        Map<String, List<SearchTime>> weekList = new HashMap<>();
        boolean isOriginal = infoLibBO.get(0).isOriginalFlag();
        Integer markSource = infoLibBO.get(0).getMarkSource();
        try {
            List<EsBean> search = new ArrayList<>();
            List<SearchTime> list1 = new ArrayList<>();
            for (InfoLibBO libBO : infoLibBO) {
                if (null == libBO.getStartTime()) {
                    continue;
                }
//                getQueryTypes(libBO, list1, weekList);
                SearchTime e = new SearchTime();
                e.setId(libBO.getDocIndexId());
                e.setTime(libBO.getStartTime());
                e.setMd5(libBO.getMd5());
                list1.add(e);
            }
            //修改操作一定是针对infolib索引操作的，所以要查询的索引数据也是确定的
            weekList.put(ESConstant.ES_INDEX_INFOLIB,list1);
            Set<String> weekSet = weekList.keySet();
            List<String>weekStr=new ArrayList<>();
            for (String string : weekSet) {
                List<SearchTime> searchTimes = weekList.get(string);
                if (CollUtil.isEmpty(searchTimes)){
                     continue;
                }
                weekStr.add(string);
                EsSearchBO bo = new EsSearchBO();
//                if (isOriginal){
//                    bo.setMd5(CollUtil.join(searchTimes.stream().map(SearchTime::getMd5).collect(Collectors.toList()), ","));
//                }else{
                //TODO 修改的话-因为编辑入口已经根据Md5查询出相似的数据过来，所以这里直接根据id来更新就可以
                    bo.setId(CollUtil.join(searchTimes.stream().map(SearchTime::getId).collect(Collectors.toList()), ","));
//                }
                bo.setIndexs(Arrays.asList(string.split(",")));
//                bo.setStartTime(DateUtil.formatDateTime(findMinDate(searchTimes)));
//                bo.setEndTime(DateUtil.formatDateTime(findMaxDate(searchTimes)));
//                bo.setPageNum(1);
//                bo.setPageSize(9999);
                PageResult<EsBean> search1 = esCommonService.queryScrollAllData(bo);
                search.addAll(search1.getRecords());
            }
            Map<Long, EsBean> collect1 = search.stream().collect(Collectors.toMap(c -> Long.parseLong(c.getId()), s -> s));
            if (MapUtil.isEmpty(collect1)) {
                return addMaps;
            }
            InfoLibESBean updateBean = new InfoLibESBean();
            for (InfoLibBO data : infoLibBO) {
                Long docIndexId = data.getDocIndexId();
                EsBean warnEs = collect1.get(docIndexId);
                if (null == warnEs) {
                    continue;
                }
//                InfoLibESBean bean = JSONUtil.toBean(JSONUtil.parseObj(warnEs, true), InfoLibESBean.class);
                //更新关联方案和二级所属分类
                List<String> targetTag = data.getTargetTag();
                List<String> planIds = data.getPlanIds();
                if (CollUtil.isEmpty(targetTag)||CollUtil.isEmpty(planIds)){
                    throw new RuntimeException("pt参数异常");
                }
                updateBean.setTagIds(targetTag);
                updateBean.setPlanIds(planIds);
                updateBean.setMarkSource(markSource);
//                updateBean = bean;
                addMaps.put(docIndexId, String.valueOf(docIndexId));
            }
            List<String> ids = new ArrayList<>();
            Set<Long> longs = addMaps.keySet();
            for (Long aLong : longs) {
                ids.add(String.valueOf(aLong));
            }
            infolibTagUpdate(ids, updateBean);
            log.info("更新二级分类:{}条",ids.size());
            return addMaps;
        } catch (Exception e) {
            log.error("/quantum/lib/add 入库失败: {}", e.getMessage());
            return null;
        }
    }
}

