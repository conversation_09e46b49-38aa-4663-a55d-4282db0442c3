package com.boryou.servs.dplatform.module.hfyuqing.controller;

import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.module.hfyuqing.service.SubjectAnalyseService;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 合肥舆情-专题监测-地域分布
 *
 * <AUTHOR>
 * @date 2024-04-29 15:22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/subjectAnalyseEChart/analyze")
public class SubjectAnalyseController {

    private final SubjectAnalyseService subjectAnalyseService;
    private final AreaDistributionService areaDistributionService;

    /**
     * 地域分布-es版本
     *
     * @param statisticsBean
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/26 14:56
     **/
    @PostMapping("/getAreaData")
    public Map<String, Long> getAreaData(@RequestBody StatisticsBean statisticsBean) {
        return areaDistributionService.getAreaDistribution(statisticsBean, "contentAreaCode");
    }

    /**
     * 地域分布-es版本
     *
     * @param params
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/26 14:56
     **/
    @PostMapping("/getAreaCodeData")
    public Map<String, Long> getAreaCodeData(@RequestBody Map<String, Object> params) {
        List<String> areaCodes = (List<String>) params.get("areaCodes");
        StatisticsBean statisBean = JSONObject.parseObject(JSONObject.toJSONString(params.get("statisBean")), StatisticsBean.class);
        return areaDistributionService.getAreaCodeDistribution(statisBean, "contentAreaCode", areaCodes);
    }

    /**
     * 合肥舆情-传播路径-获取匹配文档最新的开始时间字符串
     *
     * @param analysisSubject 参数对象
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/5/6 13:34
     **/
    @PostMapping("/getPropagationStartTime")
    public String getPropagationStartTime(@RequestBody StatisticsBean analysisSubject) {
        return areaDistributionService.getPropagationStartTime(analysisSubject);
    }

    /**
     * 专题检测-跳转至传播路径图信息列表
     *
     * @param analysisSubject
     * @return 分页对象
     * <AUTHOR>
     * @date 2024/5/7 17:19
     **/
    @PostMapping("/getPropagationListData")
    public PageInfo<IndexResultBean> getPropagationListData(@RequestBody StatisticsBean analysisSubject) {
        return areaDistributionService.getPropagationListData(analysisSubject);
    }

    /**
     * 专题检测-跳转至传播路径图信息列表
     *
     * @param analysisSubject
     * @return 分页对象
     * <AUTHOR>
     * @date 2024/5/7 17:19
     **/
    @PostMapping("/searchByKeyWords")
    public PageInfo<IndexResultBean> searchByKeyWords(@RequestBody StatisticsBean analysisSubject) {
        return areaDistributionService.searchByKeyWords(analysisSubject);
    }

    /**
     * 合肥舆情-大屏-区域码统计
     *
     * @param params
     * @return java.util.Map<java.lang.String, java.lang.Long>
     * <AUTHOR>
     * @date 2024/5/7 9:42
     **/
    @PostMapping("/getScreenAreaCount")
    public Map<String, Long> getScreenAreaCount(@RequestBody Map<String, Object> params) {
        List<String> areaCodes = null;
        if (params.get("areaCodes") != null) {
            areaCodes = (List<String>) params.get("areaCodes");
        }
        StatisticsBean statisBean = JSONObject.parseObject(JSONObject.toJSONString(params.get("statisBean")), StatisticsBean.class);
        return areaDistributionService.getScreenAreaCount(statisBean, statisBean.getAggName(), areaCodes);
    }

    /**
     * 合肥舆情-舆情监测预警-专题监测-站点分布
     *
     * @param statisBean
     * @return
     */
    @PostMapping("/getHostData")
    public Map<String, Long> getHostData(@RequestBody StatisticsBean statisBean) {
        return subjectAnalyseService.getHostData(statisBean);
    }

    /**
     * 合肥舆情-舆情监测预警-专题监测--媒体频道
     *
     * @param statisBean
     * @return
     */
    @PostMapping("/getMediaMapType")
    public Map<String, Long> getMediaMapType(@RequestBody StatisticsBean statisBean) {
        return subjectAnalyseService.getMediaMapType(statisBean);
    }

    /**
     * 合肥舆情-舆情监测预警-专题监测--媒体频道-获取媒体饼状图
     *
     * @param statisBean
     * @return
     */
    @PostMapping("/getMediaPie")
    public Map<String, Long> getMediaPie(@RequestBody StatisticsBean statisBean) {
        return subjectAnalyseService.getMediaPie(statisBean);
    }

    /**
     * 对外接口-时间趋势
     *
     * @param bo
     * @return
     */
    @PostMapping("/timeTrend")
    public Map<String, Map<String, Integer>> timeTrend(@RequestBody EsSearchBO bo) {
        return subjectAnalyseService.timeTrend(bo);
    }

    /**
     * 对外接口-时间趋势
     *
     * @param statisBean
     * @return
     */
    @PostMapping("/getTimeTrendMap24H")
    public Map<String, Integer[]> getTimeTrendMap24H(@RequestBody StatisticsBean statisBean) {
        return subjectAnalyseService.getTimeTrendMap24H(statisBean);
    }



    /**
     * 博约舆情-首页-一周走势图
     *
     * @param bo
     * @return
     */
    @PostMapping("/getWeekCount")
    public Integer[] getWeekCount(@RequestBody EsSearchBO bo) {
        return subjectAnalyseService.getWeekCount(bo);
    }

    /**
     * 合肥舆情-舆情监测预警-专题监测-时间趋势
     *
     * @param statisBean
     * @return
     */
    @PostMapping("/getTimeTrendMap")
    public Map<String, Long> getTimeTrendMap(@RequestBody StatisticsBean statisBean) {
        return subjectAnalyseService.getTimeTrendMap(statisBean);
    }

    /**
     * 首页文章-相似度信息
     *
     * @param analysisSubject
     * @return com.boryou.servs.dplatform.pojo.bean.PageInfo<com.boryou.servs.dplatform.module.external.bo.IndexResultBean>
     * <AUTHOR>
     * @date 2024/5/7 17:22
     **/
    @PostMapping("/getSimilarDataFromMd5")
    public PageInfo<IndexResultBean> getSimilarDataFromMd5(@RequestBody StatisticsBean analysisSubject) {
        return areaDistributionService.getPropagationListData(analysisSubject);
    }


    /**
     * 大屏-地图-地域分布-详情页
     *
     * @param analysisSubject
     * @return com.boryou.servs.dplatform.pojo.bean.PageInfo<com.boryou.servs.dplatform.module.external.bo.IndexResultBean>
     * <AUTHOR>
     * @date 2024/5/7 17:22
     **/
    @PostMapping("/getAreaDetailList")
    public PageInfo<IndexResultBean> getAreaDetailList(@RequestBody StatisticsBean analysisSubject) {
        return areaDistributionService.getPropagationListData(analysisSubject);
    }
}

