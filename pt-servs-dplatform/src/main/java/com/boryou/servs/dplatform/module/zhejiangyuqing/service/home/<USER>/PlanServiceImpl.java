package com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.*;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.TotalHits;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.PlanEsBean;
import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.PlanEsSearchBO;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.PlanService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

/**
 * <AUTHOR>
 * @date 2024-06-14 09:27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PlanServiceImpl implements PlanService {
    private final ElasticsearchClient esClient;
    private final ExecutorService executorService = new ThreadPoolExecutor(
            12, // 核心线程数
            200, // 最大线程数
            10L, // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(600) // 任务队列容量
    );

    public void resetEsSearchParams(EsSearchBO bo) {
        bo.setType(null);
//        bo.setExcludeWord(null);
//        bo.setSearchPosition("");
        bo.setIsSpam(null);
        bo.setEmotionFlag(null);
//        bo.setConfigSelect(0);
//        bo.setExcludeId(null);
        bo.setHost(null);
        bo.setExcludeHost(null);
        bo.setQuadraticWord(null);
        bo.setSourceSetting(null);
//        bo.setContentAreaCode(null);   //这个条件会导致数据量变化极大
        bo.setAccountLevel(null);
//        bo.setSourceMap(null);
//        bo.setSourceSetting(null);
        bo.setIsOriginal(null);
        bo.setForward(null);
        bo.setAccountLevel(null);
        bo.setAccountGrade(null);
        bo.setAggName(null);
    }

    @Override
    public boolean saveHistoryPlan(PlanEsSearchBO esSearchBOParams) throws IOException {
        long start1 = System.currentTimeMillis();
        EsSearchBO esSearchBO = esSearchBOParams.getEsSearchBO();
        if (CollUtil.isEmpty(esSearchBO.getIndexs())) {
            throw new RuntimeException("es索引参数异常");
        }
        resetEsSearchParams(esSearchBO);
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        SearchRequest searchRequest = SearchRequest.of(
                s -> s.index(EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime())).query(builder.build()._toQuery()).size(3000).scroll(t -> t.time("60s"))
        );
        SearchResponse<PlanEsBean> response = esClient.search(searchRequest, PlanEsBean.class);
        long end = System.currentTimeMillis();
        double passedTime = (end - start1) / 1000.0;
        if (passedTime > 5) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        }
        TotalHits total = response.hits().total();
        log.info("本次需要存的方案：{}总数{}条:", esSearchBOParams.getPlanId(), Objects.requireNonNull(total).value());
        List<PlanEsBean> dataToMove = response.hits().hits().stream().map(Hit::source).collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        bulkAdd(esSearchBOParams, dataToMove);

        long count = dataToMove.size();
        int page = 1;
        List<CompletableFuture<BulkResponse>> futures = new ArrayList<>();
        String scrollId = response.scrollId();
//        log.info("初始化:{}", scrollId);
        List<Hit<PlanEsBean>> hits;
        do {
            List<PlanEsBean> ScollList = new CopyOnWriteArrayList<>();
            String finalScrollId = scrollId;
            ScrollResponse<PlanEsBean> scrollResp = esClient.scroll(s -> s.scrollId(finalScrollId).scroll(t -> t.time("60s")), PlanEsBean.class);
            scrollId = scrollResp.scrollId();
//            log.info("后续:{}", scrollId);
            hits = scrollResp.hits().hits();
            for (Hit<PlanEsBean> hit : hits) {
                PlanEsBean esFileVO = hit.source();
                if (esFileVO != null) {
                    esFileVO.setPlanId(esSearchBOParams.getPlanId());
                    ScollList.add(esFileVO);
                }
            }
            if (CollUtil.isNotEmpty(ScollList)) {
                long start = System.currentTimeMillis();
                CompletableFuture<BulkResponse> bulkInsertFuture = CompletableFuture.supplyAsync(() -> {
                    // 滑动插入
                    return bulkAdd(esSearchBOParams, ScollList);
                }, executorService);
                futures.add(bulkInsertFuture);
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("{},完成一次插入,耗时:{}秒,page:{}", esSearchBOParams.getPlanId(), (System.currentTimeMillis() - start) / 1000, page);
                count += ScollList.size();
                page += 1;
            }
        } while (!hits.isEmpty());
        // 清理滚动上下文
        String finalScrollIdX = scrollId;
        esClient.clearScroll(s -> s.scrollId(finalScrollIdX));
        log.info("planId:{},插入:{},分页:{}次", esSearchBOParams.getPlanId(), count, page);
        return true;
    }

    @Override
    public List<EsBean> firstRelease(EsSearchBO esSearchBO) {
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);

        List<EsBean> results = new ArrayList<>();
        String aggKey = "typeAgg";
        String aggKey2 = "top_type_hits";
        SearchRequest searchRequest = SearchRequest.of(s -> s
                .index(CollUtil.isEmpty(esSearchBO.getIndexs()) ? EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime()) : esSearchBO.getIndexs())
                .trackTotalHits(t -> t.enabled(true))
                .query(builder.build()._toQuery())
                .aggregations(aggKey, a -> a
                        .terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).size(20))
                        .aggregations(aggKey2, subAgg -> subAgg.topHits(th -> th.sort(sort -> sort.field(f -> f.field("publishTime").order(SortOrder.Asc))).size(1)))
                )
                .size(0));
        try {
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            List<LongTermsBucket> array = ((LongTermsAggregate) response.aggregations().get(aggKey)._get()).buckets().array();
            for (LongTermsBucket bucket : array) {
                Aggregate topTypeHits = bucket.aggregations().get(aggKey2);
                TopHitsAggregate topHits = (TopHitsAggregate) topTypeHits._get();

                // 获取第一个命中的文档
                List<Hit<JsonData>> hits = topHits.hits().hits();
                if (!hits.isEmpty()) {
                    JsonData source = hits.get(0).source();
                    EsBean esBean = source.to(EsBean.class);
                    results.add(esBean);
                }
            }

        } catch (IOException e) {
            log.error(e.getMessage());
        }

        return results;
    }

    private BulkResponse bulkAdd(PlanEsSearchBO esSearchBOParams, List<PlanEsBean> dataToMove) {
        if (CollUtil.isEmpty(dataToMove)) {
            return null;
        }
        BulkRequest bulkRequest = BulkRequest.of(b -> {
//            int i = 0;
            for (PlanEsBean data : dataToMove) {
                data.setPlanId(esSearchBOParams.getPlanId());
                String docId = String.valueOf(data.getId());
//                log.info("--{},{}", data.getId(), data.getTitle());
                b.operations(op -> op.index(idx -> idx.index(esSearchBOParams.getEsSearchBO().getIndexs().get(0)).id(docId).document(data)));
//                i++;
            }
//            log.info("----总数的发" + i);
            return b;
        });
        try {
            BulkResponse bulk = esClient.bulk(bulkRequest);
            if (bulk.errors()) {
                long took = bulk.took();
                log.info("took：{},error:{}", took, bulk.errors());
                List<BulkResponseItem> items = bulk.items();
                for (BulkResponseItem item : items) {
                    ErrorCause error = item.error();
                    if (error != null) {
                        System.out.println(error);
                    }
                }
            }
            return bulk;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public PageResult<EsBean> eventContext(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        SearchRequest searchRequest;


        String unique_md5 = "unique_md5";
        String max_publishTime = "max_publishTime";
        String top_md5_hits = "top_md5_hits";
        NamedValue<SortOrder> namedValue = NamedValue.of(max_publishTime, SortOrder.Asc);
        searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(CollUtil.isEmpty(esSearchBO.getIndexs()) ? EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime()) : esSearchBO.getIndexs())
                .query(builder.build()._toQuery())
                .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                .size(0)
                .aggregations(unique_md5, a -> a.terms(t -> t
                                .field(EsBeanFieldEnum.MD5.getFieldName())
                                .size(esSearchBO.getPageNum() != 0 && esSearchBO.getPageSize() != 0 ? esSearchBO.getPageNum() * esSearchBO.getPageSize() : 1)
                                .order(namedValue))
                        .aggregations(max_publishTime, subAgg -> subAgg.max(th -> th.field("publishTime")))
                        .aggregations(top_md5_hits, top -> top.topHits(th -> th.size(1).sort(sort -> sort.field(f -> f.field("publishTime").order(SortOrder.Asc))))
                        ))
        );
        try {
            SearchResponse<Map> search = esClient.search(searchRequest, Map.class);
            pageResult.setTotal(search.hits().total().value());
            Aggregate uniqueMd5 = search.aggregations().get(unique_md5);
            StringTermsAggregate object = (StringTermsAggregate) uniqueMd5._get();
            List<StringTermsBucket> array = object.buckets().array();
            for (StringTermsBucket item : array) {
                TopHitsAggregate topMd5Hits = (TopHitsAggregate) item.aggregations().get(top_md5_hits)._get();
                for (Hit<JsonData> hit : topMd5Hits.hits().hits()) {
                    if (hit.source() != null) {
                        EsBean esBean = hit.source().to(EsBean.class);
                        // 相似条数
                        long value = topMd5Hits.hits().total().value();
                        esBean.setSimilarCount(Math.toIntExact(value));
                        results.add(esBean);
                    }
                }


            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("异常查询语句:{}", searchRequest);
            log.error("common/info/search 通用查询方法");
        }

        pageResult.setCurrent(esSearchBO.getPageNum());
        pageResult.setSize(esSearchBO.getPageSize());
        pageResult.setRecords(results);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        } else {
            log.info("Normal Request cost {} seconds", passedTime);
        }
        return pageResult;
    }

    @Override
    public List<EsBean> relatedHotArticle(EsSearchBO esSearchBO) {
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        long start = System.currentTimeMillis();
        List<EsBean> results = new ArrayList<>();

        ScriptSort scriptSort = ScriptSort.of(sc -> sc
                .script(Script.of(scriptBuilder -> scriptBuilder
                        .inline(in -> in
                                .source("def commentValue = doc['commentNum'].size()!=0 ? doc['commentNum'].value : 0;          def likeValue = doc['likeNum'].size()!=0 ? doc['likeNum'].value : 0; def reprintValue = doc['reprintNum'].size()!=0? doc['reprintNum'].value : 0; return commentValue + likeValue + reprintValue;")
                                .lang("painless")
                        )))
                .order(SortOrder.Desc)
                .type(ScriptSortType.Number));

        SearchRequest searchRequest = SearchRequest.of(s -> s
                .index(CollUtil.isEmpty(esSearchBO.getIndexs()) ? EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime()) : esSearchBO.getIndexs())
                .trackTotalHits(t -> t.enabled(true))
                .query(builder.build()._toQuery())
                .sort(sort -> sort.script(scriptSort))
        );

        try {
            SearchResponse<Map> search = esClient.search(searchRequest, Map.class);
            for (Hit<Map> hit : search.hits().hits()) {
                results.add(BeanUtil.toBean(hit.source(), EsBean.class));
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("异常查询语句:{}", searchRequest);
            log.error("common/info/search 通用查询方法");
        }


        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        } else {
            log.info("Normal Request cost {} seconds", passedTime);
        }
        return results;
    }
}

