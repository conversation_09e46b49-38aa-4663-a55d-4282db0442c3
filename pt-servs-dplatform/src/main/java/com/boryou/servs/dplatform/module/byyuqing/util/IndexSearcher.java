
package com.boryou.servs.dplatform.module.byyuqing.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 索引检索接口
 *
 * <AUTHOR>
 * @date 2017-7-4 下午3:27:44
 */
public class IndexSearcher {
    // 正则表达式数组，用于匹配范围
    private final String[] regexp = new String[]{"title:(.*?)", "text:(.*?)", "author:(.*?)", "all:(.*?)"};



    //将文字替换为带引号的文字
    private static String changeStr(String keyWords) {
        //在后面随便补一个然后替换完成后再去掉
        keyWords = "(" + keyWords + ")";
        String str = keyWords + "结尾补全";
        String regEx = "[a-zA-Z0-9\\u4e00-\\u9fa5]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        StringBuffer buffer = new StringBuffer();
        System.out.println(m);
        while (m.find()) {
            m.appendReplacement(buffer, "\"" + m.group() + "\"");
        }
        return buffer.toString().substring(0, buffer.toString().length() - 6);
    }
}
