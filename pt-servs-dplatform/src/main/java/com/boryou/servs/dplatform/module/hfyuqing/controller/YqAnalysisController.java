package com.boryou.servs.dplatform.module.hfyuqing.controller;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.hfyuqing.service.YqAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 舆情监测Controller*
 * <AUTHOR>
 * @date 2024/4/25 16:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/hfyq/analyze")
public class YqAnalysisController {

    private final YqAnalysisService esHfyqService;

    /**
     * 载体统计
     * @return
     */
    @PostMapping("/getStatisticData")
    public Return getStatisticData(@RequestBody StatisticsBean statisticsBean) {
        return Return.ok(esHfyqService.getStatisticData(statisticsBean));
    }

}
