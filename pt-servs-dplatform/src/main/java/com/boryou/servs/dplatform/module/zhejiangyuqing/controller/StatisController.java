package com.boryou.servs.dplatform.module.zhejiangyuqing.controller;

import cn.hutool.json.JSONObject;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.StatisService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 统计分析
 *
 * <AUTHOR>
 * @date 2024-06-14 09:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zhejiangyuqing")
public class StatisController {

    private final StatisService statisService;

    /**
     * 媒体级别分布图
     *
     * @param esSearchBO 搜索BO
     * @return java.util.List<com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO>
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/mediaRank")
    public JSONObject mediaRank(@RequestBody EsSearchBO esSearchBO) {
        try {
            return statisService.mediaRank(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}

