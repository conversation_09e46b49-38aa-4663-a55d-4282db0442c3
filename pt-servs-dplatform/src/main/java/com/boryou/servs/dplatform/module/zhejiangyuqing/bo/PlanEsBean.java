package com.boryou.servs.dplatform.module.zhejiangyuqing.bo;

import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description ES对应实体
 * @date 2024/4/22 16:38
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlanEsBean extends EsBean implements Serializable {
        @JsonSerialize(using = ToStringSerializer.class)
        private Long planId;
}
