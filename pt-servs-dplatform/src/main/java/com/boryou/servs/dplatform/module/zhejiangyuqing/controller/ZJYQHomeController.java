package com.boryou.servs.dplatform.module.zhejiangyuqing.controller;

import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.ZJYQHomeService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-24 17:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zhejiangyuqing")
public class ZJYQHomeController {

    private final ZJYQHomeService homeService;

    /**
     * 浙江高院舆情-辖区总览
     * 内部实现我写了两个版本，一个是未带方案的，一个带方案的，目前采用带方案的查询
     *
     * @param esSearchBO 搜索BO
     * @return java.util.List<com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO>
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/areaOverview")
    public List<AreaOverviewBO> searchById(@RequestBody EsSearchBO esSearchBO) {
        try {
            return homeService.areaOverviewOld(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

