package com.boryou.servs.dplatform.enums;

import java.util.Objects;

/**
 * @description 情感枚举类
 * <AUTHOR>
 * @date 2024/4/25 11:19
 */
public enum EmotionEnum {
    //中性
    NEUTRAL("0"),
    //敏感
    NEGATIVE("1"),
    //非敏感
    POSITIVE("2");

    String value;

    EmotionEnum(String value) {
        this.value = value;
    }

    public  String getValue(){
        return value;
    }

    public static EmotionEnum getByValue(String value){
        for(EmotionEnum x:values()){
            if(Objects.equals(x.value, value)){
                return x;
            }
        }
        return NEUTRAL;

    }
}
