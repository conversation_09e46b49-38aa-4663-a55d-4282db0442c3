package com.boryou.servs.dplatform.module.byyuqing.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.byyuqing.service.EventSurveyService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-05-08 13:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yq/event")
public class EventSurveyController {

    private final EventSurveyService eventSurveyService;
    /**
     * 地域分布-es版本
     *
     * @param statisticsBean
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/26 14:56
     **/
    @PostMapping("/getSiteMetaInfo")
    public PageResult<EsBean> getSiteMetaInfo(@RequestBody EsSearchBO statisticsBean) {
        return eventSurveyService.getSiteMetaInfo(statisticsBean);
    }
}

