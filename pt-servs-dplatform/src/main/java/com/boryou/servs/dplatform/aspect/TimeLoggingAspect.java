package com.boryou.servs.dplatform.aspect;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Aspect
@Component
@Slf4j
public class TimeLoggingAspect {

    @Around("execution(* com.boryou.servs.dplatform.*.controller.*.*(..))")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        String methodName = joinPoint.getSignature().getName();
        if (methodName.equals("search") || methodName.equals("searchx") || methodName.equals("homeData")) {
            return result;
        }
        if (executionTime > 8000) {
            String args = Arrays.toString(joinPoint.getArgs());
            log.warn("方法名称:" + methodName + "\n 请求参数: " + (CharSequenceUtil.isEmpty(args)?"无":args) + " \n请求耗时: " + executionTime/1000.0 + " 秒");
        }
        return result;
    }
}
