package com.boryou.servs.dplatform.module.warn.dao;

import java.util.Date;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONObject;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.ErrorCause;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.elasticsearch.core.search.TotalHits;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.module.warn.domain.WarnDataRes;
import com.boryou.servs.dplatform.module.warn.domain.WarnEs;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
@RequiredArgsConstructor
public class WarnDataDao {

    private final ElasticsearchClient esClient;

    public PageResult<WarnDataRes> warnGet(BoolQuery.Builder builder, Integer pageNum, Integer pageSize) {
        TimeInterval timeInterval = new TimeInterval();
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0) {
            pageSize = 10;
        }
        Integer from = (pageNum - 1) * pageSize;
        Integer size = pageSize;
        PageResult<WarnDataRes> pageResult = new PageResult<>();
        SortOptions sortOptions = SortOptionsBuilders.field(f -> f.field("warnTime").order(SortOrder.Desc));
        // 执行查询
        List<WarnDataRes> results = new ArrayList<>();

        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(ESConstant.ES_INDEX_WARN)
                .query(builder.build()._toQuery())
                .from(from)
                .size(size)
                .sort(sortOptions)
        );
        log.warn("/api/warn/get 预警查询参数: {}", searchRequest);

        SearchResponse<WarnDataRes> response = null;
        try {
            response = esClient.search(searchRequest, WarnDataRes.class);
            log.warn("/api/warn/get 预警查询耗时: {} 毫秒", timeInterval.intervalMs());
            HitsMetadata<WarnDataRes> hits = response.hits();
            TotalHits total = hits.total();
            if (total == null) {
                return pageResult;
            }
            pageResult.setTotal(total.value());
            List<Hit<WarnDataRes>> hitsList = hits.hits();
            for (Hit<WarnDataRes> hit : hitsList) {
                results.add(hit.source());
            }
            pageResult.setRecords(results);
        } catch (IOException e) {
            log.error("/api/warn/get 预警查询失败: {}", e.getMessage());
            return pageResult;
        }
        return pageResult;
    }

    public boolean warnAdd(List<WarnDataRes> warnDataResList) {
        String date = DateUtil.now();
        BulkRequest bulkRequest = BulkRequest.of(b -> {
            for (WarnDataRes data : warnDataResList) {
                //WarnEs warnEs = buildWarnEs(data);
                JSONObject jsonObject = buildWarnEs(data);
                jsonObject.set("updateTime", date);
                //warnEs.setUpdateTime(date);
                //String docId = warnEs.getId();
                String docId = data.getId();
                b.operations(op -> op.index(idx -> idx.index(ESConstant.ES_INDEX_WARN).id(docId).document(jsonObject)));
            }
            return b;
        });
        try {
            BulkResponse bulk = esClient.bulk(bulkRequest);
            if (bulk.errors()) {
                long took = bulk.took();
                List<BulkResponseItem> items = bulk.items();
                for (BulkResponseItem item : items) {
                    ErrorCause error = item.error();
                    if (error != null) {
                        log.info("/api/warn/add 预警新增失败, took：{},error:{}", took, error);
                    }
                }
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("/api/warn/add 预警新增失败: {}", e.getMessage());
            return false;
        }
    }

    private JSONObject buildWarnEs(WarnDataRes data) {
        String planId = data.getPlanId();
        String userId = data.getUserId();
        String deptId = data.getDeptId();
        String articleId = data.getArticleId();
        List<String> hitWord = data.getHitWord();
        String warnTime = data.getWarnTime();
        Integer warnType = data.getWarnType();
        String id = data.getId();
        Integer type = data.getType();
        Date publishTime = data.getPublishTime();
        String title = data.getTitle();
        String text = data.getText();
        String url = data.getUrl();
        String host = data.getHost();
        String author = data.getAuthor();
        String authorId = data.getAuthorId();
        String siteAreaCode = data.getSiteAreaCode();
        List<String> contentAreaCode = data.getContentAreaCode();
        List<String> siteMeta = data.getSiteMeta();
        List<String> contentMeta = data.getContentMeta();
        String sector = data.getSector();
        List<Integer> contentForm = data.getContentForm();
        String md5 = data.getMd5();
        List<String> picUrl = data.getPicUrl();
        Integer emotionFlag = data.getEmotionFlag();
        Boolean isOriginal = data.getIsOriginal();
        Boolean isSpam = data.getIsSpam();
        String day = data.getDay();
        Integer accountGrade = data.getAccountGrade();
        Integer warnGrade = data.getWarnGrade();
        List<String> users = data.getUsers();
        List<Integer> pushType = data.getPushType();
        //WarnEs warnEs = new WarnEs();
        //warnEs.setPlanId(planId);
        //warnEs.setUserId(userId);
        //warnEs.setDeptId(deptId);
        //warnEs.setArticleId(articleId);
        //warnEs.setHitWord(hitWord);
        //warnEs.setWarnTime(warnTime);
        //warnEs.setWarnType(warnType);
        //warnEs.setId(id);
        //warnEs.setType(type);
        //warnEs.setPublishTime(publishTime);
        //warnEs.setTitle(title);
        //warnEs.setText(text);
        //warnEs.setUrl(url);
        //warnEs.setHost(host);
        //warnEs.setAuthor(author);
        //warnEs.setAuthorId(authorId);
        //warnEs.setSiteAreaCode(siteAreaCode);
        //warnEs.setContentAreaCode(contentAreaCode);
        //warnEs.setSiteMeta(siteMeta);
        //warnEs.setContentMeta(contentMeta);
        //warnEs.setSector(sector);
        //warnEs.setContentForm(contentForm);
        //warnEs.setMd5(md5);
        //warnEs.setPicUrl(picUrl);
        //warnEs.setEmotionFlag(emotionFlag);
        //warnEs.setIsOriginal(isOriginal);
        //warnEs.setIsSpam(isSpam);
        //warnEs.setDay(day);
        //warnEs.setAccountGrade(accountGrade);
        //warnEs.setWarnGrade(warnGrade);
        JSONObject jsonObject = new JSONObject();
        jsonObject.putOpt("planId", planId);
        jsonObject.putOpt("userId", userId);
        jsonObject.putOpt("deptId", deptId);
        jsonObject.putOpt("articleId", articleId);
        jsonObject.putOpt("hitWord", hitWord);
        jsonObject.putOpt("warnTime", warnTime);
        jsonObject.putOpt("warnType", warnType);
        jsonObject.putOpt("id", id);
        jsonObject.putOpt("type", type);
        jsonObject.putOpt("publishTime", publishTime);
        jsonObject.putOpt("title", title);
        jsonObject.putOpt("text", text);
        jsonObject.putOpt("url", url);
        jsonObject.putOpt("host", host);
        jsonObject.putOpt("author", author);
        jsonObject.putOpt("authorId", authorId);
        jsonObject.putOpt("siteAreaCode", siteAreaCode);
        jsonObject.putOpt("contentAreaCode", contentAreaCode);
        jsonObject.putOpt("siteMeta", siteMeta);
        jsonObject.putOpt("contentMeta", contentMeta);
        jsonObject.putOpt("sector", sector);
        jsonObject.putOpt("contentForm", contentForm);
        jsonObject.putOpt("md5", md5);
        jsonObject.putOpt("picUrl", picUrl);
        jsonObject.putOpt("emotionFlag", emotionFlag);
        jsonObject.putOpt("isOriginal", isOriginal);
        jsonObject.putOpt("isSpam", isSpam);
        jsonObject.putOpt("day", day);
        jsonObject.putOpt("accountGrade", accountGrade);
        jsonObject.putOpt("warnGrade", warnGrade);
        jsonObject.putOpt("users", users);
        jsonObject.putOpt("pushType", pushType);
        return jsonObject;
    }

    public PageResult<EsBean> warnPush(SearchRequest searchRequest) {
        log.error("/api/warn/push 预警推送查询参数: {}", searchRequest);
        PageResult<EsBean> pageResult = new PageResult<>();
        List<EsBean> results = new ArrayList<>();

        try {
            SearchResponse<EsBean> firstResp = esClient.search(searchRequest, EsBean.class);
            String scrollId = firstResp.scrollId();
            for (Hit<EsBean> hit : firstResp.hits().hits()) {
                EsBean esFileVO = hit.source();
                results.add(esFileVO);
            }

            ScrollResponse<EsBean> scrollResp = null;
            do {
                if (scrollResp != null) {
                    for (Hit<EsBean> hit : scrollResp.hits().hits()) {
                        EsBean esFileVO = hit.source();
                        results.add(esFileVO);
                    }
                }
                String finalScrollId = scrollId;
                scrollResp = esClient.scroll(s -> s.scrollId(finalScrollId).scroll(t -> t.time("60s")), EsBean.class);
                scrollId = scrollResp.scrollId();

                if (results.size() > 50000) {
                    break;
                }

            } while (!scrollResp.hits().hits().isEmpty());

            // 清理滚动上下文
            String finalScrollIdX = scrollId;
            esClient.clearScroll(s -> s.scrollId(finalScrollIdX));
        } catch (IOException e) {
            log.error("/api/warn/push 预警推送查询失败: {}", e.getMessage());
        }
        pageResult.setRecords(results);
        pageResult.setTotal(results.size());
        return pageResult;
    }

    public List<String> warnCheck(BoolQuery.Builder builder, int pageSize) {
        TimeInterval timeInterval = new TimeInterval();
        Integer size = 10000;
        // 执行查询
        List<String> results = new ArrayList<>();

        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(ESConstant.ES_INDEX_WARN)
                .query(builder.build()._toQuery())
                .size(size)
        );
        log.warn("/api/warn/check 预警推送查询重复参数: {}", searchRequest);

        SearchResponse<WarnDataRes> response = null;
        try {
            response = esClient.search(searchRequest, WarnDataRes.class);
            log.warn("/api/warn/get 预警推送查询重复参数耗时: {} 毫秒", timeInterval.intervalMs());
            HitsMetadata<WarnDataRes> hits = response.hits();
            List<Hit<WarnDataRes>> hitsList = hits.hits();
            for (Hit<WarnDataRes> hit : hitsList) {
                WarnDataRes source = hit.source();
                if (source != null) {
                    results.add(source.getArticleId());
                }
            }
        } catch (IOException e) {
            log.error("/api/warn/get 预警推送查询重复参数失败: {}", e.getMessage());
            return results;
        }
        return results;
    }
}
