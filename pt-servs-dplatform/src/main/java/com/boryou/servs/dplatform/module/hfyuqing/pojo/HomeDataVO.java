package com.boryou.servs.dplatform.module.hfyuqing.pojo;

import com.boryou.servs.dplatform.pojo.bean.SpecifySite;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.Data;

import java.util.List;

@Data
public class HomeDataVO {

    private long id;
    private long userId;//用户id
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    private int sortType;//排序方式
    private String sortOrder;//排序方式
    private String infoTypes;//存放选中的信息类别值，多个以逗号分隔
    private String emotionTypes;//存放选中的倾向性类别值，多个以逗号分隔
    private Boolean onHighlight;//是否高亮
    private Boolean removeRepeated;//是否去重
    private Boolean removeTrash;//是否去垃圾
    private String keywordPosition;//监控词位置值，多个以逗号分隔
    private String areaWords;//地域监控词，多个以空格分隔
    private String personWords;//人物关键词，多个以空格分隔
    private String eventWords;//事件关键词，多个以空格分隔
    private String excludeWords;//排除关键词，多个以空格分隔
    private String siteIds;//指定的站点的id，多个以逗号分隔
    private String siteMeta;//指定站点标签，多个以逗号分隔
    private String contentMeta;//指定信息标签，多个以逗号分隔
    private String excludeIds;//排除id
    /**
     * 排除站点
     */
    private String excludeHost;
    private Boolean inherit = false;//是否是继承来的属性，协助前台显示设定按钮
    private String returnFields;//返回的域，多个以逗号分隔
    private String allKeywords;//标红
    private int frameSize;

    private int from;
    private int size;

    /**
     * 二次搜索词
     */
    private String quadraticWord;
    /**
     * 二次搜索过滤词
     */
    private String quadraticFilterWord;
    /**
     * 内容地域 contentAreaCode
     */
    private String contentAreaCode;
    private Boolean accurate = true;//是否精确查询
    private Boolean typePosition = false  ;//是否单独判断type
    /**
     * 时间类型（0发文时间，1采集时间）
     */

    private int timeType;
    /**
     * 作者
     */
    private String author;
    private String title;
    /**
     * 信息链接
     */
    private String url;
    /**
     * 是否使用 ik_smart
     */
    private Boolean useIkSmart;

    private EsSearchBO esSearchBO;
    /**
     * 项目接口调用标识
     */
    private Integer projectType;

    /**
     * 定制站点
     */
    private List<SpecifySite> specifySites;

}
