package com.boryou.servs.dplatform.pojo.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description 更新ES方法
 * <AUTHOR>
 * @date 2024/11/29 10:17
 */
@Data
public class EsUpdateBO {
    @NotBlank(message = "信息id不能为空")
    private String id;
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;
    @NotBlank(message = "字段值不能为空")
    private String fieldValue;
    @NotBlank(message = "开始时间不能为空")
    private String startTime;
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
}
