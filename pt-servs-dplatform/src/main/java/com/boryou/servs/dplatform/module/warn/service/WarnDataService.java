package com.boryou.servs.dplatform.module.warn.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.module.warn.dao.WarnDataDao;
import com.boryou.servs.dplatform.module.warn.domain.WarnDataRes;
import com.boryou.servs.dplatform.module.warn.domain.vo.WarnDataVO;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class WarnDataService {

    private final WarnDataDao warnDataDao;

    public PageResult<WarnDataRes> warnGet(WarnDataVO warnDataVO) {
        Integer pageNum = warnDataVO.getPageNum();
        Integer pageSize = warnDataVO.getPageSize();

        Integer readFlag = warnDataVO.getReadFlag();
        List<String> readIds = warnDataVO.getReadIds();
        if (readFlag != null && readFlag == 1 && CollUtil.isEmpty(readIds)) {
            return new PageResult<>();
        }
        BoolQuery.Builder bool = QueryBuilders.bool();
        buildQuery(warnDataVO, bool);

        return warnDataDao.warnGet(bool, pageNum, pageSize);
    }

    private void buildQuery(WarnDataVO warnDataVO, BoolQuery.Builder bool) {
        List<String> ids = warnDataVO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            //勾选id为最高优先级
            termQuery(ids, bool, "id");
            return;
        }
        String warnDateStart = warnDataVO.getWarnDateStart();
        String warnDateEnd = warnDataVO.getWarnDateEnd();
        String emotionFlag = warnDataVO.getEmotionFlag();
        String planId = warnDataVO.getPlanId();
        String type = warnDataVO.getType();
        Integer readFlag = warnDataVO.getReadFlag();
        List<String> readIds = warnDataVO.getReadIds();
        List<String> deptIds = warnDataVO.getDeptIds();
        String warnType = warnDataVO.getWarnType();
        String kw = warnDataVO.getKw();
        List<String> users = warnDataVO.getUsers();
        List<String> pushType = warnDataVO.getPushType();

        Query range = QueryBuilders.range(r -> r.field("warnTime").from(warnDateStart).to(warnDateEnd));
        bool.filter(range);

        termQuery(emotionFlag, bool, EsBeanFieldEnum.EMOTION_FLAG.getFieldName());
        termQuery(type, bool, EsBeanFieldEnum.TYPE.getFieldName());
        termQuery(warnType, bool, "warnType");
        termQuery(planId, bool, "planId");
        termQuery(deptIds, bool, "deptId");
        termQuery(users, bool, "users");
        termQuery(pushType, bool, "pushType");

        if (readFlag != null) {
            if (readFlag == 1) {
                termQuery(readIds, bool, "id");
            } else if (readFlag == 0) {
                termMustNotQuery(readIds, bool, "id");
            }
        }

        if (CharSequenceUtil.isNotBlank(kw)) {
            QueryStringQuery.Builder kwQb = QueryBuilders.queryString();
            kwQb.query("\"" + kw + "\"");
            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName());
            bool.filter(kwQb.build()._toQuery());
        }
    }

    private void termQuery(String value, BoolQuery.Builder bool, String filed) {
        if (CharSequenceUtil.isBlank(value)) {
            return;
        }

        List<FieldValue> fieldValues = CollStreamUtil.toList(CharSequenceUtil.splitTrim(value, ','), FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }

    }

    private void termQuery(List<String> value, BoolQuery.Builder bool, String filed) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        List<FieldValue> fieldValues = CollStreamUtil.toList(value, FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }

    }

    private void termMustNotQuery(List<String> value, BoolQuery.Builder bool, String filed) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        List<FieldValue> fieldValues = CollStreamUtil.toList(value, FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.mustNot(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }

    }

    public boolean warnAdd(List<WarnDataRes> warnDataResList) {
        if (CollUtil.isEmpty(warnDataResList)) {
            return true;
        }
        return warnDataDao.warnAdd(warnDataResList);
    }

    public PageResult<EsBean> warnPush(EsSearchBO esSearchBO) {
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        SortOptions sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).order(SortOrder.Desc));
        String startTime = esSearchBO.getStartTime();
        String endTime = esSearchBO.getEndTime();
        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(EsUtil.getIndexes(esSearchBO))
                .query(builder.build()._toQuery())
                .sort(sortOptions)
                .size(5000)
                .scroll(t -> t.time("60s")
                ));

        return warnDataDao.warnPush(searchRequest);
    }

    public List<String> warnCheck(WarnDataVO warnDataVO) {
        String planId = warnDataVO.getPlanId();
        List<String> articleIds = warnDataVO.getArticleIds();
        String userId = warnDataVO.getUserId();
        List<String> users = warnDataVO.getUsers();
        if (CollUtil.isEmpty(articleIds)) {
            return Collections.emptyList();
        }
        //if (planId == null && StrUtil.isBlankIfStr(userId)) {
        //    return Collections.emptyList();
        //}
        BoolQuery.Builder bool = QueryBuilders.bool();
        termQuery(planId, bool, "planId");
        termQuery(articleIds, bool, "articleId");
        termQuery(userId, bool, "userId");
        termQuery(users, bool, "users");
        return warnDataDao.warnCheck(bool, articleIds.size());
    }

}
