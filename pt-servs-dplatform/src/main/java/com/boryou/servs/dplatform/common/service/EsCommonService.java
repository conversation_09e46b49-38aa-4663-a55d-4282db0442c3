package com.boryou.servs.dplatform.common.service;


import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsUpdateBO;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @description Es公用Service接口
 * <AUTHOR>
 * @date 2024/4/23 10:26
 */

public interface EsCommonService {

    List<EsBean> searchDemo(String author);

    EsBean searchById(String id);

    PageResult<EsBean> search(EsSearchBO esSearchBO);

    Integer realTimeInfoCount(EsSearchBO esSearchBO);

    /**
     * 根据id及关键词高亮查询*
     * @param id id
     * @param keywords 关键词
     * @return 返回值
     */
    EsBean searchAndHighlightById(String id, String keywords);
    EsBean searchAndHighlightByIdX(String id, String keywords,Boolean accurate,String startTime,String endTime);

    JSONObject searchAndHighlightByUrl(String url);

    /**
     * 查询数据条数
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    Long getInfoCount(EsSearchBO esSearchBO);
    Integer getSimilarCount(EsSearchBO esSearchBO);

    Map<String,Map<String,Long>> getTypeCount(EsSearchBO esSearchBO) throws IOException;

    Map<String, Long> getTotalCount(EsSearchBO esSearchBO) throws IOException, Exception;

    Map<String, Map<String, Long>> getDouyinTotal(EsSearchBO esSearchBO) throws IOException;

    Map<String, Long> getTypeTotalCount(EsSearchBO esSearchBO)throws IOException;

    Long searchLatestThreeHoursInfoCount();

    EsBean searchByIdTime(EsSearchBO esSearchBO);

    PageResult<EsBean> searchx(EsSearchBO esSearchBO);

    Map<String, Long> mediaTypeCountForOriginal(EsSearchBO esSearchBO);

    PageResult<EsBean> queryScrollAllData(EsSearchBO esSearchBO) throws IOException;

    EsBean searchByUrl(EsSearchBO bo);

    boolean updateById(EsUpdateBO esUpdateBO);
}
