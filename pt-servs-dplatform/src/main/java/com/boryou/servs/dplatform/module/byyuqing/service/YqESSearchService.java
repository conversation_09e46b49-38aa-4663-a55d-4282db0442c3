package com.boryou.servs.dplatform.module.byyuqing.service;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface YqESSearchService {

    /**
     * 当天微博作者排行
     * <AUTHOR>
     * @date 2024/4/25
     */
    Map<String, Integer> getWeiboAuthorSortByTime(EsSearchBO esSearchBO);

    /**
     * 获取微博相似数据查询
     * <AUTHOR>
     * @date 2024/4/25
     */
    PageResult<EsBean> getWeiboSimilarData(EsSearchBO esSearchBO);

    /**
     * 事件脉络列表
     * <AUTHOR>
     * @date 2024/4/25
     */
    PageResult<EsBean> searchByKeyWordsByPublishTimeAscSort(EsSearchBO esSearchBO);

    /**
     * 事件脉络列表
     * <AUTHOR>
     * @date 2024/4/25
     */
    Map<String, Long> getAreaData(EsSearchBO esSearchBO);

    /**
    * 提取事件关键词
    * <AUTHOR>
    * @date 2024/4/29
    */
    List<EsBean> searchByKeyWordAndUrl(EsSearchBO esSearchBO);

    PageResult<EsBean> getSimilarDataById(EsSearchBO esSearchBO);

    PageResult<EsBean> getSimilarDataByMd5(EsSearchBO esSearchBO);

    /**
     * @description 获取国家政策
     * <AUTHOR>
     * @date 2024/6/19 10:30
     */
    PageResult<EsBean> policyList(EsSearchBO esSearchBO);
}
