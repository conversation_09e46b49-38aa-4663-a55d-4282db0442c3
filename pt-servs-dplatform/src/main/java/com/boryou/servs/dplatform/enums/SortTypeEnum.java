package com.boryou.servs.dplatform.enums;

/**
 * @description 排序方式枚举类
 * <AUTHOR>
 * @date 2024/5/8 15:14
 */
public enum SortTypeEnum {
    //相关度降序
    RELATION_DESC("1"),
    //热度降序   阅读数降序
    READ_NUM("2"),
    //时间降序
    TIME_DESC("3"),
    //时间升序
    TIME_ASC("4"),
    //热度升序
    HOT_ASC("5"),
    //相关度升序
    RELATION_ASC("6"),
    //相似文章数量降序
    SIMILAR_DESC("7"),
    //采集时间，入库时间降序
    UPDATE_TIME("8"),
    //互动数降序
    INTERACT_NUM("10"),
    //转发数 降序
    REPRINT_NUM("11"),
    //评论数 降序
    COMMENT_NUM("12"),
    //点赞数 降序
    LIKE_NUM("13"),
    //页面浏览量
    PAGE_VIEW("14"),
    //综合
    COMPREHENSIVE("0");
    String sortValue;

    SortTypeEnum(String s) {
        this.sortValue = s;
    }

    public String getVal(){
        return sortValue;
    }

    /**
     * 根据值获取枚举
     * @param value
     * @return
     */
    public static SortTypeEnum getByValue(String value){
        SortTypeEnum[] values = values();
        for (SortTypeEnum sortTypeEnum : values) {
            if (sortTypeEnum.sortValue.equals(value)) {
                return sortTypeEnum;
            }
        }
        return COMPREHENSIVE;
    }

}
