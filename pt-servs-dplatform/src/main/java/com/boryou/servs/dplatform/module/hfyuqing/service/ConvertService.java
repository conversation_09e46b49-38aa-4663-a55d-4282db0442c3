package com.boryou.servs.dplatform.module.hfyuqing.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.boryou.servs.dplatform.enums.SearchPositionEnum;
import com.boryou.servs.dplatform.enums.TimeTypeEnum;
import com.boryou.servs.dplatform.module.hfyuqing.enums.FieldEnum;
import com.boryou.servs.dplatform.module.hfyuqing.enums.PositionEnum;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.HomeDataVO;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConvertService {

    public List<String> convertSortType(int type) {
        List<String> sortField = new ArrayList<>();

        switch (type) {
            case 0:
                sortField.add(FieldEnum.TIME.getName());
                sortField.add(FieldEnum.REPRINT_NUM.getName());
                break;
            case 1:
                sortField.add(FieldEnum.SCORE.getName());
                break;
            case 2:
                sortField.add(FieldEnum.REPRINT_NUM.getName());
                break;
            case 3:
                sortField.add(FieldEnum.TIME.getName());
                break;
            default:
                break;
        }

        return sortField;
    }

    public HomeDataVO homeDataVO(HomeDataVO homeDataVO) {
        String startTime = homeDataVO.getStartTime();
        String endTime = homeDataVO.getEndTime();
        String emotionTypes = homeDataVO.getEmotionTypes();
        Boolean isRemoveRepeated = homeDataVO.getRemoveRepeated();
        Boolean removeTrash = homeDataVO.getRemoveTrash();
        String keywordPosition = homeDataVO.getKeywordPosition();
        String areaWords = homeDataVO.getAreaWords();
        String personWords = homeDataVO.getPersonWords();
        String eventWords = homeDataVO.getEventWords();
        String excludeWords = homeDataVO.getExcludeWords();
        String siteIds = homeDataVO.getSiteIds();
        String type = homeDataVO.getInfoTypes();
        String quadraticWord = homeDataVO.getQuadraticWord();
        String quadraticFilterWord = homeDataVO.getQuadraticFilterWord();
        String contentAreaCode = homeDataVO.getContentAreaCode();
        Boolean accurate = homeDataVO.getAccurate();
        String excludeHost = homeDataVO.getExcludeHost();
        int timeType = homeDataVO.getTimeType();
        String author = homeDataVO.getAuthor();
        String url = homeDataVO.getUrl();

        EsSearchBO esSearchBO = new EsSearchBO();
        esSearchBO.setExcludeId(homeDataVO.getExcludeIds());
        esSearchBO.setType(type);
        esSearchBO.setEmotionFlag(emotionTypes);
        if (timeType == TimeTypeEnum.CAPTURE.getVal()) {
            esSearchBO.setTimeType(TimeTypeEnum.CAPTURE.getVal());
        } else {
            esSearchBO.setTimeType(TimeTypeEnum.PUBLISH.getVal());
        }
        esSearchBO.setSiteMeta(homeDataVO.getSiteMeta());
        esSearchBO.setContentMeta(homeDataVO.getContentMeta());
        esSearchBO.setStartTime(startTime);
        esSearchBO.setEndTime(endTime);
        //todo
        //esSearchBO.setStartTime("2024-04-20 10:14:21");
        //esSearchBO.setEndTime("2024-04-21 10:14:21");
        esSearchBO.setIsOriginal(isRemoveRepeated);
        esSearchBO.setIsSpam(removeTrash);
        if (!StrUtil.isBlankIfStr(personWords)) {
            esSearchBO.setKeyWord1(personWords.replace(",", " "));
        }
        if (!StrUtil.isBlankIfStr(eventWords)) {
            esSearchBO.setKeyWord2(eventWords.replace(",", " "));

        }
        if (!StrUtil.isBlankIfStr(areaWords)) {
            esSearchBO.setKeyWord3(areaWords.replace(",", " "));
        }
        if (!StrUtil.isBlankIfStr(excludeWords)) {
            esSearchBO.setExcludeWord(excludeWords.replace(",", " "));
        }
        esSearchBO.setHost(siteIds);
        esSearchBO.setExcludeHost(excludeHost);
        esSearchBO.setSearchPosition(this.homePositionConvert(keywordPosition));
        esSearchBO.setQuadraticWord(quadraticWord);
        esSearchBO.setQuadraticFilterWord(quadraticFilterWord);
        esSearchBO.setContentAreaCode(contentAreaCode);
        esSearchBO.setAccurate(accurate);
        esSearchBO.setAuthor(author);
        esSearchBO.setUrl(url);
        esSearchBO.setProjectType(homeDataVO.getProjectType());
        homeDataVO.setEsSearchBO(esSearchBO);
        return homeDataVO;

    }

    public String homePositionConvert(String keywordPosition) {
        if (StrUtil.isBlankIfStr(keywordPosition) || keywordPosition.contains(PositionEnum.ALL.getCode())) {
            return SearchPositionEnum.ALL.name();
        }
        List<String> positionList = CharSequenceUtil.splitTrim(keywordPosition, ",");
        List<String> posNumList = new ArrayList<>();
        for (String position : positionList) {
            PositionEnum positionEnum = PositionEnum.getEnumByCode(position);
            if (positionEnum != null) {
                String info = positionEnum.getInfo();
                posNumList.add(info);
            }
        }
        return CharSequenceUtil.join(",", posNumList);
    }

}
