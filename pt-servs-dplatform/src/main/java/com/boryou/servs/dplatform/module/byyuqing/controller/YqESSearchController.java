package com.boryou.servs.dplatform.module.byyuqing.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.byyuqing.service.YqESSearchService;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/yq/info")
public class YqESSearchController {

    private final YqESSearchService yqESSearchService;

    /**
     * 舆情首页每日00:00:00到23:59:59微博作者排行
     * <AUTHOR>
     * @date 2024/4/25
     */
    @PostMapping("/getWeiboAuthorSortByTime")
    public Map<String, Integer> getWeiboAuthorSortByTime(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.getWeiboAuthorSortByTime(esSearchBO);
    }


    /**
     * @description 定制接口 首页信用中心国家政策
     * <AUTHOR>
     * @date 2024/6/19 10:29
     */

    @PostMapping("/policyList")
    public PageResult<EsBean> policyList(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.policyList(esSearchBO);
    }

    /**
     * 获取微博相似数据查询
     * <AUTHOR>
     * @date 2024/4/25
     */
    @PostMapping("/getWeiboSimilarData")
    public PageResult<EsBean> getWeiboSimilarData(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.getWeiboSimilarData(esSearchBO);
    }

    /**
     * 事件脉络列表
     */
    @PostMapping("/searchByKeyWordsByPublishTimeAscSort")
    public PageResult<EsBean> searchByKeyWordsByPublishTimeAscSort(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.searchByKeyWordsByPublishTimeAscSort(esSearchBO);
    }

    /**
     * 获取地域统计图信息
     */
    @PostMapping("/getAreaData")
    public Map<String, Long> getAreaData(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.getAreaData(esSearchBO);
    }

    /**
     * 提取事件关键词
     */
    @PostMapping("/searchByKeyWordAndUrl")
    public List<EsBean> searchByKeyWordAndUrl(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.searchByKeyWordAndUrl(esSearchBO);
    }

    @PostMapping("/getSimilarDataById")
    public PageResult<EsBean> getSimilarDataById(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.getSimilarDataById(esSearchBO);
    }

    @PostMapping("/getSimilarDataByMd5")
    public PageResult<EsBean> getSimilarDataByMd5(@RequestBody EsSearchBO esSearchBO) {
        return yqESSearchService.getSimilarDataByMd5(esSearchBO);
    }
}
