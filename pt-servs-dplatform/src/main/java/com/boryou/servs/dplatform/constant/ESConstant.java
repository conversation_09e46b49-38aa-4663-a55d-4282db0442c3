package com.boryou.servs.dplatform.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
public class ESConstant {

    @Value("${app.env}")
    private String profiles;

    public static String ES_INDEX;
    public static String ES_INDEX_WEEK;
    public static String ES_INDEX_MONTH;
    public static String ES_INDEX_WARN;
    public static String ES_INDEX_INFOLIB;
    /**
     * 三大节点逗号分隔字符串
     */
    public static String JOIN_NETXMAN_YQ_ALL;
    /**
     * 三大节点集合
     */
    public static List<String> ALL_INDEX=new ArrayList<>();

    @PostConstruct
    public void ESConstant() {
        if (profiles.equals("quantum") ||profiles.equals("qinghaicdc")||profiles.equals("jysz")||profiles.equals("sxyjt")) {
            ES_INDEX = "boryou-yq";
            ES_INDEX_WEEK = "boryou-yq-week";
            ES_INDEX_MONTH = "boryou-yq-month";
            ES_INDEX_WARN = "boryou-yq-warn";
            ES_INDEX_INFOLIB = "boryou-yq-infolib";
        } else {
            ES_INDEX = "netxman_yq";
            ES_INDEX_WEEK = "netxman_yq_week";
            ES_INDEX_MONTH = "netxman_yq_month";
            ES_INDEX_WARN = "netxman_yq_warn_boryou";
            ES_INDEX_INFOLIB = "netxman_yq_infolib";
        }
        JOIN_NETXMAN_YQ_ALL= ES_INDEX + "," + ES_INDEX_WEEK + "," + ES_INDEX_MONTH;
        ALL_INDEX.add(ES_INDEX);
        ALL_INDEX.add(ES_INDEX_WEEK);
        ALL_INDEX.add(ES_INDEX_MONTH);
    }
}
