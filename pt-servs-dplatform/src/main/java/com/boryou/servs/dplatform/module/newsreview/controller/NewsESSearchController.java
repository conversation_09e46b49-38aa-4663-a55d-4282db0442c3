package com.boryou.servs.dplatform.module.newsreview.controller;


import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.newsreview.service.NewsESSearchService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.sb.EsNewsSearchBean;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 新闻阅评信息es检索Controller
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/news/info")
public class NewsESSearchController {

    private final NewsESSearchService newsESSearchService;

    @PostMapping("/searchByUrl")
    public EsBean searchById(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return newsESSearchService.searchByUrl(esNewsSearchBean.getSector(), esNewsSearchBean.getStartTime());
    }

    /**
     * 新闻阅评广播电视媒体每日发文数量统计
     * <AUTHOR>
     */
    @PostMapping("/searchByTimeFacet")
    public Map<String, Integer> searchByTimeFacet(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return newsESSearchService.searchByTimeFacet(esNewsSearchBean.getHost(), esNewsSearchBean.getProgram(), esNewsSearchBean.getStartTime(), esNewsSearchBean.getEndTime());
    }

    /**
     * 新闻阅评电子报列表页
     * <AUTHOR>
     */
    @PostMapping("/getEpaperBySector")
    public PageResult<EsBean> getEpaperBySector(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return newsESSearchService.getEpaperBySector(esNewsSearchBean.getStartTime(), esNewsSearchBean.getSector());
    }

    /**
     * 新闻阅评根据信息来源页面查询某天的报纸信息的数量
     * <AUTHOR>
     * @date 2024/4/19
     */
    @PostMapping("/getEpaperBySectorSize")
    public Integer getEpaperBySectorSize(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return Math.toIntExact(newsESSearchService.getEpaperBySector(esNewsSearchBean.getStartTime(), esNewsSearchBean.getSector()).getSize());
    }

    /**
     * 新闻阅评获取广播电视节目内容
     * <AUTHOR>
     * @date 2024/4/19
     */
    @PostMapping("/getFullVersionProgramSegments")
    public PageResult<EsBean> getFullVersionProgramSegments(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return newsESSearchService.getFullVersionProgramSegments(esNewsSearchBean.getTitle(), esNewsSearchBean.getStartTime());
    }

    /**
     * 新闻阅评获取广播电视节目订阅列表
     * <AUTHOR>
     * @date 2024/4/19
     */
    @PostMapping("/searchByHostAndProgram")
    public PageResult<EsBean> searchByHostAndProgram(@RequestBody EsNewsSearchBean esNewsSearchBean) {
        return newsESSearchService.searchByHostAndProgram(esNewsSearchBean);
    }
}
