package com.boryou.servs.dplatform.enums;

/**
 * @description 媒体类型枚举类
 * <AUTHOR>
 * @date 2024/4/28 11:16
 */
public enum MediaTypeEnum {
    //论坛
    FORUM(0, "论坛社区"),
    //新闻网站
    NEWS(1, "新闻"),
    //博客
    BLOG(2, "博客"),
    //微博
    WEIBO(3, "微博"),
    //微信
    WECHAT(5, "微信"),
    //手机客户端  抖音 快手
    CLIENT(6, "客户端"),
    //广播
    AUDIO(8, "广播"),
    //电视
    TV(9, "电视"),
    //图片
    IMAGE(10, "图片"),
    //小视频 通过host限定 抖音，快手，头条
    VIDEO(11, "短视频"),
    //国外专利
    OVERSEAS_PATENT(13, "国外专利"),
    //国外期刊
    OVERSEAS_JOURNAL(14, "国外期刊"),
    //国内专利
    DOMESTIC_PATENT(15, "国内专利"),
    //国内期刊
    DOMESTIC_JOURNAL(16, "国内期刊"),
    //电子报
    EPAPER(17, "电子报刊"),
    //招标
    INVITE(18, "招投标"),
    //融资
    FINANCING(19, "投融资"),
    //海外
    OVERSEAS(24, "国外新闻"),
    //政务
    GOV(25, "政务"),
    //评论
    COMMENT(26, "评论");

    int value;
    String desc;

    MediaTypeEnum(int value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(String value) {
        for (MediaTypeEnum mediaTypeEnum : MediaTypeEnum.values()) {
            if (mediaTypeEnum.value == Integer.parseInt(value)) {
                return mediaTypeEnum.desc;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getAllType() {
        StringBuilder str = new StringBuilder();
        for (MediaTypeEnum item : MediaTypeEnum.values()) {
            if (str.length() != 0) {
                str.append(",");
            }
            str.append(item.getValue());
        }
        return str.toString();
    }
}
