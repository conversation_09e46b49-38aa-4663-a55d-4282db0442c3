package com.boryou.servs.dplatform.module.external.bo;


import java.time.LocalDateTime;


/**
 * 分析专题实体类
 * <AUTHOR>
 * @date 2019-06-28 10:33:28
 */
public class AnalysisSubject {

    private long id;
    private long userId;
    private String title;
    private LocalDateTime createTime;

    private String keywordPosition;
    /**
     * set值会自动替换中文逗号为英文下逗号
     */
    private String areaWords;
    /**
     * set值会自动替换中文逗号为英文下逗号
     */
    private String personWords;
    /**
     * set值会自动替换中文逗号为英文下逗号
     */
    private String eventWords;
    /**
     * set值会自动替换中文逗号为英文下逗号
     */
    private String excludeWords;
    /**
     * set值会自动替换中文逗号为英文下逗号
     **/
    private String infoTypes;

    private String startTime;
    private String endTime;


    /**
     * 规则字段
     */
    private String rule;

//    public String getAllKeywords() {
//        return CommonUtil.getAllWords(areaWords, personWords, eventWords);
//    }
//
//    public String getRule() {
//        if (rule != null && rule.length() > 0) {
//            return rule;
//        }
//        this.rule = AnalysisRuleUtil.makeRule(this.areaWords, this.personWords, this.eventWords, this.excludeWords, this.keywordPosition);
//        return this.rule;
//    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }


    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public String getKeywordPosition() {
        return keywordPosition;
    }

    public void setKeywordPosition(String keywordPosition) {
        this.keywordPosition = keywordPosition;
    }


    public String getAreaWords() {
        return areaWords;
    }

    public void setAreaWords(String areaWords) {
        this.areaWords = (areaWords == null ? areaWords : areaWords.replaceAll("，", ","));
    }


    public String getPersonWords() {
        return personWords;
    }

    public void setPersonWords(String personWords) {
        this.personWords = (personWords == null ? personWords : personWords.replaceAll("，", ","));
    }


    public String getEventWords() {
        return eventWords;
    }

    public void setEventWords(String eventWords) {
        this.eventWords = (eventWords == null ? eventWords : eventWords.replaceAll("，", ","));
    }


    public String getExcludeWords() {
        return excludeWords;
    }

    public void setExcludeWords(String excludeWords) {
        this.excludeWords = (excludeWords == null ? excludeWords : excludeWords.replaceAll("，", ","));
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getInfoTypes() {
        return infoTypes;
    }

    public void setInfoTypes(String infoTypes) {
        this.infoTypes = infoTypes;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
