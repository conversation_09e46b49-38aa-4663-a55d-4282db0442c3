package com.boryou.servs.dplatform.module.newsreview.service;

import cn.hutool.json.JSONObject;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.sb.EsNewsSearchBean;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface NewsESSearchService {

    /**
    * 新闻阅评广播电视媒体每日发文数量统计
    * <AUTHOR>
    * @date 2024/4/19
    */
    Map<String, Integer> searchByTimeFacet(String host, String programName, String startTime, String endTime);

    /**
    * 新闻阅评根据信息来源页面查询某天的报纸信息
    * <AUTHOR>
    * @date 2024/4/19
    */
    PageResult<EsBean> getEpaperBySector(String time, String sector);


    PageResult<EsBean> getFullVersionProgramSegments(String title, String startTime);

    EsBean searchByUrl(String sector, String time);

    PageResult<EsBean> searchByHostAndProgram(EsNewsSearchBean esNewsSearchBean);
}
