package com.boryou.servs.dplatform.module.hfyuqing.pojo;

import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description ES对应实体
 * @date 2024/4/22 16:38
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class EsBeanVO extends EsBean {
    private String highLightText;
    private String highLightTitle;
    private String highLightAuthor;
}
