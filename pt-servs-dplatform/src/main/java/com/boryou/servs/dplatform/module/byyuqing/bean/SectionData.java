package com.boryou.servs.dplatform.module.byyuqing.bean;


import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;

import java.util.List;

/**
 * 传播路径，用来装载每个小时段内的数据情况
 * <AUTHOR>
 * @date 2015-12-30 下午9:18:20
 */
public class SectionData {
    private int allNum; // 时段内数据量
    private List<IndexResultBean> results; // 时段内前10条数据
    private int category;// 分类等级
    private String startTime;// 开始时间
    private String endTime;// 结束时间

    public SectionData() {
    }

    public SectionData(int allNum, List<IndexResultBean> results, String startTime, String endTime) {
        this.allNum = allNum;
        this.results = results;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public IndexResultBean getFirst() {
        if (results != null && results.size() > 0) {
            return results.get(0);
        }
        return null;
    }

    public int getAllNum() {
        return allNum;
    }

    public void setAllNum(int allNum) {
        this.allNum = allNum;
    }

    public List<IndexResultBean> getResults() {
        return results;
    }

    public void setResults(List<IndexResultBean> results) {
        this.results = results;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "SectionData{" + "allNum=" + allNum + ", results=" + results + ", category=" + category + ", startTime='" + startTime + '\'' + ", endTime='" + endTime + '\'' + '}';
    }
}
