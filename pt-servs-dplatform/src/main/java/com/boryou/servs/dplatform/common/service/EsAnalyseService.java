package com.boryou.servs.dplatform.common.service;


import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsSearchTimeBO;
import com.boryou.servs.dplatform.pojo.vo.TimeCountFlowVO;
import com.boryou.servs.dplatform.pojo.vo.TimeFlowVO;

import java.util.List;
import java.util.Map;

/**
 * @description Es统计分析Service接口
 * <AUTHOR>
 * @date 2024/4/26 9:17
 */
public interface EsAnalyseService {

    /**
     * 情感分析*
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    Map<String,Long> emotionAnalysis(EsSearchBO esSearchBO);

    /**
     * 情感分析
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    Map<String,Long> emotionAnalysisForOriginal(EsSearchBO esSearchBO);

    /**
     * 情感分析
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    Map<String,Long> siteMetaAnalysis(EsSearchBO esSearchBO);

    /**
     * 意见领袖*
     * @param esSearchBO
     * @return
     */
    List<EsBean> opinionLeader(EsSearchBO esSearchBO);

    /**
     * 网民观点*
     * @param esSearchBO
     * @return
     */
    List<EsBean> netizenOpinion(EsSearchBO esSearchBO);

    /**
     * 媒体类型分布*
     * @param esSearchBO
     * @return
     */
    Map<String, Long> getMediaTypeMap(EsSearchBO esSearchBO);


    /**
     * 媒体类型分布
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    Map<String,Long> getMediaTypeMapForOriginal(EsSearchBO esSearchBO);

    /**
    * 媒体活跃度分析
    */
    Map<String,Long> mediaActiveMap(EsSearchBO esSearchBO);

    /**
    * 地域分布图
    */
    Map<String,Long> areaMap(EsSearchBO esSearchBO);

    List<TimeFlowVO> time(EsSearchTimeBO esSearchTimeBO);
    List<TimeCountFlowVO> timeCount(EsSearchTimeBO esSearchTimeBO);
}
