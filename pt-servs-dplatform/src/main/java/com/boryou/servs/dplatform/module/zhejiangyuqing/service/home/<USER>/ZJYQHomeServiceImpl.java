package com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.impl;

import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.ZJYQHomeService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

/**
 * <AUTHOR>
 * @date 2024-05-24 17:53
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ZJYQHomeServiceImpl implements ZJYQHomeService {
    private final ElasticsearchClient esClient;

    //一次全查，没有带方案的--保留备用
    @Override
    public List<AreaOverviewBO> areaOverviewOld(EsSearchBO esSearchBO) throws IOException {
        long start = System.currentTimeMillis();
        String contentAreaCode = esSearchBO.getContentAreaCode();
        List<AreaOverviewBO> areaOverviewBOS = new ArrayList<>(20);
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        String aggInclude1 = esSearchBO.getAggInclude();
        if (StrUtil.isEmpty(aggInclude1)) {
            throw new RuntimeException("参数异常!");
        }
        SearchRequest searchRequest  = SearchRequest.of(
                s -> s.index(EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime())).query(builder.build()._toQuery()).size(0).aggregations(
                        "AreaRes", a -> a.terms(t -> t.field("contentAreaCode").size(30).include(v->v.terms(Arrays.asList(aggInclude1.split(",")))))
                                .aggregations("TypeRes", k -> k.terms(f -> f.field("type").size(30)))
                ));
        SearchResponse<Integer> response = esClient.search(searchRequest, Integer.class);
        Map<String, AreaOverviewBO> value1 = new LinkedHashMap<>();
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),searchRequest,passedTime);
        };
        if (response != null) {
            List<StringTermsBucket> array = ((StringTermsAggregate) response.aggregations().get("AreaRes")._get()).buckets().array();
            for (StringTermsBucket stringTermsBucket : array) {
                String accountName = stringTermsBucket.key().stringValue();
                if (StrUtil.isEmpty(accountName)){
                    //做一次过滤
                    continue;
                }
                Aggregate accountAggregations = stringTermsBucket.aggregations().get("TypeRes");
                List<LongTermsBucket> array1 = ((LongTermsAggregate) accountAggregations._get()).buckets().array();
                AreaOverviewBO e = new AreaOverviewBO();
                for (LongTermsBucket typeAgg : array1) {
                    String key = String.valueOf(typeAgg.key());
                    long value2 = typeAgg.docCount();
                    switch (key) {
                        case "1":
                            e.setNews(value2);
                            break;
                        case "0":
                            e.setForum(value2);
                            break;
                        case "3":
                            e.setWeibo(value2);
                            break;
                        case "5":
                            e.setWechat(value2);
                            break;
                    }
                }
                e.setAreaName(accountName);
                if ((e.getForum()+e.getNews()+e.getWechat()+e.getWeibo())==0){
                    continue;
                }
                value1.put(accountName, e);
            }
        }
        Set<String> strings = value1.keySet();
        for (String string : strings) {
            AreaOverviewBO stringLongMap = value1.get(string);
            areaOverviewBOS.add(stringLongMap);
        }
        return areaOverviewBOS;
    }

    public static void main(String[] args) {
        System.out.println("123".substring(0,2));
    }
//    @Override
//    public AreaOverviewBO areaOverview(EsSearchBO esNewsSearchBean) throws IOException {
//        String contentAreaCode = esNewsSearchBean.getContentAreaCode();
//        BoolQuery.Builder builder = EsUtil.buildQuery(esNewsSearchBean);
//        SearchResponse<Integer> response = esClient.search(
//                s -> s.index(EsUtil.getIndexes(esNewsSearchBean.getStartTime(), esNewsSearchBean.getEndTime())).query(builder.build()._toQuery()).size(0)
//                        .aggregations("TypeRes", k -> k.terms(f -> f.field("type"))
//                        ), Integer.class);
//        if (response != null) {
//            Aggregate accountAggregations = response.aggregations().get("TypeRes");
//            List<LongTermsBucket> array1 = ((LongTermsAggregate) accountAggregations._get()).buckets().array();
//            AreaOverviewBO e = new AreaOverviewBO();
//            e.setNews(0L);
//            e.setForum(0L);
//            e.setWechat(0L);
//            e.setWeibo(0L);
//            for (LongTermsBucket typeAgg : array1) {
//                String key = String.valueOf(typeAgg.key());
//                long value2 = typeAgg.docCount();
//                switch (key) {
//                    case "1":
//                        e.setNews(value2);
//                        break;
//                    case "0":
//                        e.setForum(value2);
//                        break;
//                    case "5":
//                        e.setWeibo(value2);
//                        break;
//                    case "6":
//                        e.setWechat(value2);
//                        break;
//                    default:
//                        break;
//                }
//                String areaName = getAreaNames(Long.valueOf(contentAreaCode));
//                e.setAreaName(areaName);
//            }
//            return e;
//        }
//        return null;
//    }


//    public String getAreaNames(Long code) {
//        Map<Long, String> maps = new HashMap<>();
//        maps.put(330000L, "浙江高院");
//        maps.put(330100L, "杭州中院");
//        maps.put(330200L, "宁波中院");
//        maps.put(330300L, "温州中院");
//        maps.put(330400L, "嘉兴中院");
//        maps.put(330500L, "湖州中院");
//        maps.put(330600L, "绍兴中院");
//        maps.put(330700L, "金华中院");
//        maps.put(330800L, "衢州中院");
//        maps.put(330900L, "舟山中院");
//        maps.put(331000L, "台州中院");
//        maps.put(331100L, "丽水中院");
//
//        //丽水
//        maps.put(331102L,"莲都区法院");
//        maps.put(331181L,"龙泉市法院");
//        maps.put(331121L,"青田县法院");
//        maps.put(331126L,"庆元县法院");
//        maps.put(331122L,"缙云县法院");
//        maps.put(331123L,"遂昌县法院");
//        maps.put(331124L,"松阳县法院");
//        maps.put(331125L,"松阳县法院");
//        maps.put(331127L,"景宁县法院");
//        return maps.get(code);
//    }
}

