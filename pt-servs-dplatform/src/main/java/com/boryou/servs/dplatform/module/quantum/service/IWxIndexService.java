package com.boryou.servs.dplatform.module.quantum.service;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;

import java.util.Map;

public interface IWxIndexService {
    PageResult<EsBean> search(EsSearchBO esSearchBO);

    Map<String, Long> mediaTypeCountForOriginal(EsSearchBO esSearchBO);

    boolean updatePageView(String esId);
}
