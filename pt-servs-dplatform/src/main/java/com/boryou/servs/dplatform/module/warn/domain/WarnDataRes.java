package com.boryou.servs.dplatform.module.warn.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


import java.util.Date;
import java.util.List;

@Data
public class WarnDataRes {
    /**
     * 方案id
     */
    private String planId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 量子预警用, 使用前检查es索引是否存在此字段
     */
    private List<String> users;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 文章id
     */
    private String articleId;
    /**
     * 命中关键词
     */
    private List<String> hitWord;

    private String warnTime;

    /**
     * 预警类型 1: 系统 2: 人工
     */
    private Integer warnType;
    /**
     * 预警级别
     */
    private Integer warnGrade;
    /**
     * 信源级别
     */
    private Integer accountGrade;

    /**
     * 信息浏览 0:未读 1:已读
     */
    private Integer readFlag;

    /**
     * id*
     */
    private String id;
    /**
     * 媒体类型*
     */
    private Integer type;
    /**
     * 发文时间*
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 标题*
     */
    private String title;
    /**
     * 正文*
     */
    private String text;
    /**
     * 原文链接*
     */
    private String url;
    /**
     * host*
     */
    private String host;
    /**
     * (账号/作者)昵称*
     */
    private String author;
    /**
     * (账号/作者)id*
     */
    private String authorId;

    /**
     * 站点地域(码)*
     */
    private String siteAreaCode;

    /**
     * 内容地域(码)*
     */
    private List<String> contentAreaCode;

    /**
     * 站点标签*
     */
    private List<String> siteMeta;

    /**
     * 内容标签*
     */
    private List<String> contentMeta;

    /**
     * 所属板块*
     */
    private String sector;
    /**
     * 内容形式*
     */
    private List<Integer> contentForm;

    /**
     * 内容MD5*
     */
    private String md5;

    /**
     * 图片链接数组*
     */
    private List<String> picUrl;

    /**
     * 情感标识
     */
    private Integer emotionFlag;
    /**
     * 是否为原创*
     */
    private Boolean isOriginal;
    /**
     * 是否被标记为垃圾内容*
     */
    private Boolean isSpam;

    /**
     * 更新时间*
     */
    private String updateTime;

    /**
     * 发布日期
     */
    private String day;

    /**
     * 预警推送类型 1: 短信 2: 邮件 3: 微信 4: 系统
     */
    private List<Integer> pushType;
}
