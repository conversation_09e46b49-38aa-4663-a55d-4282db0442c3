package com.boryou.servs.dplatform.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.dplatform.common.service.EsAnalyseService;
import com.boryou.servs.dplatform.enums.EmotionEnum;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsSearchTimeBO;
import com.boryou.servs.dplatform.pojo.vo.TimeCountFlowVO;
import com.boryou.servs.dplatform.pojo.vo.TimeFlowVO;
import com.boryou.servs.dplatform.pojo.vo.TimeRoundFlowVO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description ES统计分析实现类
 * @date 2024/4/26 9:18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EsAnalyseServiceImpl implements EsAnalyseService {

    private final ElasticsearchClient esClient;

    /**
     * 情感分析
     *
     * @param esSearchBO 检索BO
     * @return 返回值
     */
    @Override
    public Map<String, Long> emotionAnalysis(EsSearchBO esSearchBO) {
        Map<String, Long> emotionMap = new HashMap<>();
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        try {
            String aggKey = "emotionMap";
            SearchResponse<Long> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(builder.build()._toQuery())
                            .aggregations(aggKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.EMOTION_FLAG.getFieldName()).size(3)))
                            .size(0), Long.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(aggKey);
                LongTermsAggregate longTermsAggregate = (LongTermsAggregate) aggregate._get();
                List<LongTermsBucket> array = longTermsAggregate.buckets().array();
                for (LongTermsBucket longTermsBucket : array) {
                    long key = longTermsBucket.key();
                    switch (EmotionEnum.getByValue(String.valueOf(key))) {
                        case NEGATIVE:
                            emotionMap.put(EmotionEnum.NEGATIVE.getValue(), longTermsBucket.docCount());
                            break;
                        case NEUTRAL:
                            emotionMap.put(EmotionEnum.NEUTRAL.getValue(), longTermsBucket.docCount());
                            break;
                        case POSITIVE:
                            emotionMap.put(EmotionEnum.POSITIVE.getValue(), longTermsBucket.docCount());
                            break;
                        default:
                            break;
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        emotionMap.putIfAbsent(EmotionEnum.NEGATIVE.getValue(), 0L);
        emotionMap.putIfAbsent(EmotionEnum.NEUTRAL.getValue(), 0L);
        emotionMap.putIfAbsent(EmotionEnum.POSITIVE.getValue(), 0L);
        return emotionMap;
    }

    @Override
    public Map<String, Long> emotionAnalysisForOriginal(EsSearchBO esSearchBO) {
//        if ((esSearchBO.getIsOriginal() == null ||  !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
        Map<String, Long> emotionMap = emotionAnalysis(esSearchBO);
        Long all = 0L;
        for (Map.Entry<String, Long> entry : emotionMap.entrySet()) {
            all += entry.getValue();
        }
        emotionMap.put("all", all);
        return emotionMap;
//        }
    }

    @Override
    public Map<String, Long> siteMetaAnalysis(EsSearchBO esSearchBO) {
        Map<String, Long> mediaMap = new HashMap<>();
        BoolQuery.Builder builder = new BoolQuery.Builder();
        // 站点标签or条件
        List<String> siteMetas = null;
        if (StrUtil.isNotEmpty(esSearchBO.getSiteMeta())) {
            siteMetas = Arrays.asList(esSearchBO.getSiteMeta().split(","));
            if (siteMetas.size() > 0) {
                List<FieldValue> fieldValues = siteMetas.stream().map(FieldValue::of).collect(Collectors.toList());
                builder.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.SITE_META.getFieldName()).terms(q -> q.value(fieldValues))));
            }
            for (String siteMate : siteMetas) {
                mediaMap.put(siteMate, 0L);
            }

            try {
                String aggKey = "siteMetaMap";
                SearchResponse<Long> response = esClient.search(
                        s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                                .query(builder.build()._toQuery())
                                .aggregations(aggKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.SITE_META.getFieldName()).size(3)))
                                .size(0), Long.class);
                if (response != null) {
                    StringTermsAggregate stringTermsAggregate = (StringTermsAggregate) response.aggregations().get(aggKey)._get();
                    List<StringTermsBucket> array = stringTermsAggregate.buckets().array();
                    for (StringTermsBucket stringTermsBucket : array) {
                        String key = stringTermsBucket.key().stringValue();
                        if (mediaMap.containsKey(key)) {
                            mediaMap.put(key, stringTermsBucket.docCount());
                        }
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (CollUtil.isNotEmpty(siteMetas)) {
            for (String siteMeta : siteMetas) {
                mediaMap.putIfAbsent(siteMeta, 0L);
            }
        }

        return mediaMap;
    }

    /**
     * 意见领袖
     *
     * @param esSearchBO
     * @return
     */
    @Override
    public List<EsBean> opinionLeader(EsSearchBO esSearchBO) {
        List<EsBean> resultList = new ArrayList<>();
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        Highlight highlight = EsUtil.getHighlight(-1);
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(builder.build()._toQuery())
                            .sort(SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.COMMENT_NUM.getFieldName()).order(SortOrder.Desc)))
                            .highlight(highlight)
                            .size(10), EsBean.class);
            if (response != null) {
                EsCommonServiceImpl.getBeansFromHighlight(resultList, response, 50);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultList;
    }

    @Override
    public List<EsBean> netizenOpinion(EsSearchBO esSearchBO) {
        List<EsBean> resultList = new ArrayList<>();
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(builder.build()._toQuery())
                            .sort(SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.REPRINT_NUM.getFieldName()).order(SortOrder.Desc)))
                            .size(10), EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                resultList.add(hit.source());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultList;
    }

    @Override
    public Map<String, Long> getMediaTypeMap(EsSearchBO esSearchBO) {
        SearchResponse<Integer> response = null;
        String aggNameKey = "typeAgg";
        BoolQuery.Builder bool = EsUtil.buildQuery(esSearchBO);
        SearchRequest searchRequest = SearchRequest.of(s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                .query(bool.build()._toQuery())
                .aggregations(aggNameKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).size(100).order(new NamedValue<>("_count", SortOrder.Desc)))
                )
                .size(0));
        try {
            response = esClient.search(searchRequest, Integer.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        if (aggregate._get() instanceof StringTermsAggregate) {
            List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
            for (StringTermsBucket bucket : array) {
                data.put(bucket.key().stringValue(), bucket.docCount());
            }
        } else if (aggregate._get() instanceof LongTermsAggregate) {
            List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
            for (LongTermsBucket bucket : array) {
                data.put(String.valueOf(bucket.key()), bucket.docCount());
            }
        }
        return data;
    }


    @Override
    public Map<String, Long> getMediaTypeMapForOriginal(EsSearchBO esSearchBO) {
//        if ((esSearchBO.getIsOriginal() == null ||  !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
        Map<String, Long> map = getMediaTypeMap(esSearchBO);
        Long all = 0L;
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            all += entry.getValue();
        }
        map.put("all", all);
        return map;
//        }
    }

    @Override
    public Map<String, Long> mediaActiveMap(EsSearchBO esSearchBO) {
        SearchResponse<Integer> response = null;
        String aggNameKey = "hostAgg";
        BoolQuery.Builder bool = EsUtil.buildQuery(esSearchBO);
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).size(30).order(new NamedValue<>("_count", SortOrder.Desc)))
                            )
                            .size(0), Integer.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        if (aggregate._get() instanceof StringTermsAggregate) {
            List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
            for (StringTermsBucket bucket : array) {
                data.put(bucket.key().stringValue(), bucket.docCount());
            }
        } else if (aggregate._get() instanceof LongTermsAggregate) {
            List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
            for (LongTermsBucket bucket : array) {
                data.put(String.valueOf(bucket.key()), bucket.docCount());
            }
        }
        return data;
    }

    @Override
    public Map<String, Long> areaMap(EsSearchBO esSearchBO) {
        Map<String, Long> areaMap = new HashMap<>();
        String areaCode = esSearchBO.getContentAreaCode();
        if (StrUtil.isNotEmpty(areaCode) && areaCode.contains(",")) {
            esSearchBO.setContentAreaCode("");
        }
        BoolQuery.Builder bool = EsUtil.buildQuery(esSearchBO);
//        if (StrUtil.isNotEmpty(areaCode)) {
//            bool.must(QueryBuilders.wildcard(w -> w.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).value(areaCode.replaceAll("00", "") + "*")));
//        }
        try {
            String aggKey = "areaMap";
            SearchResponse<Long> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).size(100)))
                            .size(0), Long.class);
            if (response != null) {
                StringTermsAggregate stringTermsAggregate = (StringTermsAggregate) response.aggregations().get(aggKey)._get();
                List<StringTermsBucket> array = stringTermsAggregate.buckets().array();
                for (StringTermsBucket stringTermsBucket : array) {
                    String key = stringTermsBucket.key().stringValue();
                    // 移除其他地区
                    if (StrUtil.isNotEmpty(areaCode)) {
                        String subAreaCode = areaCode;
                        if (areaCode.endsWith("0000")) {
                            //查询省级地域  移除外省的
                            subAreaCode = areaCode.substring(0, 2);
                        } else if (areaCode.endsWith("00")) {
                            //查询市级地域  移除外市的
                            subAreaCode = areaCode.substring(0, 4);
                        }
                        //查询区县  移除其他区县的
                        if (!key.startsWith(subAreaCode)) {
                            continue;
                        }

                    }
                    // 移除自己   查询区县时除外（查询省市时移除）
                    if (StrUtil.isNotEmpty(areaCode) && key.equals(areaCode) && areaCode.endsWith("00")) {
                        continue;
                    }
                    // 移除街道
                    if (key.length() != 6) {
                        continue;
                    }
                    // 查询全省时
                    //1.查询省和自治区时  省级单位===>市级单位地市     移除非市级地区
                    if (StrUtil.isNotEmpty(areaCode) && !areaCode.equals("110000") && !areaCode.equals("120000") && !areaCode.equals("310000") && !areaCode.equals("500000") && areaCode.endsWith("0000") && !key.endsWith("00")) {
                        continue;
                    }
                    //2.查询直辖市时  省级单位直辖市===>区县    移除非区县单位
                    if (StrUtil.isNotEmpty(areaCode) && (areaCode.equals("110000") || areaCode.equals("120000") || areaCode.equals("310000") || areaCode.equals("500000")) && key.endsWith("00")) {
                        continue;
                    }
                    // 查询全国时移除非省级地区
                    if (StrUtil.isEmpty(areaCode) && !key.endsWith("0000")) {
                        continue;
                    }
                    if (!areaMap.containsKey(key)) {
                        areaMap.put(key, stringTermsBucket.docCount());
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return areaMap;
    }

    @Override
    public List<TimeFlowVO> time(EsSearchTimeBO esSearchTimeBO) {
        String statisticType = esSearchTimeBO.getStatisticType();
        if (StrUtil.isBlankIfStr(statisticType)) {
            return Collections.emptyList();
        }
        try {
            List<TimeFlowVO> results = new ArrayList<>();
            EsSearchBO esSearchBO = new EsSearchBO();
            BeanUtil.copyProperties(esSearchTimeBO, esSearchBO);
            Query query = EsUtil.buildQuery(esSearchBO).build()._toQuery();

            String startTime = esSearchTimeBO.getStartTime();
            String endTime = esSearchTimeBO.getEndTime();
            List<String> indexes = EsUtil.getIndexes(esSearchTimeBO);
            List<TimeRoundFlowVO> timeRoundFlow = esSearchTimeBO.getTimeRoundFlow();
            SearchResponse<Integer> searchResponse = null;

            DateRangeAggregation.Builder dateRangeBuilder = new DateRangeAggregation.Builder()
                    .field("publishTime");
            for (TimeRoundFlowVO range : timeRoundFlow) {
                String startTimeRange = range.getStartTime();
                String endTimeRange = range.getEndTime();
                dateRangeBuilder.ranges(DateRangeExpression.of(r -> r
                        .key(startTimeRange + " | " + endTimeRange)
                        .from(f -> f.expr(startTimeRange))
                        .to(f -> f.expr(endTimeRange)))
                );
            }

            // Add the cardinality aggregation to the terms aggregation
            Aggregation termsWithCardinalityAggregation = Aggregation.of(a -> a
                    .terms(t -> t.field(statisticType)));

            // Add the terms aggregation to the date range aggregation
            Aggregation dateRangeWithTermsAggregation = Aggregation.of(a -> a
                    .dateRange(dateRangeBuilder.build())
                    .aggregations("by_type", termsWithCardinalityAggregation));
            SearchRequest request = SearchRequest.of(s -> s.index(indexes)
                    .trackTotalHits(t -> t.enabled(true))
                    .query(query)
                    .size(0)
                    .aggregations("date_ranges", dateRangeWithTermsAggregation));

            searchResponse = esClient.search(request, Integer.class);


            // Parse the search response

            DateRangeAggregate dateRangeAggregate = searchResponse.aggregations().get("date_ranges").dateRange();
            for (RangeBucket rangeBucket : dateRangeAggregate.buckets().array()) {
                String rangeKey = rangeBucket.key();
                long rangeCount = rangeBucket.docCount();
                TimeFlowVO timeFlowVO2 = new TimeFlowVO();
                timeFlowVO2.setKey(rangeKey);
                timeFlowVO2.setCount(String.valueOf(rangeCount));

                List<LongTermsBucket> byType = rangeBucket.aggregations().get("by_type").lterms().buckets().array();

                List<TimeFlowVO> timeFlowList = new ArrayList<>();
                byType.forEach(item -> {
                    TimeFlowVO timeFlowVO1 = new TimeFlowVO();
                    long key = item.key();
                    long count = item.docCount();
                    timeFlowVO1.setKey(String.valueOf(key));
                    timeFlowVO1.setCount(String.valueOf(count));
                    timeFlowList.add(timeFlowVO1);
                });
                timeFlowVO2.setChildren(timeFlowList);
                results.add(timeFlowVO2);
            }
            return results;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }
    @Override
    public List<TimeCountFlowVO> timeCount(EsSearchTimeBO esSearchTimeBO) {
//        String statisticType = esSearchTimeBO.getStatisticType();
//        if (StrUtil.isBlankIfStr(statisticType)) {
//            return Collections.emptyList();
//        }
        try {
            List<TimeCountFlowVO> results = new ArrayList<>();
            EsSearchBO esSearchBO = new EsSearchBO();
            BeanUtil.copyProperties(esSearchTimeBO, esSearchBO);
            Query query = EsUtil.buildQuery(esSearchBO).build()._toQuery();
            List<String> indexes = EsUtil.getIndexes(esSearchTimeBO);
            List<TimeRoundFlowVO> timeRoundFlow = esSearchTimeBO.getTimeRoundFlow();
            SearchResponse<Object> searchResponse = null;

            DateRangeAggregation.Builder dateRangeBuilder = new DateRangeAggregation.Builder()
                    .field("publishTime");
            for (TimeRoundFlowVO range : timeRoundFlow) {
                String startTimeRange = range.getStartTime();
                String endTimeRange = range.getEndTime();
                dateRangeBuilder.ranges(DateRangeExpression.of(r -> r
                        .key(startTimeRange + " | " + endTimeRange)
                        .from(f -> f.expr(startTimeRange))
                        .to(f -> f.expr(endTimeRange)))
                );
            }



            // Add the terms aggregation to the date range aggregation
            Aggregation dateRangeWithTermsAggregation = Aggregation.of(a -> a
                    .dateRange(dateRangeBuilder.build())
                 /*   .aggregations("docCount", c->c.valueCount(v->v.field("id")))*/);
            SearchRequest request = SearchRequest.of(s -> s.index(indexes)
                    .trackTotalHits(t -> t.enabled(true))
                    .query(query)
                    .size(0)
                    .aggregations("date_ranges", dateRangeWithTermsAggregation));
            log.info("aa:"+request);
            searchResponse = esClient.search(request, Object.class);


            // Parse the search response

            DateRangeAggregate dateRangeAggregate = searchResponse.aggregations().get("date_ranges").dateRange();
            for (RangeBucket rangeBucket : dateRangeAggregate.buckets().array()) {
                String rangeKey = rangeBucket.key();
                long rangeCount = rangeBucket.docCount();
                TimeCountFlowVO timeFlowVO2 = new TimeCountFlowVO();
                timeFlowVO2.setKey(rangeKey);
                timeFlowVO2.setCount(String.valueOf(rangeCount));
                results.add(timeFlowVO2);
            }
            return results;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }
}
