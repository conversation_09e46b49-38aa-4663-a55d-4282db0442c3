package com.boryou.servs.dplatform.module.hfyuqing.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.elasticsearch.core.search.TrackHits;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.EsAnalyseService;
import com.boryou.servs.dplatform.enums.EmotionEnum;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.hfyuqing.enums.FieldEnum;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.EsBeanVO;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.HomeDataVO;
import com.boryou.servs.dplatform.pojo.bean.SpecifySite;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsManyTypeUtil;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

@Component
@Slf4j
@RequiredArgsConstructor
public class ElasticsearchDao {

    private final ElasticsearchClient client;
    private final EsAnalyseService esAnalyseService;

    public PageResult<EsBeanVO> homeData(HomeDataVO homeDataVO) {
        long start = System.currentTimeMillis();
        EsSearchBO esSearchBO = homeDataVO.getEsSearchBO();
        esSearchBO.setUseIkSmart(homeDataVO.getUseIkSmart());
        Boolean typePosition = homeDataVO.getTypePosition();
        BoolQuery.Builder boolQueryBuilder = EsManyTypeUtil.buildManyTypeQuery(esSearchBO, typePosition);
        String title = homeDataVO.getTitle();
        List<SpecifySite> siteList = homeDataVO.getSpecifySites();
        if (CollUtil.isNotEmpty(siteList)) {
            Map<Integer, List<SpecifySite>> listMap = siteList.stream().collect(Collectors.groupingBy(SpecifySite::getType));
            BoolQuery.Builder bool = QueryBuilders.bool();
            for (Map.Entry<Integer, List<SpecifySite>> entry : listMap.entrySet()) {
                BoolQuery.Builder innerBool = QueryBuilders.bool();
                Integer key = entry.getKey();
                List<String> authors = entry.getValue().stream()
                        .map(SpecifySite::getAuthor)
                        .filter(author -> !author.isEmpty())
                        .collect(Collectors.toList());
                Query typeTerm = QueryBuilders.term(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).value(key));
                innerBool.filter(typeTerm);
                if (!authors.isEmpty()) {
                    List<FieldValue> fieldValues = authors.stream().map(FieldValue::of).collect(Collectors.toList());
                    Query authorTerms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(fieldValues)));
                    innerBool.filter(authorTerms);
                }
                // 根据 key 处理其他字段的查询，例如 HOST
                switch (key) {
                    case 3:
                        break;
                    case 5:
                    case 6:
                    case 0:
                    case 11:
                        List<FieldValue> fieldValues = entry.getValue().stream()
                                .map(SpecifySite::getHost).distinct().map(FieldValue::of).collect(Collectors.toList());
                        Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q->q.value(fieldValues)));
                        innerBool.filter(terms);
                        break;
                    // 可以添加更多的 case 语句来处理不同的 key
                }
                bool.should(innerBool.build()._toQuery());
            }
            boolQueryBuilder.filter(bool.build()._toQuery());
        }
        if (!StrUtil.isBlankIfStr(title)) {
            QueryStringQuery.Builder builder = QueryBuilders.queryString();
            if (homeDataVO.getUseIkSmart() != null && homeDataVO.getUseIkSmart()) {
                builder.analyzer("ik_smart");
            }
            QueryStringQuery.Builder query = builder.fields(EsBeanFieldEnum.TYPE.getFieldName()).query("\"" + title + "\"");
            boolQueryBuilder.must(query.build()._toQuery());
        }

        int sortType = homeDataVO.getSortType();
        String sortOrder = homeDataVO.getSortOrder();
        List<SortOptions> sortOptions = buildSort(sortType, sortOrder);

        // 构建搜索请求
        int from = homeDataVO.getFrom();
        int size = homeDataVO.getSize();
        TrackHits.Builder trackHits = new TrackHits.Builder();
        trackHits.enabled(true);
        SearchRequest.Builder builder = new SearchRequest.Builder()
                .index(EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime())) // 确保这里是你的ES索引名
                .query(boolQueryBuilder.build()._toQuery())
                .sort(sortOptions)
                .trackTotalHits(trackHits.build());

        Boolean removeRepeated = homeDataVO.getRemoveRepeated();
        PageResult<EsBeanVO> pageResult = new PageResult<>();
        List<EsBeanVO> results = new ArrayList<>();
        // 执行搜索
        SearchResponse<EsBeanVO> response = null;
        SearchRequest searchRequest;
        if (removeRepeated != null && removeRepeated) {
            try {
                final String countAgg = "countAgg";
                final String md5Agg = "md5Agg";
                final String topAgg = "topAgg";
                final String sortTimeAgg = "sortTimeAgg";
                SortOrder sort;
                Aggregation aggSort;
                if (SortOrder.Asc.jsonValue().equals(sortOrder)) {
                    sort = SortOrder.Asc;
                    aggSort = AggregationBuilders.min(t -> t.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()));
                } else {
                    sort = SortOrder.Desc;
                    aggSort = AggregationBuilders.max(t -> t.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()));
                }
                NamedValue<SortOrder> namedValue = NamedValue.of(sortTimeAgg, sort);
                builder.from(0);
                builder.size(0);
                int total = from + size + 100;
                builder.aggregations(countAgg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())
                                .precisionThreshold(30000)))
                        .aggregations(md5Agg, a -> a.terms(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())
                                        .size(total <= 0 ? 1 : total)
                                        .order(namedValue))
                                .aggregations(topAgg, top -> top.topHits(t -> t.size(1)
                                        .sort(sortOptions)))
                                .aggregations(sortTimeAgg, aggSort)
                                .aggregations("page", p->p.bucketSort(t->t.from(from).size(size))));
                searchRequest = builder.build();
                response = client.search(searchRequest, EsBeanVO.class);
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate1 = aggregations.get(md5Agg);
                List<StringTermsBucket> array = ((StringTermsAggregate) aggregate1._get()).buckets().array();
                for (StringTermsBucket bucket : array) {
                    HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                    if (hits == null) {
                        pageResult.setTotal(0L);
                        pageResult.setRecords(results);
                        return pageResult;
                    }
                    for (Hit<JsonData> hit : hits.hits()) {
                        if (hit.source() != null) {
                            results.add(hit.source().to(EsBeanVO.class));
                        }
                    }
                }
                pageResult.setRecords(results);
                Aggregate aggregate = aggregations.get(countAgg);
                pageResult.setTotal(((CardinalityAggregate) aggregate._get()).value());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            try {
                builder.from(from);
                searchRequest = builder.size(size).build();
                response = client.search(searchRequest, EsBeanVO.class);
                HitsMetadata<EsBeanVO> hits = response.hits();
                if (hits == null || hits.total() == null) {
                    pageResult.setTotal(0L);
                    pageResult.setRecords(results);
                    return pageResult;
                }
                long value = hits.total().value();
                for (Hit<EsBeanVO> hit : hits.hits()) {
                    EsBeanVO source = hit.source();
                    if (source == null) {
                        continue;
                    }
                    Map<String, List<String>> highlight = hit.highlight();
                    if (highlight.containsKey("title")) {
                        source.setHighLightTitle(CollUtil.join(highlight.get("title"), ";"));
                    }
                    if (highlight.containsKey("text")) {
                        source.setHighLightText(CollUtil.join(highlight.get("text"), ";"));
                    }
                    if (highlight.containsKey("author")) {
                        source.setHighLightTitle(CollUtil.join(highlight.get("author"), ";"));
                    }
                    results.add(source);
                }
                pageResult.setTotal(value);
                pageResult.setRecords(results);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        // log.warn("searchRequest :{}", searchRequest);
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        } else {
            log.info("Normal Request cost {} seconds", passedTime);
        }
        return pageResult;
    }

    private List<SortOptions> buildSort(int sortType, String sortOrder) {
        List<SortOptions> sortOptionsList = new ArrayList<>();
        SortOrder sort;
        if (SortOrder.Asc.jsonValue().equals(sortOrder)) {
            sort = SortOrder.Asc;
        } else {
            sort = SortOrder.Desc;
        }
        switch (sortType) {
            case 0:
                sortOptionsList.add(SortOptionsBuilders.field(item -> item.field(FieldEnum.TIME.getName()).order(sort)));
                sortOptionsList.add(SortOptionsBuilders.field(item -> item.field(FieldEnum.REPRINT_NUM.getName()).order(sort)));
                break;
            case 1:
                sortOptionsList.add(SortOptionsBuilders.field(item -> item.field(FieldEnum.SCORE.getName()).order(sort)));
                break;
            case 2:
                sortOptionsList.add(SortOptionsBuilders.field(item -> item.field(FieldEnum.REPRINT_NUM.getName()).order(sort)));
                break;
            case 3:
                sortOptionsList.add(SortOptionsBuilders.field(item -> item.field(FieldEnum.TIME.getName()).order(sort)));
                break;
            default:
                break;
        }
        return sortOptionsList;
    }

    public Map<String, Long> homeEmotion(HomeDataVO homeDataVO) {
        EsSearchBO esSearchBO = homeDataVO.getEsSearchBO();
        esSearchBO.setUseIkSmart(homeDataVO.getUseIkSmart());
        Boolean typePosition = homeDataVO.getTypePosition();
        BoolQuery.Builder boolQueryBuilder = EsManyTypeUtil.buildManyTypeQuery(esSearchBO, typePosition);
        Map<String, Long> emotionMap = new HashMap<>();
        try {
            String aggKey = "emotionMap";
            SearchResponse<Long> response = client.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(boolQueryBuilder.build()._toQuery())
                            .aggregations(aggKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.EMOTION_FLAG.getFieldName()).size(3)))
                            .size(0), Long.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(aggKey);
                LongTermsAggregate longTermsAggregate = (LongTermsAggregate) aggregate._get();
                List<LongTermsBucket> array = longTermsAggregate.buckets().array();
                for (LongTermsBucket longTermsBucket : array) {
                    long key = longTermsBucket.key();
                    switch (EmotionEnum.getByValue(String.valueOf(key))) {
                        case NEGATIVE:
                            emotionMap.put("负面", longTermsBucket.docCount());
                            break;
                        case NEUTRAL:
                            emotionMap.put("中性", longTermsBucket.docCount());
                            break;
                        case POSITIVE:
                            emotionMap.put("正面", longTermsBucket.docCount());
                            break;
                        default:
                            break;
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return emotionMap;
    }
}
