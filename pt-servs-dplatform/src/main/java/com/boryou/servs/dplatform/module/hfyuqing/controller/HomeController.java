package com.boryou.servs.dplatform.module.hfyuqing.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.EsBeanVO;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.HomeDataVO;
import com.boryou.servs.dplatform.module.hfyuqing.service.HomeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 舆情检索Controller*
 */
@RestController
@RequiredArgsConstructor
public class HomeController {

    private final HomeService homeService;

    @PostMapping("/yq/info/home/<USER>")
    public Return homeData(@RequestBody HomeDataVO homeDataVO) {
        PageResult<EsBeanVO> boryouBeanPageInfo = homeService.homeData(homeDataVO);
        return Return.ok(boryouBeanPageInfo);
    }
    @PostMapping("/yq/info/home/<USER>")
    public Map<String, Long> homeEmotion(@RequestBody HomeDataVO homeDataVO) {
        return homeService.homeEmotion(homeDataVO);
    }
}
