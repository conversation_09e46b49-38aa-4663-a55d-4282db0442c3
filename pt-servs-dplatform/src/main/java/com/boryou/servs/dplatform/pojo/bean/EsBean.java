package com.boryou.servs.dplatform.pojo.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description ES对应实体
 * @date 2024/4/22 16:38
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EsBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id*
     */
    private String id;
    /**
     * 媒体类型*
     */
    private Integer type;
    /**
     * 发文时间*
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    /**
     * 标题*
     */
    private String title;
    /**
     * 正文*
     */
    private String text;
    /**
     * 原文链接*
     */
    private String url;
    /**
     * host*
     */
    private String host;
    /**
     * (账号/作者)昵称*
     */
    private String author;
    /**
     * (账号/作者)id*
     */
    private String authorId;
    /**
     * 作者性别(0:女,1:男)*
     */
    private int authorSex;
    /**
     * 平台业务ID*
     */
    private String bizId;
    /**
     * 账号级别 (0达人  1蓝v  2红v  3橙v  4普通用户)*
     */
    private int accountLevel;
    /**
     * 信源等级  央级，省级，地市，重点，中小，企业商业
     */
    private String accountGrade;
    /**
     * 站点地域(码)*
     */
    private String siteAreaCode;
    /**
     * 内容地域(码)*
     */
    private List<String> contentAreaCode;
    /**
     * 站点标签*
     */
    private List<String> siteMeta;
    /**
     * 内容标签*
     */
    private List<String> contentMeta;
    /**
     * 粉丝数*
     */
    private Integer fansNum;
    /**
     * 阅读数*
     */
    private Integer readNum;
    /**
     * 评论数*
     */
    private Integer commentNum;
    /**
     * 点赞数*
     */
    private Integer likeNum;
    /**
     * 转发数*
     */
    private Integer reprintNum;
    /**
     * 所属板块*
     */
    private String sector;
    /**
     * 内容形式*
     */
    private List<Integer> contentForm;
    /**
     * 内容MD5*
     */
    private String md5;
    /**
     * 网页源码路径*
     */
    private String srcCodePath;
    /**
     * 封面图片链接*
     */
    private String coverUrl;
    /**
     * 图片链接数组*
     */
    private List<String> picUrl;
    /**
     * 音视频链接数组*
     */
    private List<String> avdUrl;
    /**
     * 情感标识
     */
    private Integer emotionFlag;
    /**
     * 是否为原创*
     */
    private Boolean isOriginal;
    /**
     * 是否被标记为垃圾内容*
     */
    private Boolean isSpam;

    /**
     * 更新时间*
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
    * 发布日期
    */
    private String day;
    /**
     * 相似信息数量*
     */
    private Integer similarCount;
    /**
     * 量子-信息库es的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long infoId;
    //页面浏览量-pv
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pageView;

    /**
     * 量子-二级分类id
     */
    private List<String>tagIds;
    /**
     * 量子-所属方案id
     */
    private List<String>planIds;
}
