
package com.boryou.servs.dplatform.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.servs.dplatform.common.entity.bo.TimeRoundFlowVO;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class TimeUtil {

    public static List<TimeRoundFlowVO> splitTimeInterval(String start, String end, int parts) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(start, formatter);
        LocalDateTime endTime = LocalDateTime.parse(end, formatter);

        long totalMinutes = ChronoUnit.SECONDS.between(startTime, endTime);
        long intervalMinutes = totalMinutes / parts;

        List<TimeRoundFlowVO> intervals = new ArrayList<>();
        LocalDateTime intervalStart = startTime;

        for (int i = 0; i < parts; i++) {
            LocalDateTime intervalEnd = i == parts - 1 ? endTime : intervalStart.plusSeconds(intervalMinutes);
            intervals.add(new TimeRoundFlowVO(formatTime(intervalStart), formatTime(intervalEnd)));
            intervalStart = intervalEnd;
        }

        return intervals;
    }

    private static String formatTime(LocalDateTime time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return time.format(formatter);
    }
    public static List<TimeRoundFlowVO> timeRange(String startTimeStr, String endTimeStr, DateField unit, int step, int offset) {
        DateTime startTime = DateUtil.parse(startTimeStr);
        DateTime endTime = DateUtil.parse(endTimeStr);
        DateRange dateRange = new DateRange(startTime, endTime, unit, step, true, false);
        List<TimeRoundFlowVO> timeRoundList = new ArrayList<>();
        String lastTime = "";
        for (DateTime dateTime : dateRange) {
            if (CharSequenceUtil.isBlank(lastTime)) {
                lastTime = dateTime.toString("yyyy-MM-dd HH:mm:ss");
            } else {
                TimeRoundFlowVO timeRoundFlowVO = new TimeRoundFlowVO();
                timeRoundFlowVO.setStartTime(lastTime);
                timeRoundFlowVO.setEndTime(dateTime.offsetNew(DateField.SECOND, offset).toString("yyyy-MM-dd HH:mm:ss"));
                timeRoundList.add(timeRoundFlowVO);
                lastTime = dateTime.toString("yyyy-MM-dd HH:mm:ss");
            }
        }
        TimeRoundFlowVO timeRoundFlowVO = new TimeRoundFlowVO();
        timeRoundFlowVO.setStartTime(lastTime);
        timeRoundFlowVO.setEndTime(endTimeStr);
        timeRoundList.add(timeRoundFlowVO);
        return timeRoundList;
    }

}
