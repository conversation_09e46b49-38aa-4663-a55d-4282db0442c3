package com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.impl;

import cn.hutool.json.JSONObject;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.StatisService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-06-14 09:27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StatisServiceImpl implements StatisService {
    private final ElasticsearchClient esClient;

    @Override
    public JSONObject mediaRank(EsSearchBO esSearchBO) throws IOException {
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        long start = System.currentTimeMillis();
        SearchRequest searchRequest = SearchRequest.of(s -> s.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()).size(0)
                .aggregations("by_host", a -> a.terms(t -> t.field("accountGrade").size(20)))
                .aggregations("total_distinct_hosts", a -> a.cardinality(t -> t.field("host")))
                .aggregations("total_docs_accountLevel", a -> a.valueCount(t -> t.field("id")))
        );
        SearchResponse<Integer> response = esClient.search(searchRequest
                , Integer.class);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > 5) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        }
        JSONObject res = new JSONObject();
        Map<String, Aggregate> aggregations = response.aggregations();
        res.set("mediaTotalNum", aggregations.get("total_distinct_hosts").cardinality().value());
        res.set("mediaPublishTotal", (long) aggregations.get("total_docs_accountLevel").valueCount().value());
        List<LongTermsBucket> array = aggregations.get("by_host").lterms().buckets().array();
        List<Map<String, Long>> arras = new ArrayList<>();
        for (LongTermsBucket stringTermsBucket : array) {
            arras.add(new HashMap<String, Long>() {
                {
                    put(String.valueOf(stringTermsBucket.key()), stringTermsBucket.docCount());
                }
            });
        }
        res.set("mediaRankData", arras);
        return res;
    }
}

