package com.boryou.servs.dplatform.enums;


/**
 * @description ES字段枚举类
 * <AUTHOR>
 * @date 2024/4/23 9:59
 */
public enum EsBeanFieldEnum {
    //id
    ID("id"),
    //类型
    TYPE("type"),
    //发文时间
    PUBLISH_TIME("publishTime"),
    //标题
    TITLE("title"),
    //正文
    TEXT("text"),
    //原文链接
    URL("url"),
    //host
    HOST("host"),
    //(账号/作者)昵称
    AUTHOR("author"),
    //(账号/作者)id
    AUTHOR_ID("authorId"),
    //作者性别(0:女,1:男)
    AUTHOR_SEX("authorSex"),
    //平台业务ID
    BIZ_ID("bizId"),
    //账号级别 (0达人  1蓝v  2红v  3橙v  4普通用户)
    ACCOUNT_LEVEL("accountLevel"),
    //信源等级 央级，省级，地市，重点，中小，企业商业
    ACCOUNT_GRADE("accountGrade"),
    //站点地域(码)
    SITE_AREA_CODE("siteAreaCode"),
    //内容地域(码)
    CONTENT_AREA_CODE("contentAreaCode"),
    // 站点标签
    SITE_META("siteMeta"),
    //内容标签
    CONTENT_META("contentMeta"),
    //粉丝数
    FANS_NUM("fansNum"),
    //阅读数
    READ_NUM("readNum"),
    //评论数
    COMMENT_NUM("commentNum"),
    //点赞数
    LIKE_NUM("likeNum"),
    //转发/在看数
    REPRINT_NUM("reprintNum"),
    //所属板块
    SECTOR("sector"),
    //内容形式
    CONTENT_FORM("contentForm"),
    //md5
    MD5("md5"),
    //源码路径
    SRC_CODE_PATH("srcCodePath"),
    //封面图片链接
    COVER_URL("coverUrl"),
    //内容图片链接数组
    PIC_URL("picUrl"),
    //音视频链接数组
    AVD_URL("avdUrl"),
    //情感标识
    EMOTION_FLAG("emotionFlag"),
    //是否为原创
    IS_ORIGINAL("isOriginal"),
    //是否被标记为垃圾内容
    IS_SPAM("isSpam"),
    //更新时间
    UPDATE_TIME("updateTime"),
    //发文日期
    DAY("day"),
    //分数
    SCORE("_score"),
    //浏览量
    PAGE_VIEW("pageView"),
    //量子二级分类
    TAG_IDS("tagIds"),
    //方案id
    PLANIDS("planIds");
    
    String fieldName;

    EsBeanFieldEnum(String name) {
        this.fieldName = name;
    }

    public String getFieldName(){
        return fieldName;
    }
}
