package com.boryou.servs.dplatform.module.quantum.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.InlineScript;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.UpdateByQueryRequest;
import co.elastic.clients.elasticsearch.core.UpdateByQueryResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.EsCommonService;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.module.quantum.service.IWxIndexService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;

/**
 * <AUTHOR>
 * @date 2024-07-31 09:24
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WxIndexServiceImpl implements IWxIndexService {


    private final ElasticsearchClient esClient;

    private final EsCommonService esCommonService;

    @Override
    public PageResult<EsBean> search(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        builder.must(QueryBuilders.term(s->s.field("planId").value(esSearchBO.getBusinessId())));
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        SearchRequest  searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(ESConstant.ES_INDEX_INFOLIB)
                    .query(builder.build()._toQuery())
                    .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                    .size(esSearchBO.getPageSize())
                    .sort(sortOptions)
            );
            try {
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                for (Hit<EsBean> hit : response.hits().hits()) {
                    results.add(hit.source());
                }
                pageResult.setRecords(results);
                pageResult.setTotal(response.hits().total().value());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("quantum/index/search报错{}",e.getMessage());
            }
        pageResult.setCurrent(esSearchBO.getPageNum());
        pageResult.setSize(esSearchBO.getPageSize());
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),searchRequest,passedTime);
        }else{
            log.info("Normal Request cost {} seconds",passedTime);
        }
        return pageResult;
    }

    @Override
    public Map<String, Long> mediaTypeCountForOriginal(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        Map<String, Long> data = new HashMap<>(50);
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        builder.must(QueryBuilders.term(s->s.field("planId").value(esSearchBO.getBusinessId())));
            SearchResponse<Integer> response = null;
            String aggNameKey = "typeAgg";
            SearchRequest request = SearchRequest.of(b -> b.index(ESConstant.ES_INDEX_INFOLIB).trackTotalHits(t -> t.enabled(true))
                    .query(builder.build()._toQuery())
                    .aggregations(aggNameKey, a -> a.terms(t -> t.field(esSearchBO.getAggName()))
                    )
                    .size(0)
            );
            try {
                long end = System.currentTimeMillis();
                double passedTime = (end - start) / 1000.0;
                if (passedTime > 5) {
                    log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),request,passedTime);
                }
                response = esClient.search(request, Integer.class);
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(aggNameKey);
                List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
                for (LongTermsBucket bucket : array) {
                    data.put(String.valueOf(bucket.key()), bucket.docCount());
                }
            } catch (IOException e) {
                log.error("common/info/search 通用查询方法");
            }
        return data;
    }

    @Override
    public boolean updatePageView(String esId) {
        UpdateByQueryRequest.Builder builder = new UpdateByQueryRequest.Builder();
        EsUtil.simpleTermQuery(Collections.singletonList(esId), builder, "id");
        InlineScript.Builder script = new InlineScript.Builder();
        script.source("if (ctx._source.pageView!= null) { ctx._source.pageView += 1; } else { ctx._source.pageView = 1; }");
        builder.script(item -> item.inline(script.build()));
        //todo 索引
        builder.index(ESConstant.ES_INDEX_INFOLIB);
        UpdateByQueryRequest build = builder.build();
        UpdateByQueryResponse updateByQueryResponse = null;
        try {
            updateByQueryResponse = esClient.updateByQuery(build);
          updateByQueryResponse.updated();
           //refresh 方法用于强制刷新索引，使得新添加、更新或删除的文档能够立即被搜索到。
          esClient.indices().refresh(s->s.index(ESConstant.ES_INDEX_INFOLIB));
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}

