package com.boryou.servs.dplatform.module.zhejiangyuqing.service.home;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.PlanEsSearchBO;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-24 17:42
 */
public interface PlanService {


    boolean saveHistoryPlan(PlanEsSearchBO esSearchBO) throws Exception;

    /**
     * 事件首发
     * @param esSearchBO
     * @return
     */
    List<EsBean> firstRelease(EsSearchBO esSearchBO);

    /**
     * 事件脉络
     *
     * @param esSearchBO
     * @return
     */
    PageResult<EsBean> eventContext(EsSearchBO esSearchBO);

    /**
     * 相关热文
     *
     * @param esSearchBO
     * @return
     */
    List<EsBean> relatedHotArticle(EsSearchBO esSearchBO);
}

