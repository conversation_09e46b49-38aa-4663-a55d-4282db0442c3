package com.boryou.servs.dplatform.module.newsreview.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.MediaTypeEnum;
import com.boryou.servs.dplatform.module.newsreview.service.NewsESSearchService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.sb.EsNewsSearchBean;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class NewsESSearchServiceImpl implements NewsESSearchService {

    private final ElasticsearchClient esClient;

    @Override
    public Map<String, Integer> searchByTimeFacet(String host, String programName, String startTime, String endTime) {
        Map<String, Integer> map = new HashMap<>();
        final String dayAgg = "dayAgg";

        List<Query> queryList = new ArrayList<>();
        if (StrUtil.isNotEmpty(host)) {
            queryList.add(QueryBuilders.match(q -> q.field(EsBeanFieldEnum.HOST.getFieldName()).query(host)));
        }
        if (StrUtil.isNotEmpty(programName)) {
            queryList.add(QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TITLE.getFieldName() + ".keyword").query(programName)));
        }
        queryList.add(QueryBuilders.queryString().fields(EsBeanFieldEnum.TEXT.getFieldName()).query("\"完整版\"").build()._toQuery());
        queryList.add(
                QueryBuilders.range(r -> r.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).from(DateUtil.format(DateUtil.parse(startTime, DatePattern.PURE_DATE_PATTERN), DatePattern.NORM_DATETIME_PATTERN))
                        .to(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(endTime, DatePattern.PURE_DATE_PATTERN), 1), DatePattern.NORM_DATETIME_PATTERN)))
        );
        BoolQuery.Builder bool = QueryBuilders.bool();
        bool.must(queryList).should(Arrays.asList(
                QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.AUDIO.getValue())),
                QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.TV.getValue()))
        ));
        try {
            SearchResponse<Integer> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(DateUtil.format(DateUtil.parse(startTime, DatePattern.PURE_DATE_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.parse(endTime, DatePattern.PURE_DATE_PATTERN), DatePattern.NORM_DATETIME_PATTERN)))
                            .query(bool.build()._toQuery())
                            .aggregations(dayAgg, a -> a.terms(t -> t.field(EsBeanFieldEnum.DAY.getFieldName())))
                            .size(0), Integer.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(dayAgg);
                List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
                for (StringTermsBucket bucket : array) {
                    map.put(bucket.key().stringValue(), Math.toIntExact(bucket.docCount()));
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public PageResult<EsBean> getEpaperBySector(String time, String sector) {
        PageResult<EsBean> esBeanPageResult = new PageResult<>();
        List<EsBean> EsBeanList = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();
        bool.must(
                Arrays.asList(
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.EPAPER.getValue())),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.SECTOR.getFieldName()).query(sector)),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.DAY.getFieldName()).query(DateUtil.format(DateUtil.parse(time, DatePattern.PURE_DATE_PATTERN), DatePattern.PURE_DATE_PATTERN)))
                )
        );
        DateTime dateTime = DateUtil.parse(time, DatePattern.PURE_DATE_PATTERN);
        String startTime = DateUtil.format(DateUtil.beginOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        String endTime = DateUtil.format(DateUtil.endOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);

        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(startTime,endTime))
                            .query(bool.build()._toQuery())
                            .size(100), EsBean.class);
            if (response != null) {
                for (Hit<EsBean> hit : response.hits().hits()) {
                    EsBeanList.add(hit.source());
                }
                esBeanPageResult.setRecords(EsBeanList);
                esBeanPageResult.setTotal(response.hits().total().value());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return esBeanPageResult;
    }

    @Override
    public PageResult<EsBean> getFullVersionProgramSegments(String title, String time) {
        PageResult<EsBean> esBeanList = new PageResult();
        List<EsBean> beanList = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();
        bool.must(
                Arrays.asList(
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TITLE.getFieldName() + ".keyword").query(title)),
                        QueryBuilders.queryString().fields(EsBeanFieldEnum.TEXT.getFieldName()).query("\"完整版\"").build()._toQuery(),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.DAY.getFieldName()).query(DateUtil.format(DateUtil.parse(time, DatePattern.PURE_DATE_PATTERN), DatePattern.PURE_DATE_PATTERN)))
                )
        ).should(
                Arrays.asList(
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.AUDIO.getValue())),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.TV.getValue()))
                )
        );
        DateTime dateTime = DateUtil.parse(time, DatePattern.PURE_DATE_PATTERN);
        String startTime = DateUtil.format(DateUtil.beginOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        String endTime = DateUtil.format(DateUtil.endOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(startTime,endTime))
                            .query(bool.build()._toQuery())
                            .size(100)
                            .sort(sort -> sort.field(
                                    f -> f.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).order(SortOrder.Asc)
                            )), EsBean.class);
            if (response != null) {
                for (Hit<EsBean> hit : response.hits().hits()) {
                    beanList.add(hit.source());
                }
                esBeanList.setRecords(beanList);
                esBeanList.setTotal(response.hits().total().value());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return esBeanList;
    }

    @Override
    public EsBean searchByUrl(String url, String time) {
        List<EsBean> EsBeanList = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();
        bool.must(
                Arrays.asList(
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(MediaTypeEnum.EPAPER.getValue())),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.URL.getFieldName()).query(url))
                )
        );
        DateTime dateTime = null;
        if (StrUtil.isNotEmpty(time)) {
            dateTime = DateUtil.parse(time, DatePattern.NORM_DATE_PATTERN);
        }
        String startTime = dateTime == null ? "" : DateUtil.format(DateUtil.beginOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        String endTime = dateTime == null ? "" : DateUtil.format(DateUtil.endOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(startTime,endTime))
                            .query(bool.build()._toQuery())
                            .size(1), EsBean.class);
            if (response != null) {
                for (Hit<EsBean> hit : response.hits().hits()) {
                    EsBeanList.add(hit.source());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (CollUtil.isNotEmpty(EsBeanList)) {
            return EsBeanList.get(0);
        } else {
            return new EsBean();
        }

    }

    @Override
    public PageResult<EsBean> searchByHostAndProgram(EsNewsSearchBean esNewsSearchBean) {
        PageResult<EsBean> esBeanPageResult = new PageResult<>();
        List<EsBean> EsBeanList = new ArrayList<>();

        DateTime dateTime = DateUtil.parse(esNewsSearchBean.getStartTime(), DatePattern.NORM_DATE_PATTERN);
        BoolQuery.Builder bool = QueryBuilders.bool();
        List<Query> queryList = new ArrayList<>();
        if (StrUtil.isNotEmpty(esNewsSearchBean.getHost()) && StrUtil.isNotEmpty(esNewsSearchBean.getProgram())) {
            List<String> hosts = new ArrayList<>(Arrays.asList(esNewsSearchBean.getHost().split(",")));
            List<String> titles = new ArrayList<>(Arrays.asList(esNewsSearchBean.getProgram().split(",")));
            if (hosts.size() == titles.size()) {
                for (int i = 0; i < hosts.size(); i++) {
                    int index = i;
                    queryList.add(QueryBuilders.bool().must(
                            Arrays.asList(
                                    QueryBuilders.match(q -> q.field(EsBeanFieldEnum.HOST.getFieldName()).query(hosts.get(index))),
                                    QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TITLE.getFieldName() + ".keyword").query(titles.get(index)))
                            )

                    ).build()._toQuery());

                }
            }
            if (CollUtil.isNotEmpty(queryList)) {
                bool.should(queryList);
            }
        }

        bool.must(
                Arrays.asList(
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).query(esNewsSearchBean.getType())),
                        QueryBuilders.queryString().fields(EsBeanFieldEnum.TEXT.getFieldName()).query("\"完整版\"").build()._toQuery(),
                        QueryBuilders.match(q -> q.field(EsBeanFieldEnum.DAY.getFieldName()).query(DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN)))
                )
        );

        String startTime = DateUtil.format(DateUtil.beginOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        String endTime = DateUtil.format(DateUtil.endOfDay(dateTime),DatePattern.NORM_DATETIME_PATTERN);
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(startTime,endTime))
                            .query(bool.build()._toQuery())
                            .size(100), EsBean.class);
            if (response != null) {
                for (Hit<EsBean> hit : response.hits().hits()) {
                    EsBeanList.add(hit.source());
                }
                esBeanPageResult.setRecords(EsBeanList);
                esBeanPageResult.setTotal(response.hits().total().value());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return esBeanPageResult;
    }
}
