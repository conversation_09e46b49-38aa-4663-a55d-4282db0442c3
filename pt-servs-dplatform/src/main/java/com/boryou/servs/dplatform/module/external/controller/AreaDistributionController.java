package com.boryou.servs.dplatform.module.external.controller;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dplatform.module.byyuqing.service.YqESSearchService;
import com.boryou.servs.dplatform.module.external.bo.IndexResultVo;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 区域分布-es版本
 *
 * <AUTHOR>
 * @date 2024-04-26 14:48
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/aiProbes/analyze")
public class AreaDistributionController {


    private final AreaDistributionService areaDistributionService;

    /**
     * 地域分布-es版本
     *
     * @param statisticsBean
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/26 14:56
     **/
    @PostMapping("/areaDistribution")
    public Map<String, Long> getWeiboAuthorSortByTime(@RequestBody StatisticsBean statisticsBean) {
        return areaDistributionService.getAreaDistribution(statisticsBean, "contentAreaCode");
    }

    /**
     * 传播路径分析-es版本
     *
     * @param statisticsBean
     * @return com.boryou.servs.common.bean.Return
     * <AUTHOR>
     * @date 2024/4/29 11:27
     **/
    @PostMapping("/propagationPath")
    public IndexResultVo propagationPath(@RequestBody StatisticsBean statisticsBean) {
        return areaDistributionService.propagationPath(statisticsBean);
    }

}

