package com.boryou.servs.dplatform.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Script;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.elasticsearch.core.search.TotalHits;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.NamedValue;
import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.common.constant.BasicConstant;
import com.boryou.servs.dplatform.common.service.EsCommonService;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.enums.SearchPositionEnum;
import com.boryou.servs.dplatform.enums.SortTypeEnum;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsUpdateBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.boryou.servs.common.constant.BasicConstant.INTERFACE_TIME_OUT;
import static com.boryou.servs.dplatform.util.EsUtil.buildKeyWord;
import static com.boryou.servs.dplatform.util.EsUtil.quotedEveryKeyWord;

/**
 * <AUTHOR>
 * @description ES公用实现类
 * @date 2024/4/23 10:25
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class EsCommonServiceImpl implements EsCommonService {

    private final ElasticsearchClient esClient;

    @Override
    public List<EsBean> searchDemo(String author) {
        EsSearchBO esSearchBO = new EsSearchBO();
        esSearchBO.setKeyWord1("安徽 合肥 黄山");
        esSearchBO.setStartTime("2024-04-01 00:00:00");
        esSearchBO.setEndTime("2024-04-15 00:00:00");
        esSearchBO.setIsOriginal(true);
        return this.search(esSearchBO).getRecords();
    }

    @Override
    public EsBean searchById(String id) {
        List<EsBean> results = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.ID.getFieldName()).value(id)));
        // 执行查询
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(null, null))
                            .query(bool.build()._toQuery()), EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                results.add(hit.source());
            }
        } catch (IOException e) {
            log.error("searchById查询有问题");
        }
        return results.size() > 0 ? results.get(0) : null;
    }

    @Override
    public EsBean searchByIdTime(EsSearchBO esSearchBO) {
        String id = esSearchBO.getId();
        String startTime = esSearchBO.getStartTime();
        String endTime = esSearchBO.getEndTime();
        List<EsBean> results = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.ID.getFieldName()).value(id)));
        // 执行查询
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO))
                            .query(bool.build()._toQuery()), EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                results.add(hit.source());
            }
        } catch (IOException e) {
            log.error("searchByIdTime查询有问题");
        }
        return results.size() > 0 ? results.get(0) : null;
    }


    @Override
    public PageResult<EsBean> search(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        if ((null != esSearchBO.getProjectType() && ProjectFlagEnum.QUANTUM.getValue() == esSearchBO.getProjectType()) && "1".equals(esSearchBO.getInfoFlag()) && "0".equals(esSearchBO.getInfoTag())) {
            //量子逻辑：如果是查入库的infolib 并且infotag是要查未入库的
            builder.mustNot(s -> s.term(t -> t.field("infoTag").value(1)));
        }
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        SearchRequest searchRequest;
        if ((esSearchBO.getIsOriginal() == null || !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
            searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(EsUtil.getIndexes(esSearchBO))
                    .query(builder.build()._toQuery())
                    .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                    .size(esSearchBO.getPageSize())
                    .sort(sortOptions)
            );
            try {
                log.warn("项目:{},{}", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest);
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                for (Hit<EsBean> hit : response.hits().hits()) {
                    results.add(hit.source());
                }
                pageResult.setRecords(results);
                pageResult.setTotal(response.hits().total().value());
            } catch (IOException e) {
                e.printStackTrace();
                log.error("异常查询语句:{}", searchRequest);
                log.error("common/info/search 通用查询方法");
            }
        } else {
            //以下为内容去重，并不能实现微博原创去重，微博原创去重请调用searchx方法
            final String countAgg = "countAgg";
            final String md5Agg = "md5Agg";
            final String topAgg = "topAgg";
            final String sortTimeAgg = "sortTimeAgg";
            NamedValue<SortOrder> namedValue = EsUtil.getBucketSort(esSearchBO.getSortType(), sortTimeAgg);
            Aggregation  aggSort = EsUtil.getAggregationSort(esSearchBO.getSortType());
            searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(EsUtil.getIndexes(esSearchBO))
                    .query(builder.build()._toQuery())
                    .size(0)
                    .aggregations(countAgg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())))
                    .aggregations(md5Agg, a -> a.terms(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())
                                    .size(esSearchBO.getPageNum() != 0 && esSearchBO.getPageSize() != 0 ? esSearchBO.getPageNum() * esSearchBO.getPageSize() : 1)
                                    .order(namedValue))
                            .aggregations(topAgg, top -> top.topHits(t -> t.size(1).sort(sortOptions)))
                            .aggregations( sortTimeAgg , aggSort)));
            try {
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                if (response != null) {
                    Map<String, Aggregate> aggregations = response.aggregations();
                    Aggregate aggregate1 = aggregations.get(md5Agg);
                    List<StringTermsBucket> array1 = ((StringTermsAggregate) aggregate1._get()).buckets().array();
                    EsBean bean;
                    for (StringTermsBucket bucket : array1) {
                        HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                        for (Hit<JsonData> hit : hits.hits()) {
                            if (hit.source() != null) {
                                bean = hit.source().to(EsBean.class);
                                bean.setSimilarCount((int) hits.total().value());
                                results.add(bean);
                            }
                        }
                    }
                    results = results.subList((esSearchBO.getPageNum() - 1) * esSearchBO.getPageSize(), results.size());
                    pageResult.setRecords(results);
                    Aggregate aggregate = aggregations.get(countAgg);
                    pageResult.setTotal(((CardinalityAggregate) aggregate._get()).value());
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("异常查询语句:{}", searchRequest);
                log.error("common/info/search 通用查询方法");
            }
        }
        pageResult.setCurrent(esSearchBO.getPageNum());
        pageResult.setSize(esSearchBO.getPageSize());
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        } else {
            log.info("Normal Request cost {} seconds", passedTime);
        }
        return pageResult;

    }

    @Override
    public PageResult<EsBean> searchx(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        SearchRequest searchRequest = SearchRequest.of(b -> b
                .trackTotalHits(t -> t.enabled(true))
                .index(EsUtil.getIndexes(esSearchBO))
                .query(builder.build()._toQuery())
                .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                .size(esSearchBO.getPageSize())
                .sort(sortOptions)
        );

        try {
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                results.add(hit.source());
            }
            pageResult.setRecords(results);
            pageResult.setTotal(response.hits().total().value());
        } catch (IOException e) {
            log.error("common/info/searchx 通用方法查询出错");
        }
        pageResult.setCurrent(esSearchBO.getPageNum());
        pageResult.setSize(esSearchBO.getPageSize());
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > INTERFACE_TIME_OUT) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        } else {
            log.info("Normal Request cost {} seconds", passedTime);
        }
        return pageResult;
    }

    @Override
    public Map<String, Long> mediaTypeCountForOriginal(EsSearchBO esSearchBO) {
        long start = System.currentTimeMillis();
        Map<String, Long> data = new HashMap<>(50);
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        if ((null != esSearchBO.getProjectType() && ProjectFlagEnum.QUANTUM.getValue() == esSearchBO.getProjectType()) && "1".equals(esSearchBO.getInfoFlag()) && "0".equals(esSearchBO.getInfoTag())) {
            //量子逻辑：如果是查入库的infolib 并且infotag是要查未入库的
            builder.mustNot(s -> s.term(t -> t.field("infoTag").value(1)));
        }
        if ((esSearchBO.getIsOriginal() == null || !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
            SearchResponse<Integer> response = null;
            String aggNameKey = "typeAgg";
            SearchRequest request = SearchRequest.of(b -> b.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                    .query(builder.build()._toQuery())
                    .aggregations(aggNameKey, a -> a.terms(t -> t.field(esSearchBO.getAggName()).size(50))
                    )
                    .size(0)
            );
            try {
                long end = System.currentTimeMillis();
                double passedTime = (end - start) / 1000.0;
                if (passedTime > 5) {
                    log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), request, passedTime);
                }
                response = esClient.search(request, Integer.class);
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(aggNameKey);
                List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
                for (LongTermsBucket bucket : array) {
                    data.put(String.valueOf(bucket.key()), bucket.docCount());
                }
            } catch (IOException e) {
                log.error("common/info/search 通用查询方法");
            }
        } else {
            final String countAgg = "countAgg";
            final String md5Agg = "md5Agg";
            SearchRequest searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(EsUtil.getIndexes(esSearchBO))
                    .query(builder.build()._toQuery())
                    .size(0)
                    .aggregations(countAgg, a -> a.terms(t -> t.field(esSearchBO.getAggName()).size(50))
                            .aggregations(md5Agg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())))));
            try {
                long end = System.currentTimeMillis();
                double passedTime = (end - start) / 1000.0;
                if (passedTime > 5) {
                    log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
                }
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                if (response != null) {
                    Map<String, Aggregate> aggregations = response.aggregations();
                    Aggregate aggregate1 = aggregations.get(countAgg);
                    List<LongTermsBucket> array = ((LongTermsAggregate) aggregate1._get()).buckets().array();
                    for (LongTermsBucket bucket : array) {
                        Aggregate aggregate = bucket.aggregations().get(md5Agg);
                        data.put(String.valueOf(bucket.key()), ((CardinalityAggregate) aggregate._get()).value());
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("common/info/search 通用查询方法");
            }
        }
        return data;
    }

    @Override
    public Integer realTimeInfoCount(EsSearchBO esSearchBO) {
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        List<String> indexes = EsUtil.getIndexes(esSearchBO);
        // 执行查询
        if ((esSearchBO.getIsOriginal() == null || !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
            CountRequest request = CountRequest.of(b -> b
                    .index(indexes)
                    .query(builder.build()._toQuery())
            );
            try {
                CountResponse response = esClient.count(request);
                return Math.toIntExact(response.count());
            } catch (IOException e) {
                log.error("common/info/realTimeInfoCount 通用查询方法");
            }
        } else {
            final String countAgg = "countAgg";
            SearchRequest searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(indexes)
                    .query(builder.build()._toQuery())
                    .size(0)
                    .aggregations(countAgg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName()))));
            try {
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                if (response != null) {
                    Map<String, Aggregate> aggregations = response.aggregations();
                    Aggregate aggregate = aggregations.get(countAgg);
                    return Math.toIntExact(((CardinalityAggregate) aggregate._get()).value());
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("common/info/realTimeInfoCount 通用查询方法");
            }
        }
        return 0;
    }

    /**
     * 根据id及关键词高亮查询
     *
     * @param id       id
     * @param keywords 关键词
     * @return
     */
    @Override
    public EsBean searchAndHighlightById(String id, String keywords) {
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.ID.getFieldName()).value(id)));
        if (CharSequenceUtil.isNotBlank(keywords)) {
            QueryStringQuery.Builder keyWordQb1 = buildKeyWord(quotedEveryKeyWord(keywords, false), SearchPositionEnum.ALL.name());
            bool.must(keyWordQb1.build()._toQuery());
        }

        try {
            SearchRequest searchRequest = SearchRequest.of(s -> s.index(EsUtil.getIndexes(null, null))
                    .query(bool.build()._toQuery())
                    .highlight(EsUtil.getHighlight(-1))
                    .size(1)
            );
            log.warn("项目:{}", searchRequest);
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            getBeansFromHighlightMap(results, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return results.size() > 0 ? results.get(0) : searchById(id);
    }

    /**
     * 根据id及关键词高亮查询
     *
     * @param id       id
     * @param keywords 关键词
     * @return
     */
    @Override
    public EsBean searchAndHighlightByIdX(String id, String keywords, Boolean accurate, String startTime, String endTime) {
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.ID.getFieldName()).value(id)));
        if (CharSequenceUtil.isNotBlank(keywords)) {
            QueryStringQuery.Builder keyWordQb1 = buildKeyWord(quotedEveryKeyWord(keywords, accurate != null && accurate), SearchPositionEnum.ALL.name());
            bool.must(keyWordQb1.build()._toQuery());
        }
        List<String> indexes = EsUtil.getIndexes(startTime, endTime);
        System.out.println("indexes = " + indexes);
        try {
            SearchRequest searchRequest = SearchRequest.of(s -> s.index(indexes)
                    .query(bool.build()._toQuery())
                    .highlight(EsUtil.getHighlight(-1))
                    .size(1)
            );
            log.warn("项目:{}", searchRequest);
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            getBeansFromHighlightMap(results, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return results.size() > 0 ? results.get(0) : searchById(id);
    }

    @Override
    public JSONObject searchAndHighlightByUrl(String url) {
        // 执行查询
        JSONObject jsonObject = new JSONObject();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.URL.getFieldName()).value(url)));
        // 执行查询
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(null, null))
                            .query(bool.build()._toQuery())
                            .size(1), EsBean.class);
            if (CollUtil.isNotEmpty(response.hits().hits())) {
                EsBean bean = response.hits().hits().get(0).source();
                if (bean != null) {
                    jsonObject.put("title", bean.getTitle());
                    jsonObject.put("type", String.valueOf(bean.getType()));
                    String host = bean.getHost();
                    if (StringUtils.isNotEmpty(jsonObject.getString("type"))) {
                        if ("m.toutiao.com".equals(host) || "www.toutiao.com".equals(host) || "toutiao.com".equals(host)) {
                            jsonObject.put("type", "61");
                        } else if ("www.douyin.com".equals(host)) {
                            jsonObject.put("type", "111");
                        } else if ("www.kuaishou.com".equals(host)) {
                            jsonObject.put("type", "112");
                        }
                    }
                    jsonObject.put("time", DateUtil.format(bean.getPublishTime(), BasicConstant.DATE_FORMAT_STR));
                }
            }

        } catch (IOException e) {
            log.error("searchByUrl查询有问题");
        }
        return jsonObject;
    }

    /**
     * 查询数据条数
     *
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @Override
    public Long getInfoCount(EsSearchBO esSearchBO) {
        Long count = 0L;
        // 创建查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        CountRequest countRequest = CountRequest.of(c -> c.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()));
        // 执行查询
        try {
            CountResponse countResponse = esClient.count(countRequest);
            count = countResponse.count();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("查询数量失败");
        }
        return count;
    }

    @Override
    public Integer getSimilarCount(EsSearchBO esSearchBO) {
        String md5 = esSearchBO.getMd5();
        String startTime = esSearchBO.getStartTime();
        String endTime = esSearchBO.getEndTime();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.MD5.getFieldName()).value(md5)));
        // 执行查询
        try {
            CountRequest countRequest = CountRequest.of(c -> c.index(EsUtil.getIndexes(esSearchBO)).query(bool.build()._toQuery()));
            CountResponse response = esClient.count(countRequest);
            return Math.toIntExact(response.count());
        } catch (IOException e) {
            log.error("searchByMd5查询有问题");
        }
        return 0;
    }

    @SneakyThrows
    @Override
    public Map<String, Map<String, Long>> getTypeCount(EsSearchBO esSearchBO) throws IOException {
        boolean flag = getTimeType(esSearchBO.getStartTime(), esSearchBO.getEndTime());
        CalendarInterval timeType = CalendarInterval.Day;
        String timeStr = "yyyy-MM-dd";
//        System.out.println(flag);
        if (flag) {
            timeType = CalendarInterval.Hour;
            timeStr = "yyyy-MM-dd HH";
        }
        Map<String, Map<String, Long>> mapss = new LinkedHashMap<>();
        // 执行查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        CalendarInterval finalTimeType = timeType;
        String finalTimeStr = timeStr;
        SearchResponse<Integer> response = esClient.search(
                s -> s.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()).size(0).aggregations(
                        "typeBasedCounts", a -> a.terms(t -> t.field("type").include(c -> c.terms(Arrays.asList("1", "3", "5"))))
                                .aggregations("dailyCounts", k -> k.dateHistogram(f -> f.field("publishTime").calendarInterval(finalTimeType).format(finalTimeStr)))
                ), Integer.class);
        if (response != null) {
            List<LongTermsBucket> array = ((LongTermsAggregate) response.aggregations().get("typeBasedCounts")._get()).buckets().array();
            for (LongTermsBucket stringTermsBucket : array) {
                Map<String, Long> value = new LinkedHashMap<>();
                long accountName = stringTermsBucket.key();
                Aggregate accountAggregations = stringTermsBucket.aggregations().get("dailyCounts");
                List<DateHistogramBucket> linkTypeAggs = ((DateHistogramAggregate) accountAggregations._get()).buckets().array();
                for (DateHistogramBucket typeAgg : linkTypeAggs) {
                    value.put(typeAgg.keyAsString(), typeAgg.docCount());
                }
                mapss.put(String.valueOf(accountName), value);
            }
        }
        return mapss;
    }

    @Override
    public Map<String, Long> getTotalCount(EsSearchBO esSearchBO) throws Exception {
        boolean flag = getTimeType(esSearchBO.getStartTime(), esSearchBO.getEndTime());
        CalendarInterval timeType = CalendarInterval.Day;
        String timeStr = "yyyy-MM-dd";
//        System.out.println(flag);
        if (flag) {
            timeType = CalendarInterval.Hour;
            timeStr = "yyyy-MM-dd HH";
        }
        // 执行查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        CalendarInterval finalTimeType = timeType;
        String finalTimeStr = timeStr;
        SearchResponse<Integer> response = esClient.search(
                s -> s.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()).size(0).
                        aggregations("dailyCounts", k -> k.dateHistogram(f -> f.field("publishTime").calendarInterval(finalTimeType).format(finalTimeStr))
                        ), Integer.class);
        Map<String, Long> value = new LinkedHashMap<>();
        if (response != null) {
            Aggregate accountAggregations = response.aggregations().get("dailyCounts");
            List<DateHistogramBucket> linkTypeAggs = ((DateHistogramAggregate) accountAggregations._get()).buckets().array();
            for (DateHistogramBucket typeAgg : linkTypeAggs) {
                value.put(typeAgg.keyAsString(), typeAgg.docCount());
            }
        }
        return value;
    }

    /**
     * 判断时间范围在不在一天内，如果在一天内就返回true，接口取一天内的每个小时数
     *
     * @param startTime
     * @param endTime
     * @return boolean
     * <AUTHOR>
     * @date 2024/6/3 13:37
     **/
    public static boolean getTimeType(String startTime, String endTime) throws Exception {
        boolean flag = false;
        if (StrUtil.isBlankIfStr(startTime) || StrUtil.isBlankIfStr(endTime)) {
            throw new Exception("时间不能为空");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);
        //long secondsBetween = ChronoUnit.SECONDS.between(startDateTime, endDateTime);
//        long minutesBetween = ChronoUnit.MINUTES.between(startDateTime, endDateTime);
//        if (minutesBetween <= 400) {
//            List<TimeRoundFlowVO> timeRoundFlowMinute = TimeUtil.timeRange(startTime, endTime, DateField.MINUTE, 1, 0);
////            Map.Entry<String, List<TimeRoundFlowVO>> entry = MapUtil.entry("yyyy-MM-dd HH:mm", timeRoundFlowMinute);
////            System.out.println(entry);
//        }
        long hoursBetween = ChronoUnit.HOURS.between(startDateTime, endDateTime);
        if (hoursBetween <= 24) {
            flag = true;
        }
        return flag;
    }

    public static void main(String[] args) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse("2024-06-02 09:24:27", formatter);
        LocalDateTime endDateTime = LocalDateTime.parse("2024-06-03 10:24:27", formatter);

        long hoursBetween = ChronoUnit.HOURS.between(startDateTime, endDateTime);
        if (hoursBetween <= 24) {
            System.out.println(hoursBetween);
        }
        System.out.println("=---" + hoursBetween);
    }

    @SneakyThrows
    @Override
    public Map<String, Map<String, Long>> getDouyinTotal(EsSearchBO esSearchBO) throws IOException {
        boolean flag = getTimeType(esSearchBO.getStartTime(), esSearchBO.getEndTime());
        CalendarInterval timeType = CalendarInterval.Day;
        String timeStr = "yyyy-MM-dd";
//        System.out.println(flag);
        if (flag) {
            timeType = CalendarInterval.Hour;
            timeStr = "yyyy-MM-dd HH";
        }
        Map<String, Map<String, Long>> mapss = new LinkedHashMap<>();
        // 执行查询
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        CalendarInterval finalTimeType = timeType;
        String finalTimeStr = timeStr;
        SearchResponse<Integer> response = esClient.search(
                s -> s.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()).size(0).aggregations(
                        "typeBasedCounts", a -> a.terms(t -> t.field("host").include(c -> c.terms(Arrays.asList("www.iesdouyin.com", "www.kuaishou.com", "www.toutiao.com"))))
                                .aggregations("dailyCounts", k -> k.dateHistogram(f -> f.field("publishTime").calendarInterval(finalTimeType).format(finalTimeStr)))
                ), Integer.class);
        if (response != null) {
            List<StringTermsBucket> array = ((StringTermsAggregate) response.aggregations().get("typeBasedCounts")._get()).buckets().array();
            for (StringTermsBucket stringTermsBucket : array) {
                Map<String, Long> value = new LinkedHashMap<>();
                String accountName = stringTermsBucket.key().stringValue();
                Aggregate accountAggregations = stringTermsBucket.aggregations().get("dailyCounts");
                List<DateHistogramBucket> linkTypeAggs = ((DateHistogramAggregate) accountAggregations._get()).buckets().array();
                for (DateHistogramBucket typeAgg : linkTypeAggs) {
                    value.put(typeAgg.keyAsString(), typeAgg.docCount());
                }
                mapss.put(accountName, value);
            }
        }
        return mapss;
    }

    @Override
    public Map<String, Long> getTypeTotalCount(EsSearchBO esSearchBO) throws IOException {
        long start = System.currentTimeMillis();
        Map<String, Long> mapss = new LinkedHashMap<>();
        String aggName = esSearchBO.getAggName();
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);

        builder.mustNot(Collections.singletonList(QueryBuilders.term(s -> s.field(aggName).value(""))));//排除分组字段值为空字符串的
        SearchRequest searchRequest = SearchRequest.of(s -> s.index(EsUtil.getIndexes(esSearchBO)).query(builder.build()._toQuery()).size(0)
                .aggregations(aggName + "Count", a -> a.terms(t -> t.field(aggName)/*.order(Collections.singletonList(NamedValue.of("_count", SortOrder.Desc)))*/.size(esSearchBO.getAggSize()))
                ));
        SearchResponse<Long> response = esClient.search(searchRequest, Long.class);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > 5) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        }
        if (response == null) {
            return mapss;
        }
        Aggregate aggregate = response.aggregations().get(aggName + "Count");
        if (aggregate._get() instanceof StringTermsAggregate) {
            List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
            for (StringTermsBucket bucket : array) {
                mapss.put(bucket.key().stringValue(), bucket.docCount());
            }
        } else if (aggregate._get() instanceof LongTermsAggregate) {
            List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
            for (LongTermsBucket bucket : array) {
                mapss.put(String.valueOf(bucket.key()), bucket.docCount());
            }
        }
        return mapss;
    }

    @Override
    public Long searchLatestThreeHoursInfoCount() {
        EsSearchBO esSearchBO = new EsSearchBO();
        Date now = new Date();
        String endTime = DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);
        String startTime = DateUtil.format(DateUtil.offsetHour(now, -3), DatePattern.NORM_DATETIME_PATTERN);
        esSearchBO.setStartTime(startTime);
        esSearchBO.setEndTime(endTime);
        return getInfoCount(esSearchBO);
    }

    public static void getBeansFromHighlightMap(List<EsBean> esBeanList, SearchResponse<EsBean> response) {
        if (response != null) {
            for (Hit<EsBean> hit : response.hits().hits()) {
                EsBean bean = hit.source();
                Map<String, List<String>> highlightMap = hit.highlight();
                if (highlightMap.containsKey(EsBeanFieldEnum.TITLE.getFieldName())) {
                    if (CollUtil.isNotEmpty(highlightMap.get(EsBeanFieldEnum.TITLE.getFieldName()))) {
                        bean.setTitle(highlightMap.get(EsBeanFieldEnum.TITLE.getFieldName()).get(0));
                    }
                }
                if (highlightMap.containsKey(EsBeanFieldEnum.TEXT.getFieldName())) {
                    if (CollUtil.isNotEmpty(highlightMap.get(EsBeanFieldEnum.TEXT.getFieldName()))) {
                        bean.setText(highlightMap.get(EsBeanFieldEnum.TEXT.getFieldName()).get(0));
                    }
                }
                esBeanList.add(bean);
            }
        }
    }

    /**
     * 高亮并且限制截取长度
     *
     * @param esBeanList
     * @param response
     * @param subLength
     * @return void
     * <AUTHOR>
     * @date 2024/5/9 16:30
     **/
    public static void getBeansFromHighlight(List<EsBean> esBeanList, SearchResponse<EsBean> response, Integer subLength) {
        if (response != null) {
            for (Hit<EsBean> hit : response.hits().hits()) {
                EsBean bean = hit.source();
                if (null == bean) {
                    continue;
                }
                Map<String, List<String>> highlightMap = hit.highlight();
                if (highlightMap.containsKey(EsBeanFieldEnum.TITLE.getFieldName())) {
                    if (CollUtil.isNotEmpty(highlightMap.get(EsBeanFieldEnum.TITLE.getFieldName()))) {
                        String title = (title = highlightMap.get(EsBeanFieldEnum.TITLE.getFieldName()).get(0)) == null ? "" : title;
                        if (title.length() > subLength) {
                            bean.setTitle(title.substring(0, subLength - 1));
                        } else {
                            bean.setTitle(title);
                        }
                    }
                }
                if (highlightMap.containsKey(EsBeanFieldEnum.TEXT.getFieldName())) {
                    if (CollUtil.isNotEmpty(highlightMap.get(EsBeanFieldEnum.TEXT.getFieldName()))) {
                        String text = highlightMap.get(EsBeanFieldEnum.TEXT.getFieldName()).get(0);
                        if (text.length() > subLength) {
                            bean.setText(text.substring(0, subLength - 1));
                        } else {
                            bean.setText(text);
                        }

                    }
                }
                esBeanList.add(bean);
            }
        }
    }


    public PageResult<EsBean> queryScrollAllData(EsSearchBO esSearchBO) throws IOException {
        PageResult<EsBean> datas = new PageResult<>();
        long start1 = System.currentTimeMillis();
        if (CollUtil.isEmpty(esSearchBO.getIndexs())) {
            throw new RuntimeException("es索引参数异常");
        }
        EsSearchBO searchBo = new EsSearchBO();
        searchBo.setIndexs(esSearchBO.getIndexs());
        BoolQuery.Builder builder = EsUtil.buildQuery(esSearchBO);
        SearchRequest searchRequest = SearchRequest.of(
                s -> s.index(EsUtil.getIndexes(searchBo)).query(builder.build()._toQuery()).size(3000).scroll(t -> t.time("60s"))
        );
        SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
        long end = System.currentTimeMillis();
        double passedTime = (end - start1) / 1000.0;
        if (passedTime > 5) {
            log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()), searchRequest, passedTime);
        }
        TotalHits total = response.hits().total();
        log.info("滑动第一次查询总数{}条:", Objects.requireNonNull(total).value());
        List<EsBean> dataToMove = response.hits().hits().stream().map(Hit::source).collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        long count = dataToMove.size();
        int page = 1;
//        List<CompletableFuture<BulkResponse>> futures = new ArrayList<>();
        String scrollId = response.scrollId();
//        log.info("初始化:{}", scrollId);
        List<Hit<EsBean>> hits;
        List<EsBean> ScollList = new CopyOnWriteArrayList<>(dataToMove);
        do {
            String finalScrollId = scrollId;
            ScrollResponse<EsBean> scrollResp = esClient.scroll(s -> s.scrollId(finalScrollId).scroll(t -> t.time("60s")), EsBean.class);
            scrollId = scrollResp.scrollId();
//            log.info("后续:{}", scrollId);
            hits = scrollResp.hits().hits();
            boolean flag = false;
            for (Hit<EsBean> hit : hits) {
                EsBean esFileVO = hit.source();
                if (esFileVO != null) {
                    ScollList.add(esFileVO);
                    flag = true;
                }
            }
            if (flag) {
                count += ScollList.size();
                page += 1;
            }

        } while (!hits.isEmpty());
        // 清理滚动上下文
        String finalScrollIdX = scrollId;
        esClient.clearScroll(s -> s.scrollId(finalScrollIdX));
        log.info("查询总数:{},分页一共:{}次", count, page);
        datas.setTotal(count);
        datas.setRecords(ScollList);
        return datas;
    }

    @Override
    public EsBean searchByUrl(EsSearchBO bo) {
        // 执行查询
        BoolQuery.Builder bool = QueryBuilders.bool();
        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.URL.getFieldName()).value(bo.getUrl())));
        // 执行查询
        SearchResponse<EsBean> response = null;
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(bo.getStartTime(), bo.getEndTime()))
                            .query(bool.build()._toQuery())
                            .size(1), EsBean.class);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("searchByUrl出错");
        }
        if (CollUtil.isNotEmpty(response.hits().hits())) {
            EsBean bean = response.hits().hits().get(0).source();
            if (bean != null) {
                return bean;
            }
        }
        return null;

    }

    @Override
    public boolean updateById(EsUpdateBO esUpdateBO) {
        boolean flag = false;
        UpdateByQueryRequest.Builder builder = new UpdateByQueryRequest.Builder();
        //设置索引
        builder.index(EsUtil.getIndexes(esUpdateBO.getStartTime(), esUpdateBO.getEndTime()));
        //设置查询
        builder.query(QueryBuilders.term(t -> t.field("id").value(esUpdateBO.getId())));
        // 设置要更新的脚本
        Script.Builder scriptBuilder = new Script.Builder();
        scriptBuilder.inline(t -> t.source("ctx._source." + esUpdateBO.getFieldName() + "=" + esUpdateBO.getFieldValue()));
        Script script = scriptBuilder.build();
        builder.script(script);
        //构建请求
        UpdateByQueryRequest queryRequest = builder.build();
        try {
            UpdateByQueryResponse response = esClient.updateByQuery(queryRequest);
            Long updated = response.updated();
            if (updated != null && updated >= 1) {
                flag = true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return flag;
    }

}
