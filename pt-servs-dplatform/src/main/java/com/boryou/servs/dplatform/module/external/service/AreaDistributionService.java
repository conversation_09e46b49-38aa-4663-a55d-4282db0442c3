package com.boryou.servs.dplatform.module.external.service;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.IndexResultVo;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-26 14:50
 */
public interface AreaDistributionService {
    /**
     * 获取区域分布
     *
     * @param statisticsBo
     * @return java.util.Map<java.lang.String, java.lang.Integer>
     * <AUTHOR>
     * @date 2024/4/26 14:54
     **/
    Map<String, Long> getAreaDistribution(StatisticsBean statisticsBo, String aggName);

    /**
     * 通用搜索条件设置
     *
     * @param statisticsBo
     * @return co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder
     * <AUTHOR>
     * @date 2024/4/28 17:22
     **/
    BoolQuery.Builder setCommonSearch(StatisticsBean statisticsBo, String... type);

    IndexResultVo propagationPath(StatisticsBean statisticsBean);

    Map<String, Long> getAreaCodeDistribution(StatisticsBean statisBean, String contentAreaCode, List<String> areaCodes);

    @NotNull
    Map<String, Long> getAggCountMap(String aggName, BoolQuery.Builder bool, Integer aggSize,StatisticsBean statisticsBean);

    String getPropagationStartTime(StatisticsBean analysisSubject);

    PageInfo<IndexResultBean> getPropagationListData(StatisticsBean analysisSubject);

    Map<String, Long> getScreenAreaCount(StatisticsBean statisticsBean, String aggName, List<String> areaCodes);

    PageInfo<IndexResultBean> getQueryPageInfoList(StatisticsBean analysisSubject, BoolQuery.Builder bool);

    PageInfo<IndexResultBean> getAreaAuthorInfo(StatisticsBean statisBean, String aggName, List<String> areaCodes);

    PageInfo<IndexResultBean> searchByKeyWords(StatisticsBean analysisSubject);
}

