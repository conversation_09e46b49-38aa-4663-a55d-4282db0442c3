
package com.boryou.servs.dplatform.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * 检索模式 0:标题+正文  1:标题  2:正文*
 */

@Getter
@RequiredArgsConstructor
public enum SearchIssueEnum {
	//默认值 按全部处理
	DEFAULT(-1,"默认值"),
	//正文+标题
	SEARCH_POSITION_ALL(0,"全部"),
	//标题
	SEARCH_POSITION_TITLE(1,"标题"),
	//正文
	SEARCH_POSITION_TEXT(2,"正文"),
	//普通模式
	SEARCH_MODE_NORMAL(0,"普通模式"),
	//高级模式
	SEARCH_MODE_PROFESSION(1,"高级模式");

	/**
	 * 类型
	 */
	private final Integer type;

	/**
	 * 描述
	 */
	private final String description;


	/**
	 * 描述
	/**
	 * 提前判断，用于解决Case中出现的Constant expression required问题
	 * @param type
	 * @return
	 */
	public static SearchIssueEnum getByType(Integer type){
		for(SearchIssueEnum x:values()){
			if(Objects.equals(x.getType(), type)){
				return x;
			}
		}
		return DEFAULT;
	}
}
