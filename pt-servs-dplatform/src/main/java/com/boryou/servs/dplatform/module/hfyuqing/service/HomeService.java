package com.boryou.servs.dplatform.module.hfyuqing.service;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.EsAnalyseService;
import com.boryou.servs.dplatform.module.hfyuqing.dao.ElasticsearchDao;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.EsBeanVO;
import com.boryou.servs.dplatform.module.hfyuqing.pojo.HomeDataVO;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class HomeService {

    private final ElasticsearchDao elasticsearchDao;
    private final ConvertService convertService;

    public PageResult<EsBeanVO> homeData(HomeDataVO homeDataVO) {
        convertService.homeDataVO(homeDataVO);
        return elasticsearchDao.homeData(homeDataVO);
    }
    public Map<String, Long> homeEmotion(HomeDataVO homeDataVO) {
        convertService.homeDataVO(homeDataVO);
        return elasticsearchDao.homeEmotion(homeDataVO);
    }
}