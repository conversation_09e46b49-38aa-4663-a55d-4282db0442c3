package com.boryou.servs.dplatform.module.quantum.bo;

import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-29 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InfoLibBO extends EsBean {

    /**
     * 检索方案的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docIndexId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long infoId;
    /**
     * 媒体类型
     */
    private String types;


    /**
     * 检索开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 检索结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    /**
     * 项目接口调用标识
     */
    private Integer projectType;

    private List<String> ids;

    /**
     * 分类标签
     */
    private List<String> targetTag;
    private List<String> tagIds;
    /**
     * 方案id
     */
    private List<String> planIds;

    private boolean originalFlag;

    /**
     * 搜索词
     */
    private String keyWord;

    private String summary;
    /**
     * 0是元数据打标  1是方案打标过来的  （免检数据是0，因为是从三大过来的）
     */
    private Integer markSource;
    /**
     * 0是打标  1是发布
     */
    private Integer addType;

    /**
     * 打标时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
}

