package com.boryou.servs.dplatform.util;

import co.elastic.clients.elasticsearch._types.SortOrder;

/**
 * <AUTHOR>
 * @date 2024-05-08 17:44
 */
public class CommonUtil {
    /**
     * 根据字符串asc,des来获取ES的排序类型
     *
     * @param sortType 字符串asc,desc
     * @return co.elastic.clients.elasticsearch._types.SortOrder
     * <AUTHOR>
     * @date 2024/5/8 17:45
     **/
    public static SortOrder getSort(String sortType) {
        if (sortType.equalsIgnoreCase(SortOrder.Asc.jsonValue())) {
            return SortOrder.Asc;
        } else {
            return SortOrder.Desc;
        }
    }
}

