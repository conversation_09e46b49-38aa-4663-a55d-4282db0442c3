package com.boryou.servs.dplatform.module.external.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.ProjectFlagEnum;
import com.boryou.servs.dplatform.enums.SortTypeEnum;
import com.boryou.servs.dplatform.module.external.bo.WrapperRequest;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class WrapperService {
    private final ElasticsearchClient esClient;

    public PageResult<EsBean> wrapperSearch(WrapperRequest esSearchBO) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = new PageResult<>();
        // 创建查询
        Query query1 = QueryBuilders.wrapper(w->w.query(esSearchBO.getWrapperQuery()));
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        // 执行查询
        List<EsBean> results = new ArrayList<>();
        SearchRequest searchRequest;
        if ((esSearchBO.getIsOriginal() == null ||  !esSearchBO.getIsOriginal()) && !SortTypeEnum.SIMILAR_DESC.getVal().equals(esSearchBO.getSortType())) {
            searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(EsUtil.getIndexes(esSearchBO))
                    .query(query1)
                    .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                    .size(esSearchBO.getPageSize())
                    .sort(sortOptions)
            );
            try {
//                log.warn("项目:{},{}", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),searchRequest);
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                for (Hit<EsBean> hit : response.hits().hits()) {
                    results.add(hit.source());
                }
                pageResult.setRecords(results);
                pageResult.setTotal(response.hits().total().value());
            } catch (IOException e) {
                e.printStackTrace();
                log.error("WrapperService.wrapperSearch 异常查询语句:{}",searchRequest);
            }
        } else {
            //以下为内容去重，并不能实现微博原创去重，微博原创去重请调用searchx方法
            final String countAgg = "countAgg";
            final String md5Agg = "md5Agg";
            final String topAgg = "topAgg";
            final String sortTimeAgg = "sortTimeAgg";
            NamedValue<SortOrder> namedValue = EsUtil.getBucketSort(esSearchBO.getSortType(), sortTimeAgg);
            Aggregation aggSort ;
            if (SortTypeEnum.PAGE_VIEW.getVal().equals(esSearchBO.getSortType())) {
                aggSort = AggregationBuilders.max(t ->t.field(EsBeanFieldEnum.PAGE_VIEW.getFieldName()));
            }else{
                //取时间最早的一条
                aggSort = AggregationBuilders.min(t -> t.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()));
            }
            searchRequest = SearchRequest.of(b -> b
                    .trackTotalHits(t -> t.enabled(true))
                    .index(EsUtil.getIndexes(esSearchBO))
                    .query(query1)
                    .size(0)
                    .aggregations(countAgg, c -> c.cardinality(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())))
                    .aggregations(md5Agg, a -> a.terms(t -> t.field(EsBeanFieldEnum.MD5.getFieldName())
                                    .size(esSearchBO.getPageNum() != 0 && esSearchBO.getPageSize() != 0 ? esSearchBO.getPageNum() * esSearchBO.getPageSize() : 1)
                                    .order(namedValue))
                            .aggregations(topAgg, top -> top.topHits(t -> t.size(1).sort(sortOptions)))
                            .aggregations( sortTimeAgg , aggSort)));
            try {
                SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
                if (response != null) {
                    Map<String, Aggregate> aggregations = response.aggregations();
                    Aggregate aggregate1 = aggregations.get(md5Agg);
                    List<StringTermsBucket> array1 = ((StringTermsAggregate) aggregate1._get()).buckets().array();
                    EsBean bean;
                    for (StringTermsBucket bucket : array1) {
                        HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                        for (Hit<JsonData> hit : hits.hits()) {
                            if (hit.source() != null) {
                                bean = hit.source().to(EsBean.class);
                                bean.setSimilarCount((int) hits.total().value());
                                results.add(bean);
                            }
                        }
                    }
                    results = results.subList((esSearchBO.getPageNum() -1)*esSearchBO.getPageSize(), results.size());
                    pageResult.setRecords(results);
                    Aggregate aggregate = aggregations.get(countAgg);
                    pageResult.setTotal(((CardinalityAggregate) aggregate._get()).value());
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("WrapperService.wrapperSearch 异常查询语句:{}",searchRequest);
            }
        }
        pageResult.setCurrent(esSearchBO.getPageNum());
        pageResult.setSize(esSearchBO.getPageSize());
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        log.warn("项目:{},{},查询耗时{}秒", ProjectFlagEnum.getDesc(esSearchBO.getProjectType()),searchRequest,passedTime);

        return pageResult;
    }

}
