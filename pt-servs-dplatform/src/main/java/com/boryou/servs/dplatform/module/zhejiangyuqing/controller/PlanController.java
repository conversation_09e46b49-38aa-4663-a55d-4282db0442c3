package com.boryou.servs.dplatform.module.zhejiangyuqing.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.PlanEsSearchBO;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.PlanService;
import com.boryou.servs.dplatform.pojo.R;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 浙江高院的es历史节点处理器
 *
 * <AUTHOR>
 * @date 2024-05-24 17:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zhejiangyuqing")
public class PlanController {

    private final PlanService planService;

    /**
     * @param esSearchBO 搜索BO
     * @return java.util.List<com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO>
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("saveHistoryPlan")
    public R saveHistoryPlan(@RequestBody PlanEsSearchBO esSearchBO) {
        try {
            return R.check(planService.saveHistoryPlan(esSearchBO));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }

    }

    /**
     * 事件首发
     * @param esSearchBO
     * @return
     */
    @PostMapping("/firstRelease")
    public R firstRelease(@RequestBody EsSearchBO esSearchBO) {
        try {
            List<EsBean> flag = planService.firstRelease(esSearchBO);
            return R.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }

    /**
     * 事件首发
     * @param esSearchBO
     * @return
     */
    @PostMapping("/eventContext")
    public R eventContext(@RequestBody EsSearchBO esSearchBO) {
        try {
            PageResult<EsBean> flag = planService.eventContext(esSearchBO);
            return R.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }

    /**
     * 相关热文
     * @param esSearchBO
     * @return
     */
    @PostMapping("/relatedHotArticle")
    public R relatedHotArticle(@RequestBody EsSearchBO esSearchBO) {
        try {
            List<EsBean> flag = planService.relatedHotArticle(esSearchBO);
            return R.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }
}

