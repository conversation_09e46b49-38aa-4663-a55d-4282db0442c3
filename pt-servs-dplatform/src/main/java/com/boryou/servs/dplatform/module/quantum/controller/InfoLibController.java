package com.boryou.servs.dplatform.module.quantum.controller;

import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibBO;
import com.boryou.servs.dplatform.module.quantum.bo.InfoLibSearchBO;
import com.boryou.servs.dplatform.module.quantum.bo.SearchInfoLibBO;
import com.boryou.servs.dplatform.module.quantum.service.IInfoLibService;
import com.boryou.servs.dplatform.pojo.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * 浙江高院的es历史节点处理器
 *
 * <AUTHOR>
 * @date 2024-05-24 17:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/quantum/lib")
public class InfoLibController {

    private final IInfoLibService infoLibService;

    /**
     * @param esSearchBO 搜索BO
     * @return
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("list")
    public R list(@RequestBody SearchInfoLibBO esSearchBO) {
        try {
            return R.success(infoLibService.list(esSearchBO));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }

    }

    /**
     * @param infoLibBO 搜索BO
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("add")
    public R add(@RequestBody List<InfoLibBO> infoLibBO) {
        try {
            return R.success(infoLibService.add(infoLibBO));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }

    /**
     * @param infoLibBO 搜索BO
     * @return
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("edit")
    public R edit(@RequestBody List<InfoLibBO> infoLibBO) {
        try {
            return R.success(infoLibService.edit(infoLibBO));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }

    /**
     * @param infoLibBO 搜索BO
     * @return
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("delete")
    public R delete(@RequestBody List<InfoLibBO> infoLibBO) {
        try {
            return R.success(infoLibService.delete(infoLibBO));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }
    //从infolib删除，支持多个
    @PostMapping("/batchDelete")
    public R batchDelete(@RequestBody SearchInfoLibBO searchInfoLibBO) {
        try {
            return R.check(infoLibService.batchDelete(searchInfoLibBO));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 根据id集合查询infolib数据
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/getInfoLibListById")
    public R getInfoLibListById(@RequestBody InfoLibSearchBO infoLibSearchBO) {
        try {
            return R.success(infoLibService.getInfoLibListById(infoLibSearchBO));
        } catch (IOException e) {
            return R.error(e.getMessage());
        }
    }


    /**
     * 删除信息库数据，从infolib中删除
     * 
     * 更新es的infotag为0，回到元数据中  
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/updateESInfoTag")
    public R updateESInfoTag(@RequestBody InfoLibBO infoLibBO) {
        try {
            return R.success(infoLibService.libInfoClassUpdate(infoLibBO.getIds(),0, ESConstant.ALL_INDEX));
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }


    /**
     * 删除信息库的planids和tagids
     *
     * 更新es的infotag为0，回到元数据中  
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/removeInfoLibTagids")
    public R removeInfoLibTagids(@RequestBody InfoLibBO infoLibBO) {
        try {
            return R.success(infoLibService.removeInfoLibTagids(infoLibBO.getIds()));
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

}

