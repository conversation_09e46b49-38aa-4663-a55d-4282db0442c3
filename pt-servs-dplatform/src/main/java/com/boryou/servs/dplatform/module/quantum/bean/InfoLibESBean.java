package com.boryou.servs.dplatform.module.quantum.bean;

import com.boryou.servs.dplatform.module.zhejiangyuqing.bo.PlanEsBean;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-30 13:30
 */
@Data
public class InfoLibESBean extends PlanEsBean {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long infoId;

    private List<String>planIds;
    private List<String>tagIds;
    /**
     * 0是打标  1是发布
     */
    private String addType;

    /**
     * 0是元数据打标  1是方案打标过来的  （免检数据是0，因为是从三大过来的）
     */
    private Integer markSource;
    /**
     * 打标时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
}

