package com.boryou.servs.dplatform.module.hfyuqing.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.dplatform.enums.MediaTypeEnum;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.module.hfyuqing.service.SubjectAnalyseService;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.*;

/**
 * <AUTHOR>
 * @date 2024-04-29 15:32
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SubjectAnalyseServiceImpl implements SubjectAnalyseService {
    private final AreaDistributionService areaDistributionService;
    private final ElasticsearchClient esClient;

    @Override
    public Map<String, Long> getHostData(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);
        return SpringUtil.getBean(AreaDistributionService.class).getAggCountMap("host", bool, 10, statisticsBean);
    }

    @Override
    public Map<String, Long> getTimeTrendMap(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);

        SearchResponse<Integer> response = null;
        String aggNameKey = "publishTime" + "Agg";
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(statisticsBean.getStartTime(), statisticsBean.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.dateHistogram(x -> x.field("publishTime").format("yyyy-MM-dd").calendarInterval(CalendarInterval.Day))
                                    .aggregations("type_buckets", b -> b.terms(x -> x.field("type")))
                            )
                            .size(0), Integer.class);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        List<DateHistogramBucket> array = ((DateHistogramAggregate) aggregate._get()).buckets().array();
        for (DateHistogramBucket bucket : array) {
            List<LongTermsBucket> typeBuckets = ((LongTermsAggregate) bucket.aggregations().get("type_buckets")._get()).buckets().array();
            typeBuckets.forEach(typeBucket -> {
                data.put(bucket.keyAsString() + "_" + typeBucket.key(), typeBucket.docCount());
            });

        }
        return data;
    }

    @Override
    public Map<String, Long> getMediaMapType(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);
        return SpringUtil.getBean(AreaDistributionService.class).getAggCountMap("type", bool, 10, statisticsBean);
    }

    @Override
    public Map<String, Long> getMediaPie(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);
        SearchResponse<Integer> response = null;
        String aggName = "siteMeta";
        String aggNameKey = aggName + "Agg";
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(statisticsBean.getStartTime(), statisticsBean.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.terms(t -> t.field(aggName)
                                    .size(20)
                                    .include(i -> i.terms(Arrays.asList(statisticsBean.getSiteMeta())))
                                    .order(new NamedValue<>("_count", SortOrder.Desc)))
                            )
                            .size(0), Integer.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, Long> data = new HashMap<>();
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);

        List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
        for (StringTermsBucket bucket : array) {
            data.put(bucket.key().stringValue(), bucket.docCount());
        }

        // 填充没有数据的标签
        for (String s : statisticsBean.getSiteMeta()) {
            if (!data.containsKey(s)) {
                data.put(s, 0L);
            }
        }

        return data;
    }

    @Override
    public Map<String, Map<String, Integer>> timeTrend(EsSearchBO bo) {
        BoolQuery.Builder bool = EsUtil.buildQuery(bo);
        Map<String, Map<String, Integer>> res = new LinkedHashMap<>();

        // 时间分段
        DateTime startTime = DateUtil.parse(bo.getStartTime(), PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN, NORM_DATETIME_PATTERN);
        DateTime endTime = DateUtil.parse(bo.getEndTime(), PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN, NORM_DATETIME_PATTERN);
        int count = bo.getAggSize();
        // 分割时间
        long between = DateUtil.between(startTime, endTime, DateUnit.SECOND, Boolean.FALSE);
        long interval = between / count * 1000;

        List<DateRangeExpression> range = new ArrayList<>(bo.getAggSize());
        // 今天
        for (int i = 0; i < count; i++) {
            int finalI = i;
            long from = startTime.getTime() + finalI * interval;
            long to = startTime.getTime() + (finalI + 1) * interval;
            String key = DateUtil.format(new Date(from), DatePattern.PURE_DATETIME_FORMAT) + "-" + DateUtil.format(new Date(to), DatePattern.PURE_DATETIME_FORMAT);
            range.add(new DateRangeExpression.Builder()
                    .from(builder -> builder.value((double) startTime.getTime() + finalI * interval))
                    .to(builder -> builder.value((double) startTime.getTime() + (finalI + 1) * interval))
                    .key(key).build());
            res.put(key, null);
        }

        SearchResponse<Integer> response = null;
        String aggNameKey = "publish_time" + "Agg";
        SearchRequest searchRequest = SearchRequest.of(s -> s.index(EsUtil.getIndexes(bo.getStartTime(), bo.getEndTime())).trackTotalHits(t -> t.enabled(true))
                .query(bool.build()._toQuery())
                .aggregations(aggNameKey, a -> a.dateRange(x -> x.field("publishTime").ranges(range))
                        .aggregations("type_buckets", b -> b.terms(x -> x.field("type")))
                )
                .size(0));
        try {
            response = esClient.search(searchRequest, Integer.class);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        List<RangeBucket> array = ((DateRangeAggregate) aggregate._get()).buckets().array();
        for (RangeBucket bucket : array) {
            List<LongTermsBucket> typeBuckets = ((LongTermsAggregate) bucket.aggregations().get("type_buckets")._get()).buckets().array();
            typeBuckets.forEach(typeBucket -> {
                data.put(bucket.key() + "_" + typeBucket.key(), typeBucket.docCount());
            });
        }

        if (StringUtils.isEmpty(bo.getType())) {
            bo.setType(MediaTypeEnum.getAllType());
        }
        String[] typeList = bo.getType().split(",");

        for (String key : res.keySet()) {
            Map<String, Integer> map = res.get(key);
            if (map == null) {
                map = new LinkedHashMap<>();
            }

            for (String type : typeList) {
                if (!data.containsKey(key + "_" + type)) {
                    map.put(type, 0);
                } else {
                    map.put(type, Math.toIntExact(data.get(key + "_" + type)));
                }
            }
            res.put(key, map);
        }
        return res;
    }

    @Override
    public Map<String, Integer[]> getTimeTrendMap24H(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);

        SearchResponse<Object> response = null;
        String aggNameKey = "publishTime" + "Agg";
        DateTime startTime = DateUtil.parse(statisticsBean.getStartTime(), PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN);
        DateTime endTime = DateUtil.parse(statisticsBean.getEndTime(), PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN);
        long l = DateUtil.betweenDay(startTime, endTime, false);
        // 时间间隔
        CalendarInterval calendarInterval;
        // 分组数量，后面统一会加1
        int aggNum = 23;
        if (l > 1) {
            calendarInterval = CalendarInterval.Day;
            aggNum = (int) l;
        } else {
            calendarInterval = CalendarInterval.Hour;
        }
        try {
            SearchRequest.Builder search = new SearchRequest.Builder();
            search.index(EsUtil.getIndexes(statisticsBean.getStartTime(), statisticsBean.getEndTime()))
                    .query(bool.build()._toQuery())
                    .size(0)
                    .aggregations(aggNameKey, a -> a.dateHistogram(x -> x.field("publishTime")
                                    .calendarInterval(calendarInterval))
                            .aggregations("type_buckets", b -> b.terms(x -> x.field("type"))));
            SearchRequest searchRequest = search.build();
            response = esClient.search(searchRequest, Object.class);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        List<DateHistogramBucket> array = ((DateHistogramAggregate) aggregate._get()).buckets().array();
        for (DateHistogramBucket bucket : array) {
            List<LongTermsBucket> typeBuckets = ((LongTermsAggregate) bucket.aggregations().get("type_buckets")._get()).buckets().array();
            typeBuckets.forEach(typeBucket -> {
                data.put(bucket.keyAsString() + "_" + typeBucket.key(), typeBucket.docCount());
            });
        }

        Map<String, Integer[]> res = new LinkedHashMap<>();
        for (MediaTypeEnum item : MediaTypeEnum.values()) {
            List<String> list = data.keySet().stream().filter(x -> x.endsWith("_" + item.getValue())).collect(Collectors.toList());
            if (list.isEmpty()) {
                Integer[] collect = new Integer[aggNum + 1];
                Arrays.fill(collect, 0);
                res.put(item.getDesc(), collect);
                continue;
            }
            int finalAggNum = aggNum;
            list.forEach(x -> {
                // 一天趋势
                if (finalAggNum == 23) {
                    Integer[] hourIndex = getHourIndex(statisticsBean.getStartTime(), res.get(item.getDesc()), x, data.get(x).intValue());
                    res.put(item.getDesc(), hourIndex);
                }
                // 一周趋势
                else {
                    if (res.get(item.getDesc()) == null) {
                        Integer[] collect = new Integer[finalAggNum + 1];
                        Arrays.fill(collect, 0);
                        res.put(item.getDesc(), collect);
                    }
                    Integer[] weekIndex = getWeekIndex(statisticsBean.getStartTime(), res.get(item.getDesc()), x, data.get(x).intValue());
                    res.put(item.getDesc(), weekIndex);
                }
            });
        }
        return res;
    }

    private Integer[] getWeekIndex(String startTime, Integer[] integers, String key, int value) {
        DateTime dateTime = DateUtil.parse(startTime, PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN);
        DateTime parse = DateUtil.parse(key.split(" ")[0], NORM_DATE_PATTERN);
        long l = DateUtil.betweenDay(dateTime, parse, true);
        integers[Math.toIntExact(l)] = value;
        return integers;
    }

    @Override
    public Integer[] getWeekCount(EsSearchBO bo) {
        BoolQuery.Builder bool = EsUtil.buildQuery(bo);
        SearchResponse<Integer> response = null;
        String aggNameKey = "dateAgg";
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(bo.getStartTime(), bo.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.dateHistogram(x -> x.field("publishTime").format("yyyy-MM-dd").calendarInterval(CalendarInterval.Day)))
                            .size(0), Integer.class);

            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate aggregate = aggregations.get(aggNameKey);
            List<DateHistogramBucket> array = ((DateHistogramAggregate) aggregate._get()).buckets().array();

            Map<String, Integer> res = new LinkedHashMap<>();
            DateTime startTime = DateUtil.parse(bo.getStartTime(), "yyyy-MM-dd HH:mm:ss");
            DateTime endTime = DateUtil.parse(bo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
            long l = DateUtil.betweenDay(startTime, endTime, false);
            for (long i = 1; i <= l; i++) {
                DateTime dateTime = DateUtil.offsetDay(endTime, (int) -(l - i));
                String key = DateUtil.format(dateTime, "yyyy-MM-dd");
                res.put(key, 0);
            }
            for (DateHistogramBucket bucket : array) {
                res.put(bucket.keyAsString(), (int) bucket.docCount());
            }
            return res.values().toArray(new Integer[0]);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new Integer[0];
    }

    private static Integer[] getHourIndex(String startTime, Integer[] collect, String key, Integer value) {
        DateTime start = DateUtil.parse(startTime, PURE_DATETIME_PATTERN, NORM_DATETIME_MINUTE_PATTERN);
        // 24小时填充0
        if (collect == null) {
            collect = new Integer[24];
            for (int i = 0; i < 24; i++) {
                collect[i] = 0;
            }
        }
        for (int i = 0; i < 24; i++) {
            String format = DateUtil.format(DateUtil.offsetHour(start, i), "yyyy-MM-dd HH");
            if (key.startsWith(format)) {
                collect[i] = value;
            }
        }
        return collect;
    }

}

