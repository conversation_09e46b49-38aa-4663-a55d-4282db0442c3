package com.boryou.servs.dplatform.module.warn.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class WarnDataVO {

    @NotBlank(message = "预警开始时间不能为空")
    private String warnDateStart;

    @NotBlank(message = "预警结束时间不能为空")
    private String warnDateEnd;

    private String emotionFlag;

    private String planId;

    private String userId;

    private Long deptId;

    private String type;

    private Integer readFlag;

    private String warnType;

    private String kw;

    private List<String> ids;
    private List<String> readIds;

    //预警查询重复
    private List<String> articleIds;
    private List<String> deptIds;
    private List<String> users;
    private List<String> md5s;

    /**
     * 预警推送类型 1: 短信 2: 邮件 3: 微信 4: 系统
     */
    private List<String> pushType;

    private Integer pageNum;
    private Integer pageSize;
}
