package com.boryou.servs.dplatform.module.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.ScrollResponse;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.util.NamedValue;
import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.dplatform.enums.*;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.IndexResultVo;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import com.boryou.servs.dplatform.pojo.bo.SortBO;
import com.boryou.servs.dplatform.util.CommonUtil;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-26 14:55
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AreaDistributionServiceImpl implements AreaDistributionService {

    private final ElasticsearchClient esClient;

    private void concatQueryStr(List<Query> queries, String kw, String searchPosition) {
        String[] split = kw.trim().split("\\s+");
        Query match;
        switch (SearchPositionEnum.getByValue(searchPosition)) {
            case TITLE:
                match = QueryBuilders.queryString(q -> q.fields("title").query(String.join(" ", split)).defaultOperator(Operator.Or));
                queries.add(match);
                break;
            case TEXT:
                match = QueryBuilders.queryString(q -> q.fields("text").query(String.join(" ", split)).defaultOperator(Operator.Or));
                queries.add(match);
                break;
            default:
                match = QueryBuilders.queryString(q -> q.fields(Arrays.asList("title", "text")).query(String.join(" ", split)).defaultOperator(Operator.Or));
                queries.add(match);
                break;
        }
    }


    @Override
    public Map<String, Long> getAreaDistribution(StatisticsBean statisticsBean, String aggName) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);
        return areaDistributionService.getAggCountMap(aggName, bool, statisticsBean.getAggSize(),statisticsBean);
    }

    @Override
    public @NotNull Map<String, Long> getAggCountMap(String aggName, BoolQuery.Builder bool, Integer aggSize,StatisticsBean statisticsBean) {
        SearchResponse<Integer> response = null;
        String aggNameKey = aggName + "Agg";
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes( statisticsBean.getStartTime(),  statisticsBean.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.terms(t -> t.field(aggName).size(aggSize).order(new NamedValue<>("_count", SortOrder.Desc)))
                            )
                            .size(0), Integer.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);

        if (aggregate._get() instanceof StringTermsAggregate) {
            List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
            for (StringTermsBucket bucket : array) {
                data.put(bucket.key().stringValue(), bucket.docCount());
            }
        } else if (aggregate._get() instanceof LongTermsAggregate) {
            List<LongTermsBucket> array = ((LongTermsAggregate) aggregate._get()).buckets().array();
            for (LongTermsBucket bucket : array) {
                data.put(String.valueOf(bucket.key()), bucket.docCount());
            }
        }
        return data;
    }

    @Override
    public String getPropagationStartTime(StatisticsBean analysisSubject) {
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(JSONUtil.toBean(JSONObject.toJSONString(analysisSubject), StatisticsBean.class), "2");
        SearchResponse<Object> response = null;
        try {
            List<SortOptions> sorts = new ArrayList<>();
            List<SortBO> sortField = analysisSubject.getSorts();
            if (CollectionUtil.isNotEmpty(sortField)) {
                for (SortBO sortBO : sortField) {
                    sorts.add(SortOptionsBuilders.field(f -> f.field(sortBO.getSortField()).order(CommonUtil.getSort(sortBO.getSortType()))));
                }
            } else {
                sorts.add(SortOptionsBuilders.field(f -> f.field("publishTime").order(SortOrder.Asc)));
            }
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(analysisSubject.getStartTime(), analysisSubject.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery()).
//                            sort(c -> c.field( f -> f.field("publishTime").order(SortOrder.Asc)))
        sort(sorts).
                            size(1), Object.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<Hit<Object>> hits = response.hits().hits();
        if (CollectionUtil.isNotEmpty(hits)) {
            return JSONObject.parseObject(JSONObject.toJSONString(hits.get(0).source())).getString("publishTime");
        }
        return null;
    }

    public static void main(String[] args) {
        SortOrder asc = SortOrder.valueOf("asc");
        System.out.println(asc);
    }

    @Override
    public PageInfo<IndexResultBean> getPropagationListData(StatisticsBean analysisSubject) {
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(JSONUtil.toBean(JSONObject.toJSONString(analysisSubject), StatisticsBean.class), "2");
        return getQueryPageInfoList(analysisSubject, bool);
    }

    public PageInfo<IndexResultBean> getQueryPageInfoList(StatisticsBean analysisSubject, BoolQuery.Builder bool) {
        SearchResponse<Object> response = null;
        PageInfo<IndexResultBean> pageInfo = new PageInfo<>();
        try {
            String sortField = analysisSubject.getSortField();
            String sortType = analysisSubject.getSortType();
            List<SortOptions> sorts = new ArrayList<>();
            List<SortBO> sorts1 = analysisSubject.getSorts();
            if (CollectionUtil.isNotEmpty(sorts1)) {
                for (SortBO sortBO : sorts1) {
                    sorts.add(SortOptionsBuilders.field(f -> f.field(sortBO.getSortField()).order(CommonUtil.getSort(sortBO.getSortType()))));
                }
            } else if (StrUtil.isNotEmpty(sortField) && StrUtil.isNotEmpty(sortType)) {
                sorts.add(SortOptionsBuilders.field(f -> f.field(sortField).order(CommonUtil.getSort(sortType))));
            }
            if (CollectionUtil.isNotEmpty(sorts)) {
                response = esClient.search(s -> s.index(EsUtil.getIndexes(analysisSubject.getStartTime(),analysisSubject.getEndTime())).trackTotalHits(t -> t.enabled(true))
                        .query(bool.build()._toQuery()).from((analysisSubject.getPageNum() - 1) * analysisSubject.getPageSize()).size(analysisSubject.getPageSize()).
                        //      sort(c -> c.field(f -> f.field(sortField).order(finalSort))), Object.class);
                                sort(sorts), Object.class);
            } else {
                response = esClient.search(s -> s.index(EsUtil.getIndexes(analysisSubject.getStartTime(),analysisSubject.getEndTime())).trackTotalHits(t -> t.enabled(true))
                                .query(bool.build()._toQuery()).from((analysisSubject.getPageNum() - 1) * analysisSubject.getPageSize()).size(analysisSubject.getPageSize())
                        , Object.class);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        List<Hit<Object>> hits = response.hits().hits();
        List<IndexResultBean> datas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(hits)) {
            for (Hit<Object> hit : hits) {
                Object source = hit.source();
                String jsonString = JSONObject.toJSONString(source);
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                IndexResultBean indexResultBean = JSONObject.parseObject(jsonString, IndexResultBean.class);
                indexResultBean.setBiz(jsonObject.getString("bizId"));
                indexResultBean.setTime(jsonObject.getString("publishTime"));
                indexResultBean.setSiteAddress(jsonObject.getString("siteAreaCode"));
                indexResultBean.setOriginal(jsonObject.getBoolean("isOriginal"));
                indexResultBean.setSubmitTime(jsonObject.getString("updateTime"));
                datas.add(indexResultBean);
            }
        }
        pageInfo.setList(datas);
        pageInfo.setTotal(response.hits().total() != null ? response.hits().total().value() : 0);
        return pageInfo;
    }

    //---先不删吧，后面再删
    @Override
    public PageInfo<IndexResultBean> getAreaAuthorInfo(StatisticsBean statisBean, String aggName, List<String> areaCodes) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisBean, statisBean.getSearchKewordType());
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            // 此处注意prefix只能一后缀匹配,如果需要多个匹配需要用:"wildcard": { "field": "val*e*" } ,但是性能比prefix差
            List<Query> querys = new ArrayList<>();
            for (String areaCode : areaCodes) {
                querys.add(QueryBuilders.prefix(t -> t.field("contentAreaCode").value(areaCode)));
            }
            bool.should(querys).minimumShouldMatch("1");
        }
        SearchResponse<Object> response = null;
        try {
            String startTime = statisBean.getStartTime();
            String endTime = statisBean.getEndTime();
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(startTime,endTime)).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .from((statisBean.getPageNum() - 1) * statisBean.getPageSize()).size(statisBean.getPageSize())
                    , Object.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 执行查询
        PageInfo<IndexResultBean> pageResult = new PageInfo();

        List<Hit<Object>> hits = response.hits().hits();
        List<IndexResultBean> datas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(hits)) {
            for (Hit<Object> hit : hits) {
                Object source = hit.source();
                String jsonString = JSONObject.toJSONString(source);
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                IndexResultBean indexResultBean = JSONObject.parseObject(jsonString, IndexResultBean.class);
                indexResultBean.setBiz(jsonObject.getString("bizId"));
                indexResultBean.setTime(jsonObject.getString("publishTime"));
                indexResultBean.setSiteAddress(jsonObject.getString("siteAreaCode"));
                indexResultBean.setOriginal(jsonObject.getBoolean("isOriginal"));
                indexResultBean.setSubmitTime(jsonObject.getString("updateTime"));
                datas.add(indexResultBean);
            }
        }
        pageResult.setList(datas);
        pageResult.setTotal(response.hits().total() != null ? response.hits().total().value() : 0);
        return pageResult;
    }

    @Override
    public PageInfo<IndexResultBean> searchByKeyWords(StatisticsBean analysisSubject) {
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(JSONUtil.toBean(JSONObject.toJSONString(analysisSubject), StatisticsBean.class), "2");
        List<Query> mustNotQueries = new ArrayList<>();
        mustNotQueries.add(QueryBuilders.bool().should(QueryBuilders.exists(s -> s.field("picUrl"))).should(QueryBuilders.exists(c -> c.field("authorPortraitLink"))).minimumShouldMatch("1").build()._toQuery());
        bool.mustNot(mustNotQueries);
        return getQueryPageInfoList(analysisSubject, bool);
    }

    @Override
    public Map<String, Long> getScreenAreaCount(StatisticsBean statisticsBean, String aggName, List<String> areaCodes) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean, statisticsBean.getSearchKewordType());
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            // 此处注意prefix只能一后缀匹配,如果需要多个匹配需要用:"wildcard": { "field": "val*e*" } ,但是性能比prefix差
            List<Query> querys = new ArrayList<>();
            for (String areaCode : areaCodes) {
                querys.add(QueryBuilders.prefix(t -> t.field("contentAreaCode").value(areaCode)));
            }
            bool.should(querys).minimumShouldMatch("1");
        }
        return areaDistributionService.getAggCountMap(aggName, bool, statisticsBean.getAggSize(),statisticsBean);
    }

    /**
     * 初步的公用搜索，实际上可能有bug，还在完善中
     *
     * @param statisticsBean
     * @param type           搜索的类型 type="1"是关键词之间是or的关系，慎用！；  "2"是常用的类型，正在考虑将2作为默认值
     * @return co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder
     * <AUTHOR>
     * @date 2024/5/10 13:20
     **/
    @Override
    public BoolQuery.Builder setCommonSearch(StatisticsBean statisticsBean, String... type) {
        List<Query> mustNotQueries = new ArrayList<>();
        List<Query> mustQueries = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();
        if (statisticsBean.getConfigSelect() == 1) {
            //专业搜索   照搬escommon中的代码
            String expression = statisticsBean.getProWord().replaceAll("\\| \\(", "|\\(");
            Query query = EsUtil.proSearchBuilder(expression, 0, statisticsBean.getKeywordsPosition());
            bool.filter(query);
        } else {
            //非专业搜索
            if (CharSequenceUtil.isNotBlank(statisticsBean.getKeywords1())) {
                QueryStringQuery.Builder keyWordQb1 = EsUtil.buildKeyWord(EsUtil.quotedEveryKeyWord(statisticsBean.getKeywords1(), statisticsBean.isAccurate()), statisticsBean.getKeywordsPosition());
                bool.filter(keyWordQb1.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(statisticsBean.getKeywords2())) {
                QueryStringQuery.Builder keyWordQb2 = EsUtil.buildKeyWord(EsUtil.quotedEveryKeyWord(statisticsBean.getKeywords2(), statisticsBean.isAccurate()), statisticsBean.getKeywordsPosition());
                bool.filter(keyWordQb2.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(statisticsBean.getKeywords3())) {
                QueryStringQuery.Builder keyWordQb3 = EsUtil.buildKeyWord(EsUtil.quotedEveryKeyWord(statisticsBean.getKeywords3(), statisticsBean.isAccurate()), statisticsBean.getKeywordsPosition());
                bool.filter(keyWordQb3.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(statisticsBean.getKeywords4())) {
                QueryStringQuery.Builder keyWordQb4 = EsUtil.buildKeyWord(EsUtil.quotedEveryKeyWord(statisticsBean.getKeywords4(), statisticsBean.isAccurate()), statisticsBean.getKeywordsPosition());
                bool.filter(keyWordQb4.build()._toQuery());
            }
        }
        //处理排除词过滤词
        if (CharSequenceUtil.isNotBlank(statisticsBean.getExcludeWords()) /*|| CharSequenceUtil.isNotBlank(statisticsBean.getQuadraticFilterWord())*/) {
            StringBuilder stringBuilder = new StringBuilder();
            if (CharSequenceUtil.isNotBlank(statisticsBean.getExcludeWords())) {
                stringBuilder.append(statisticsBean.getExcludeWords());
            }
//            if (CharSequenceUtil.isNotBlank(statisticsBean.getQuadraticFilterWord())) {
//                stringBuilder.append(statisticsBean.getQuadraticFilterWord());
//            }
            String[] keyWordArr = stringBuilder.toString().split("\\s+");
            List<String> keyWordList = new ArrayList<>();
            for (String str : keyWordArr) {
                keyWordList.add("\"" + str + "\"");
            }
            String keyword = org.apache.commons.lang3.StringUtils.join(keyWordList, " ");
            QueryStringQuery.Builder queryString = QueryBuilders.queryString();
            queryString.query(keyword).defaultOperator(Operator.Or);
            bool.mustNot(queryString.build()._toQuery());
        }
        //时间范围
        setTimeCondition(statisticsBean, bool);
        //设置内容地域
        if (StrUtil.isNotEmpty(statisticsBean.getContentAreaCode())) {
            List<FieldValue> fieldValues = Arrays.stream(statisticsBean.getContentAreaCode().split(",")).map(FieldValue::of).collect(Collectors.toList());
            bool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).terms(q -> q.value(fieldValues))));
        }

        // 情感解析
        Integer emotion = statisticsBean.getEmotion();
        if (emotion != null && -1 != emotion) {
            bool.must(q -> q.term(t -> t.field("emotionFlag").value(emotion)));
        }
        String md5 = statisticsBean.getMd5();
        if (StrUtil.isNotEmpty(md5)) {
            bool.filter(q -> q.term(t -> t.field("md5").value(md5)));
        }
//        // 原创解析
//        int original = statisticsBean.getOriginal();
//        if (1 == original) {
//            bool.filter(q -> q.term(t -> t.field("isOriginal").value(true)));
//        }
        //是否垃圾数据  =0不是垃圾数据
        if (statisticsBean.getSpamFlag()!=null && statisticsBean.getSpamFlag() == 0 ) {
            bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_SPAM.getFieldName()).value(IsSpamEnum.IS_NOT_SPAM.getFlag())));
        }
        // 处理媒体类型
        if (CharSequenceUtil.isNotBlank(statisticsBean.getMediaType())) {
            BoolQuery.Builder typeBool = QueryBuilders.bool();
            List<String> typeArray;
            if (statisticsBean.getMediaType().contains(",")) {
                typeArray = new ArrayList<>(Arrays.asList(statisticsBean.getMediaType().split(",")));
            }else{
                typeArray = new ArrayList<>(Arrays.asList(statisticsBean.getMediaType().split(" ")));
            }
            // 增加微博专属条件contentForm accountLevel forward accountAreaCode
            if (typeArray.contains(String.valueOf(MediaTypeEnum.WEIBO.getValue()))) {
                List<Query> queryList = new ArrayList<>();
                if (statisticsBean.getOriginal() == 1) {
                    queryList.add(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_ORIGINAL.getFlag())));
                }
                if (!queryList.isEmpty()) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.WEIBO.getValue())));
                    typeBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
                    typeArray.removeIf(String.valueOf(MediaTypeEnum.WEIBO.getValue())::equals);
                }
            }
            // 对剩下媒体类型处理
            if (CollUtil.isNotEmpty(typeArray)) {
                List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).terms(q -> q.value(fieldValues)));
                typeBool.should(terms);
            }
            typeBool.minimumShouldMatch("1");
            bool.filter(typeBool.build()._toQuery());
        }
//        // type类型
//        String mediaType = statisticsBean.getMediaType();
//        if (!StringUtils.isEmpty(mediaType) && !"-1".equals(mediaType)) {
//            if (mediaType.contains(",")) {
//                bool.filter(QueryBuilders.terms(t -> t.field("type").terms(te -> te.value(Arrays.stream(mediaType.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
//            } else if (mediaType.contains(" ")) {
//                bool.filter(QueryBuilders.terms(t -> t.field("type").terms(te -> te.value(Arrays.stream(mediaType.split(" ")).map(FieldValue::of).collect(Collectors.toList())))));
//            }
//        }
        //url查询
        if (statisticsBean.getUrl() != null && !statisticsBean.getUrl().isEmpty()) {
            String finalUrl = statisticsBean.getUrl();
            bool.filter(QueryBuilders.term(c -> c.field("host").value(finalUrl)));
        }
        // host
        if (statisticsBean.getHost() != null && !statisticsBean.getHost().isEmpty()) {
            String finalHost = statisticsBean.getHost();
            bool.filter(QueryBuilders.terms(c -> c.field("host").terms(t -> t.value(Arrays.stream(finalHost.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
        }
        if (statisticsBean.getAuthor() != null && !statisticsBean.getAuthor().isEmpty()) {
            bool.filter(q -> q.term(t -> t.field("author").value(statisticsBean.getAuthor())));
        } else {
            if (null != statisticsBean.getAuthorExists() && statisticsBean.getAuthorExists()) {
                bool.filter(QueryBuilders.exists(s -> s.field("author")));
            }
        }
        bool.filter(mustQueries);
        bool.mustNot(mustNotQueries);
        return bool;
    }

    public static void setTimeCondition(StatisticsBean statisticsBean, BoolQuery.Builder bool) {
        // 时间解析
        String startTime = statisticsBean.getStartTime();
        String endTime = statisticsBean.getEndTime();
        if (StrUtil.isEmpty(startTime) || StrUtil.isEmpty(endTime)) {
            // 所有时间
            startTime = "1970-01-01 00:00:00";
            endTime = DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN);
        }
        if (!startTime.contains("-") && !endTime.contains("-")) {
            startTime = DateUtil.format(DateUtil.parse(startTime, DatePattern.PURE_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            endTime = DateUtil.format(DateUtil.parse(endTime, DatePattern.PURE_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
        }
        if (startTime.split(":").length == 2 || endTime.split(":").length == 2) {
            startTime = startTime + ":00";
            endTime = endTime + ":00";
        }
        if (!StrUtil.isEmpty(statisticsBean.getStartTime()) && !StrUtil.isEmpty(statisticsBean.getEndTime())) {
            if (statisticsBean.getTimeType() == 0) {
                String finalStartTime = startTime;
                String finalEndTime = endTime;
                Query range = QueryBuilders.range(r -> r.field("publishTime")
                        .from(finalStartTime).to(finalEndTime));
                bool.filter(range);
            } else {
                String finalStartTime1 = startTime;
                String finalEndTime1 = endTime;
                Query range = QueryBuilders.range(r -> r.field("updateTime")
                        .from(finalStartTime1).to(finalEndTime1));
                bool.filter(range);
            }
        }
    }

    @Override
    public IndexResultVo propagationPath(StatisticsBean statisticsBean) {
        IndexResultVo data = new IndexResultVo();
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(statisticsBean);
        SearchResponse<Object> response = null;
        List<Object> objects = new ArrayList<>();
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(statisticsBean.getStartTime(),statisticsBean.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .size(5000).scroll(t -> t.time("60s")), Object.class);
            String scrollId = response.scrollId();

            for (Hit<Object> hit : response.hits().hits()) {
                Object esFileVO = hit.source();
                objects.add(esFileVO);
            }
            ScrollResponse<Object> scrollResp = null;
            do {
                if (scrollResp != null) {
                    for (Hit<Object> hit : scrollResp.hits().hits()) {
                        Object esFileVO = hit.source();
                        objects.add(esFileVO);
                    }
                }
                String finalScrollId = scrollId;
                scrollResp = esClient.scroll(s -> s.scrollId(finalScrollId).scroll(t -> t.time("60s")),
                        Object.class);
                scrollId = scrollResp.scrollId();
            } while (!scrollResp.hits().hits().isEmpty());

            // 清理滚动上下文
            String finalScrollIdX = scrollId;
            esClient.clearScroll(s -> s.scrollId(finalScrollIdX));


        } catch (IOException e) {
            e.printStackTrace();
        }
        List<IndexResultBean> datas = new ArrayList();
        for (Object esFileVO : objects) {
            String jsonString = JSONObject.toJSONString(esFileVO);
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            IndexResultBean indexResultBean = JSONObject.parseObject(jsonString, IndexResultBean.class);
            indexResultBean.setBiz(jsonObject.getString("bizId"));
            indexResultBean.setTime(jsonObject.getString("publishTime"));
            indexResultBean.setSiteAddress(jsonObject.getString("siteAreaCode"));
            indexResultBean.setOriginal(jsonObject.getBoolean("isOriginal"));
            indexResultBean.setSubmitTime(jsonObject.getString("updateTime"));
            // 减少网络带宽数据量
            indexResultBean.setText(null);
            indexResultBean.setTitle(null);
            indexResultBean.setPicUrl(null);
            indexResultBean.setUrl(null);
            datas.add(indexResultBean);
        }
        data.setIndexResult(datas);
        data.setAllNum(response.hits().total().value());
        return data;
    }

    /**
     * 这个接口开始设计的不好，现在做了兼容性增强，支持areaCodes实参list,也支持vo的字段复制
     *
     * @param statisticsBean
     * @param aggName
     * @param areaCodes
     * @return java.util.Map<java.lang.String, java.lang.Long>
     * <AUTHOR>
     * @date 2024/5/16 17:19
     **/
    @Override
    public Map<String, Long> getAreaCodeDistribution(StatisticsBean statisticsBean, String aggName, List<String> areaCodes) {
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(statisticsBean);
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            List<FieldValue> fieldValues = areaCodes.stream().map(FieldValue::of).collect(Collectors.toList());
            bool.should(QueryBuilders.terms(t -> t.field(aggName).terms(q -> q.value(fieldValues))));
            bool.minimumShouldMatch("1");
        } else {
            if (StrUtil.isNotEmpty(statisticsBean.getContentAreaCode())) {
                bool.must(QueryBuilders.term(s -> s.field("contentAreaCode").value(statisticsBean.getContentAreaCode())));
            }
        }
        return SpringUtil.getBean(AreaDistributionService.class).getAggCountMap(aggName, bool, statisticsBean.getAggSize(),statisticsBean);
    }
}
