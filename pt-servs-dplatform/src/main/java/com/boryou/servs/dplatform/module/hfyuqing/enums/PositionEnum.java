package com.boryou.servs.dplatform.module.hfyuqing.enums;

import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;

import java.util.HashMap;
import java.util.Map;

public enum PositionEnum {
    ALL( "all", "0"),

    TITLE(EsBeanFieldEnum.TITLE.getFieldName(), "1"),

    TEXT(EsBeanFieldEnum.TEXT.getFieldName(), "2"),

    AUTHOR(EsBeanFieldEnum.AUTHOR.getFieldName(), "3");

    private final String code;
    private final String info;

    PositionEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    private static final Map<String, PositionEnum> categoryMap = new HashMap<>();

    static {
        PositionEnum[] ens = PositionEnum.values();
        for (PositionEnum en : ens) {
            categoryMap.put(en.getCode(), en);
        }
    }

    public static PositionEnum getEnumByCode(String code) {
        return categoryMap.get(code);
    }
}
