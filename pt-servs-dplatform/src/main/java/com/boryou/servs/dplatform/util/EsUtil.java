package com.boryou.servs.dplatform.util;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch._types.*;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.AggregationBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.UpdateByQueryRequest;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.util.NamedValue;
import com.boryou.servs.common.constant.BasicConstant;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.enums.*;
import com.boryou.servs.dplatform.module.hfyuqing.enums.PositionEnum;
import com.boryou.servs.dplatform.pojo.bo.EsExcludeBO;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.SourceSettingBO;
import com.boryou.servs.dplatform.pojo.vo.SourceMapV2VO;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description Es工具类, 进行各种条件组装拼接
 * @date 2024/4/23 9:51
 */
public class EsUtil {

    private EsUtil() {
    }

    /**
     * 拼接查询条件*
     *
     * @param esSearchBO 检索条件BO
     * @return 返回值
     */
    public static BoolQuery.Builder buildQuery(EsSearchBO esSearchBO) {
        BoolQuery.Builder bool = QueryBuilders.bool();
        List<Query> mustNot = new ArrayList<>();
        // 拼接关键词
        if (esSearchBO.getConfigSelect() == 1) {
            //量子系统专业搜索舆情监测
            if (esSearchBO.getProjectType() != null && ProjectFlagEnum.QUANTUM.getValue() == esSearchBO.getProjectType() && esSearchBO.getType() != null && esSearchBO.getType().contains("&")) {
                String type1 = "";
                String type2 = "";
                String[] types = esSearchBO.getType().split("&");
                if (types.length > 0) {
                    type1 = types[0];
                }
                if (types.length > 1) {
                    type2 = types[1];
                }

                //量子
                BoolQuery.Builder proBool = new BoolQuery.Builder();
                // 专业搜索 关键词+type融合
                if (StrUtil.isNotEmpty(type1) && (StrUtil.isNotEmpty(esSearchBO.getProWord()) || StrUtil.isNotEmpty(esSearchBO.getExcludeWord()))) {
                    List<String> typeArray = new ArrayList<>(Arrays.asList(type1.split(",")));
                    List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                    Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).terms(q -> q.value(fieldValues)));
                    BoolQuery.Builder bool1 = QueryBuilders.bool();
                    if (StrUtil.isNotEmpty(esSearchBO.getProWord())) {
                        String expression = esSearchBO.getProWord()
                                .replaceAll("  ", " ")
                                .replaceAll("\\) \\(", "\\)|\\(")
                                .replaceAll("\\| \\(", "|\\(");
                        Query query = proSearchBuilder(expression, 0, esSearchBO.getSearchPosition());
                        bool1.must(terms, query);
                    } else {
                        bool1.must(terms);
                    }
                    if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord())) {
                        String excludeWord = esSearchBO.getExcludeWord()
                                .replaceAll("  ", " ")
                                .replaceAll("\\) \\(", "\\)|\\(")
                                .replaceAll("\\| \\(", "|\\(");
                        bool1.mustNot(proSearchBuilder(excludeWord, 0, esSearchBO.getSearchPosition()));
                    }

                    proBool.should(bool1.build()._toQuery());
                }
                if (StrUtil.isNotEmpty(type2) && (StrUtil.isNotEmpty(esSearchBO.getProWord2()) || StrUtil.isNotEmpty(esSearchBO.getExcludeWord2()))) {
                    List<String> typeArray = new ArrayList<>(Arrays.asList(type2.split(",")));
                    List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                    Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).terms(q -> q.value(fieldValues)));
                    BoolQuery.Builder bool1 = QueryBuilders.bool();
                    if (StrUtil.isNotEmpty(esSearchBO.getProWord2())) {
                        String expression = esSearchBO.getProWord2()
                                .replaceAll("  ", " ")
                                .replaceAll("\\) \\(", "\\)|\\(")
                                .replaceAll("\\| \\(", "|\\(");
                        Query query = proSearchBuilder(expression, 0, esSearchBO.getSearchPosition());
                        bool1.must(terms, query);
                    } else {
                        bool1.must(terms);
                    }
                    if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord2())) {
                        String excludeWord = esSearchBO.getExcludeWord2()
                                .replaceAll("  ", " ")
                                .replaceAll("\\) \\(", "\\)|\\(")
                                .replaceAll("\\| \\(", "|\\(");
                        bool1.mustNot(proSearchBuilder(excludeWord, 0, esSearchBO.getSearchPosition()));
                    }

                    proBool.should(bool1.build()._toQuery());
                }
                //上面两种都为空时判断  不查询任何数据
                if ((StrUtil.isEmpty(type1) || (StrUtil.isEmpty(esSearchBO.getProWord()) && StrUtil.isEmpty(esSearchBO.getExcludeWord())))
                        && (StrUtil.isEmpty(type2) || (StrUtil.isEmpty(esSearchBO.getProWord2()) && StrUtil.isEmpty(esSearchBO.getExcludeWord2())))) {
                    esSearchBO.setType("-1");
                } else {
                    bool.filter(proBool.build()._toQuery());
                }
            } else {
                // 专业搜索
                if (StrUtil.isNotEmpty(esSearchBO.getProWord())) {
                    String expression = esSearchBO.getProWord()
                            .replaceAll("  ", " ")
                            .replaceAll("\\) \\(", "\\)|\\(")
                            .replaceAll("\\| \\(", "|\\(");
                    Query query = proSearchBuilder(expression, 0, esSearchBO.getSearchPosition());
                    bool.filter(query);
                }
            }
        } else {
            // 非专业搜索
            if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord1())) {
                QueryStringQuery.Builder keyWordQb1 = buildKeyWord(quotedEveryKeyWord(esSearchBO.getKeyWord1(), esSearchBO.getAccurate()), esSearchBO.getSearchPosition());
                bool.filter(keyWordQb1.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord2())) {
                QueryStringQuery.Builder keyWordQb2 = buildKeyWord(quotedEveryKeyWord(esSearchBO.getKeyWord2(), esSearchBO.getAccurate()), esSearchBO.getSearchPosition());
                bool.filter(keyWordQb2.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord3())) {
                QueryStringQuery.Builder keyWordQb3 = buildKeyWord(quotedEveryKeyWord(esSearchBO.getKeyWord3(), esSearchBO.getAccurate()), esSearchBO.getSearchPosition());
                bool.filter(keyWordQb3.build()._toQuery());
            }
            if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord4())) {
                QueryStringQuery.Builder keyWordQb4 = buildKeyWord(quotedEveryKeyWord(esSearchBO.getKeyWord4(), esSearchBO.getAccurate()), esSearchBO.getSearchPosition());
                bool.filter(keyWordQb4.build()._toQuery());
            }
        }
        // 二次检索词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticWord())) {
            String expression = esSearchBO.getQuadraticWord()
                    .replaceAll("  ", " ")
                    .replaceAll("\\) \\(", "\\)|\\(")
                    .replaceAll("\\| \\(", "|\\(");
            Query query;
            if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticPosition())) {
                query = proSearchBuilder(expression, 0, esSearchBO.getQuadraticPosition());
            } else {
                query = proSearchBuilder(expression, 0, esSearchBO.getSearchPosition());
            }
            bool.filter(query);
        }

        // 处理排除id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeId())) {
            String excludeId = esSearchBO.getExcludeId();
            List<FieldValue> list = Arrays.stream(excludeId.split(","))
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
//            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ID.getFieldName()).terms(q -> q.value(list))));
            mustNot.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ID.getFieldName()).terms(q -> q.value(list))));
        }
        // 处理排除词过滤词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord()) || CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
            //量子系统舆情监测专业搜索的过滤词已单独处理   当前type.contains(&)一定是量子系统舆情监测专业搜索
            if (esSearchBO.getConfigSelect() == 1) {
                if ((esSearchBO.getProjectType() == null || esSearchBO.getType() == null || ProjectFlagEnum.QUANTUM.getValue() != esSearchBO.getProjectType() || !esSearchBO.getType().contains("&"))) {
                    String expression = esSearchBO.getExcludeWord()
                            .replaceAll("  ", " ")
                            .replaceAll("\\) \\(", "\\)|\\(")
                            .replaceAll("\\| \\(", "|\\(");
                    Query query = proSearchBuilder(expression, 0, esSearchBO.getSearchPosition());
                    mustNot.add(query);
                }
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord())) {
                    stringBuilder.append(esSearchBO.getExcludeWord());
                }
                if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
                    stringBuilder.append(esSearchBO.getQuadraticFilterWord());
                }
                String[] keyWordArr = stringBuilder.toString().split("\\s+");
                List<String> keyWordList = new ArrayList<>();
                for (String str : keyWordArr) {
                    keyWordList.add("\"" + str + "\"");
                }
                String keyword = StringUtils.join(keyWordList, " OR ");
                QueryStringQuery.Builder queryString = buildProWord(keyword, esSearchBO.getSearchPosition());
//            bool.mustNot(queryString.build()._toQuery());
                mustNot.add(queryString.build()._toQuery());
            }
        }
        if (CollUtil.isNotEmpty(esSearchBO.getExcludeTermField())) {
            List<EsExcludeBO> excludeTermField = esSearchBO.getExcludeTermField();
//            ArrayList<Query> list = new ArrayList<>();
            for (EsExcludeBO esExcludeBO : excludeTermField) {
                List<FieldValue> fieldValues = Arrays.stream(esExcludeBO.getValues().split(",")).map(FieldValue::of).collect(Collectors.toList());
//                list.add(QueryBuilders.terms(s->s.field(esExcludeBO.getField()).terms(c->c.value(fieldValues))));
                mustNot.add(QueryBuilders.terms(s -> s.field(esExcludeBO.getField()).terms(c -> c.value(fieldValues))));
            }
//            bool.mustNot(list);
//            mustNot.add(list);
        }

        // 处理是否原创条件    后面讨论确认只针对微博有效，所以在下方微博逻辑判断，先考虑forward字段，如果为空再处理isOriginal字段。
//        if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
//            bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_ORIGINAL.getFlag())));
//        }
        //是否垃圾数据  false是不是垃圾数据
        if (esSearchBO.getIsSpam() != null && IsSpamEnum.IS_NOT_SPAM.getFlag() == esSearchBO.getIsSpam()) {
            bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_SPAM.getFieldName()).value(IsSpamEnum.IS_NOT_SPAM.getFlag())));
        }
        // 处理媒体类型
        if (CharSequenceUtil.isNotBlank(esSearchBO.getType())) {
            if (esSearchBO.getType().contains("&")) {
                if (esSearchBO.getType().startsWith("&")) {
                    esSearchBO.setType(esSearchBO.getType().replace("&", ""));
                } else {
                    esSearchBO.setType(esSearchBO.getType().replace("&", ","));
                }
            }
            BoolQuery.Builder typeBool = QueryBuilders.bool();
            List<String> typeArray = new ArrayList<>(Arrays.asList(esSearchBO.getType().split(",")));
            // 增加微博专属条件contentForm accountLevel forward accountAreaCode
            if (typeArray.contains(String.valueOf(MediaTypeEnum.WEIBO.getValue()))) {
                List<Query> queryList = new ArrayList<>();

                if (StrUtil.isNotEmpty(esSearchBO.getContentForm())) {
                    List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getContentForm().split(",")).map(FieldValue::of).collect(Collectors.toList());
                    queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.CONTENT_FORM.getFieldName()).terms(q -> q.value(fieldValues))));
                }
                if (StrUtil.isNotEmpty(esSearchBO.getAccountLevel())) {
                    List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getAccountLevel().split(",")).map(FieldValue::of).collect(Collectors.toList());
                    queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ACCOUNT_LEVEL.getFieldName()).terms(q -> q.value(fieldValues))));
                }
                if (StrUtil.isNotEmpty(esSearchBO.getForward()) && !esSearchBO.getForward().contains(",") && !esSearchBO.getForward().equals("0")) {
                    boolean forward = "1".equals(esSearchBO.getForward());
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(forward)));
                } else if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
                    queryList.add(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_ORIGINAL.getFlag())));
                }
                if (StrUtil.isNotEmpty(esSearchBO.getAccountAreaCode())) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).value(esSearchBO.getAccountAreaCode())));
                }
                if (queryList.size() > 0) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.WEIBO.getValue())));
                    typeBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
                    typeArray.removeIf(String.valueOf(MediaTypeEnum.WEIBO.getValue())::equals);
                }
            }
            //根据文章来源所属板块 sector 来查询
            if (StrUtil.isNotEmpty(esSearchBO.getSector())) {
                bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.SECTOR.getFieldName()).value(esSearchBO.getSector())));
            }
            // 增加短视频专属条件videoHost
            if (typeArray.contains(String.valueOf(MediaTypeEnum.VIDEO.getValue()))) {
                List<Query> queryList = new ArrayList<>();
                if (StrUtil.isNotEmpty(esSearchBO.getVideoHost())) {
                    List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getVideoHost().split(",")).map(FieldValue::of).collect(Collectors.toList());
                    queryList.add(QueryBuilders.terms(q -> q.field(EsBeanFieldEnum.HOST.getFieldName()).terms(t -> t.value(fieldValues))));
                }
                if (queryList.size() > 0) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.VIDEO.getValue())));
                    typeBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
                    typeArray.removeIf(String.valueOf(MediaTypeEnum.VIDEO.getValue())::equals);
                }
            }
            // 对剩下媒体类型处理
            if (CollUtil.isNotEmpty(typeArray)) {
                List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).terms(q -> q.value(fieldValues)));
                typeBool.should(terms);
            }
            typeBool.minimumShouldMatch("1");
            bool.filter(typeBool.build()._toQuery());
        }
        if (StrUtil.isNotEmpty(esSearchBO.getAccountGrade())) {
            List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getAccountGrade().split(",")).map(FieldValue::of).collect(Collectors.toList());
            bool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ACCOUNT_GRADE.getFieldName()).terms(q -> q.value(fieldValues))));
        }
        // 处理定向id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getId())) {
            buildTermsQueries(bool, EsBeanFieldEnum.ID.getFieldName(), esSearchBO.getId().split(","));
        }
        // 情感  如果是0,1,2全选就不带条件进去，减少过滤性能
        if (CharSequenceUtil.isNotBlank(esSearchBO.getEmotionFlag()) && !"0,1,2".equals(esSearchBO.getEmotionFlag().trim())) {
            buildTermsQueries(bool, EsBeanFieldEnum.EMOTION_FLAG.getFieldName(), esSearchBO.getEmotionFlag().split(","));
        }
        // 域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getHost())) {
            buildTermsQueries(bool, EsBeanFieldEnum.HOST.getFieldName(), esSearchBO.getHost().split(","));
        }
        // 排除域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeHost())) {
            List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getExcludeHost().split(",")).map(FieldValue::of).collect(Collectors.toList());
//            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(fieldValues))));
            mustNot.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(fieldValues))));
        }
        // 站点地域
        if (CharSequenceUtil.isNotBlank(esSearchBO.getSiteAreaCode())) {
            buildTermsQueries(bool, EsBeanFieldEnum.SITE_AREA_CODE.getFieldName(), esSearchBO.getSiteAreaCode().split(","));
        }
        // 站点标签
        if (CharSequenceUtil.isNotBlank(esSearchBO.getSiteMeta())) {
            buildTermsQueries(bool, EsBeanFieldEnum.SITE_META.getFieldName(), esSearchBO.getSiteMeta().split(","));
        }
        // 信息分类标签
        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentMeta())) {
            buildTermsQueries(bool, EsBeanFieldEnum.CONTENT_META.getFieldName(), esSearchBO.getContentMeta().split(","));
        }
        // 文章地域
        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentAreaCode())) {
            buildTermsQueries(bool, EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName(), esSearchBO.getContentAreaCode().split(","));
        }
//        //文章内容 仅微博
//        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentForm())) {
//            buildTermsQueries(bool,EsBeanFieldEnum.CONTENT_FORM.getFieldName(),esSearchBO.getContentForm().split(","));
//        }
        // 作者
        if (CharSequenceUtil.isNotBlank(esSearchBO.getAuthor())) {
            buildTermsQueries(bool, EsBeanFieldEnum.AUTHOR.getFieldName(), esSearchBO.getAuthor().split(","));
        }
        // 作者id集合
        if (CharSequenceUtil.isNotBlank(esSearchBO.getAuthorIds())) {
            buildTermsQueries(bool, EsBeanFieldEnum.AUTHOR_ID.getFieldName(), esSearchBO.getAuthorIds().split(","));
        }
        // 排除作者
        String excludeAuthor = esSearchBO.getExcludeAuthor();
        if (CharSequenceUtil.isNotBlank(excludeAuthor)) {
            List<FieldValue> fieldValues = Arrays.stream(excludeAuthor.split(",")).map(FieldValue::of).collect(Collectors.toList());
//            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(fieldValues))));
            mustNot.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(fieldValues))));
        }
        String needHost = esSearchBO.getNeedHost();
        if (CharSequenceUtil.isNotBlank(needHost)) {
            BoolQuery.Builder hostAuthorBool = QueryBuilders.bool();
            hostAuthorBool.should(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName())
                    .terms(q -> q.value(StrUtil.splitTrim(needHost, ",")
                            .stream().map(FieldValue::of).collect(Collectors.toList())))));
            hostAuthorBool.should(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName())
                    .terms(q -> q.value(StrUtil.splitTrim(needHost, ",")
                            .stream().map(FieldValue::of).collect(Collectors.toList())))));
            hostAuthorBool.minimumShouldMatch("1");
            bool.filter(hostAuthorBool.build()._toQuery());
        }
        // 定向信源
        if ("1".equals(esSearchBO.getSourceSetting()) || "2".equals(esSearchBO.getSourceSetting())) {
            BoolQuery.Builder sourceBool = QueryBuilders.bool();
            Map<String, String> sourceMap = esSearchBO.getSourceMap();
            try {
                if (sourceMap != null) {
                    for (Map.Entry<String, String> entry : sourceMap.entrySet()) {
                        // 按照类型查询命中的author或host
                        List<Query> queryList = new ArrayList<>();
                        queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(entry.getKey())));
                        if (!entry.getKey().equals(String.valueOf(MediaTypeEnum.WECHAT.getValue()))
                                && !entry.getKey().equals(String.valueOf(MediaTypeEnum.WEIBO.getValue()))
                                && !entry.getKey().equals(String.valueOf(MediaTypeEnum.CLIENT.getValue()))
                        ) {
                            queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(StrUtil.splitTrim(entry.getValue(), ",")
                                    .stream().map(FieldValue::of).collect(Collectors.toList())))));
                        } else {
                            queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(StrUtil.splitTrim(entry.getValue(), ",")
                                    .stream().map(FieldValue::of).collect(Collectors.toList())))));
                        }
                        sourceBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
                    }
                    // 1 must  2 mustnot
                    if ("1".equals(esSearchBO.getSourceSetting())) {
                        bool.filter(sourceBool.build()._toQuery());
                    } else {
//                bool.mustNot(sourceBool.build()._toQuery());
                        mustNot.add(sourceBool.build()._toQuery());
                    }
                }

            } catch (Exception e) {
                System.out.println(esSearchBO);
                e.printStackTrace();
            }

        }
        // 定向信源V2
        buildSourceMapV2(esSearchBO, bool, mustNot);
        //定向信源-大V
        if (CollUtil.isNotEmpty(esSearchBO.getSourceSettingBO())) {
            BoolQuery.Builder sourceBool = QueryBuilders.bool();
            List<SourceSettingBO> sourceMap = esSearchBO.getSourceSettingBO();
            try {
                if (sourceMap != null) {
                    Map<String, List<SourceSettingBO>> collect = sourceMap.stream().collect(Collectors.groupingBy(s -> s.getType()));
                    for (Map.Entry<String, List<SourceSettingBO>> entry : collect.entrySet()) {
                        List<SourceSettingBO> value = entry.getValue();
                        // 按照类型查询命中的author或host
                        List<Query> queryList = new ArrayList<>();
                        queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(entry.getKey())));
                        List<String> typeArray = value.stream().map(SourceSettingBO::getDomain).collect(Collectors.toList());
                        List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                        List<String> authorList = value.stream().map(SourceSettingBO::getAuthor).collect(Collectors.toList());
                        List<FieldValue> authors = authorList.stream().map(FieldValue::of).collect(Collectors.toList());
                        queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(fieldValues))));
                        queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(authors))));
                        sourceBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
                    }
                }

            } catch (Exception e) {
                System.out.println(esSearchBO);
                e.printStackTrace();
            }
            bool.filter(sourceBool.build()._toQuery());
        }
        //默认查询赋值时间，防止默认查询范围很大的性能问题  如果传指定es index索引查询就不需要家默认查询时间
        if (CharSequenceUtil.isEmpty(esSearchBO.getStartTime()) && CharSequenceUtil.isEmpty(esSearchBO.getEndTime()) && null == esSearchBO.getIndexs()) {
            Date date = new Date();
            String end = DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN);
            String start = DateUtil.format(DateUtil.offsetDay(date, -1), DatePattern.NORM_DATETIME_PATTERN);
            esSearchBO.setStartTime(start);
            esSearchBO.setEndTime(end);
        }
        // 时间范围
        if (CharSequenceUtil.isNotEmpty(esSearchBO.getStartTime()) || CharSequenceUtil.isNotEmpty(esSearchBO.getEndTime())) {
            // yyyy-MM-dd HH:mm:ss = 10 + 1 + 8 转换yyyy-MM-dd HH:mm 和 yyyyMMddHHmmss
            String timeField;
            if (esSearchBO.getTimeType() == TimeTypeEnum.PUBLISH.getVal()) {
                timeField = EsBeanFieldEnum.PUBLISH_TIME.getFieldName();
            } else {
                timeField = EsBeanFieldEnum.UPDATE_TIME.getFieldName();
            }
            String finalTimeField = timeField;
            if (CharSequenceUtil.isNotEmpty(esSearchBO.getStartTime())) {
                if (esSearchBO.getStartTime().length() < 19) {
                    esSearchBO.setStartTime(DateUtil.format(DateUtil.parse(esSearchBO.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
                }
                Query range = QueryBuilders.range(r -> r.field(finalTimeField).from(esSearchBO.getStartTime()));
                bool.filter(range);
            }
            if (CharSequenceUtil.isNotEmpty(esSearchBO.getEndTime())) {
                if (esSearchBO.getEndTime().length() < 19) {
                    esSearchBO.setEndTime(DateUtil.format(DateUtil.parse(esSearchBO.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
                }
                Query range = QueryBuilders.range(r -> r.field(finalTimeField).to(esSearchBO.getEndTime()));
                bool.filter(range);
            }
        }

        Long businessId = esSearchBO.getBusinessId();
        if (null != esSearchBO.getIndexs() && null != businessId) {
            //浙江高院指定索引并且planId不为空的时候生效
            bool.filter(QueryBuilders.term(t -> t.field("planId").value(businessId)));
        }
        //量子二级分类逻辑
        String planIds = esSearchBO.getPlanIds();
        String targetTag = esSearchBO.getTargetTag();
        if (StrUtil.isNotEmpty(planIds)) {
            bool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.PLANIDS.getFieldName()).terms(q -> q.value(Arrays.stream(planIds.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
        }
        if (StrUtil.isNotEmpty(targetTag)) {
            if ("-1".equals(targetTag)) {
                //pc端默认查未分类的数据，排除掉分类过的数据
                mustNot.add(QueryBuilders.exists(s -> s.field(EsBeanFieldEnum.TAG_IDS.getFieldName())));
                mustNot.add(QueryBuilders.exists(s -> s.field(EsBeanFieldEnum.PLANIDS.getFieldName())));
            } else {
                bool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TAG_IDS.getFieldName()).terms(q -> q.value(Arrays.stream(targetTag.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
            }
        }
        //根据md5查询有多少相似的数据，量子需求
        String md5 = esSearchBO.getMd5();
        if (StrUtil.isNotEmpty(md5)) {
            bool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.MD5.getFieldName()).terms(q -> q.value(Arrays.stream(md5.split(",")).map(FieldValue::of).collect(Collectors.toList())))));
        }
        //此句永远放最后不要动！！！！防止漏掉mustNot
        if (!mustNot.isEmpty()) {
            //拼接最终的mustnot表达式，因为mustnot是并且的条件关系.前面的mustnot条件都是分开生效的.
            bool.mustNot(QueryBuilders.bool().should(mustNot).build()._toQuery());
        }
        return bool;
    }

    private static void buildSourceMapV2(EsSearchBO esSearchBO, BoolQuery.Builder bool, List<Query> mustNot) {
        if (!"1".equals(esSearchBO.getSourceSetting()) && !"2".equals(esSearchBO.getSourceSetting())) {
            return;
        }
        List<SourceMapV2VO> sourceMapV2 = esSearchBO.getSourceMapV2();
        if (CollUtil.isEmpty(sourceMapV2)) {
            return;
        }
        BoolQuery.Builder sourceBool = QueryBuilders.bool();
        for (SourceMapV2VO sourceMapV2VO : sourceMapV2) {
            String type = sourceMapV2VO.getType();
            List<String> host = sourceMapV2VO.getHost();
            List<String> author = sourceMapV2VO.getAuthor();
            List<String> sector = sourceMapV2VO.getSector();
            if (CharSequenceUtil.isBlank(type)) {
                continue;
            }
            List<Query> queryList = new ArrayList<>();
            queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(type)));
            if (CollUtil.isNotEmpty(host)) {
                queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName())
                        .terms(q -> q.value(host.stream().map(FieldValue::of).collect(Collectors.toList())))));
            }
            if (CollUtil.isNotEmpty(author)) {
                queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName())
                        .terms(q -> q.value(author.stream().map(FieldValue::of).collect(Collectors.toList())))));
            }
            if (CollUtil.isNotEmpty(sector)) {
                queryList.add(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.SECTOR.getFieldName())
                        .terms(q -> q.value(sector.stream().map(FieldValue::of).collect(Collectors.toList())))));
            }
            sourceBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
        }

        // 1 must  2 mustnot
        if ("1".equals(esSearchBO.getSourceSetting())) {
            bool.filter(sourceBool.build()._toQuery());
        } else {
            mustNot.add(sourceBool.build()._toQuery());
        }

    }

    /**
     * 获取排序条件
     *
     * @param sortValue
     * @return
     */
    public static SortOptions getSortOptions(String sortValue) {
        SortOptions sortOptions = null;
        SortTypeEnum sortTypeEnum = SortTypeEnum.getByValue(sortValue);
        switch (sortTypeEnum) {
            case TIME_ASC:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).order(SortOrder.Asc));
                break;
            case READ_NUM:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.READ_NUM.getFieldName()).order(SortOrder.Desc));
                break;
            case HOT_ASC:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.READ_NUM.getFieldName()).order(SortOrder.Asc));
                break;
            case RELATION_ASC:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.SCORE.getFieldName()).order(SortOrder.Asc));
                break;
            case UPDATE_TIME:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.UPDATE_TIME.getFieldName()).order(SortOrder.Desc));
                break;
            //互动数--10
            case INTERACT_NUM:
                sortOptions = SortOptionsBuilders.script(f -> f.type(ScriptSortType.Number).script(t -> t.inline(s -> s.source("def sum = 0;\n" +
                        "            if (doc['commentNum'].size() > 0) sum += doc['commentNum'].value;\n" +
                        "            if (doc['likeNum'].size() > 0) sum += doc['likeNum'].value;\n" +
                        "            if (doc['reprintNum'].size() > 0) sum += doc['reprintNum'].value;\n" +
                        "            return sum;").lang("painless"))).order(SortOrder.Desc));
                break;
            case REPRINT_NUM:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.REPRINT_NUM.getFieldName()).order(SortOrder.Desc));
                break;
            case COMMENT_NUM:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.COMMENT_NUM.getFieldName()).order(SortOrder.Desc));
                break;
            case LIKE_NUM:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.LIKE_NUM.getFieldName()).order(SortOrder.Desc));
                break;
            case PAGE_VIEW:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.PAGE_VIEW.getFieldName()).order(SortOrder.Desc));
                break;
            default:
                sortOptions = SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).order(SortOrder.Desc));
                break;
        }
        return sortOptions;

    }

    //    @SafeVarargs
    public static List<String> getIndexes(EsSearchBO esSearchBO) {
        List<String> indexs = esSearchBO.getIndexs();
        Integer projectType = esSearchBO.getProjectType();
        if (CollUtil.isNotEmpty(indexs)) {
            //如果指定索引名，优先查指定索引
            if (!"1".equals(esSearchBO.getInfoFlag()) && null != projectType && ProjectFlagEnum.QUANTUM.getValue() == projectType) {
                //如果是中电信量子项目并且是信息库的功能就直接查infolib
                return Collections.singletonList(ESConstant.ES_INDEX_INFOLIB);
            }
            return indexs;
        }
        return getIndexes(esSearchBO.getStartTime(), esSearchBO.getEndTime());

    }
    /**
     * 去重情况下的排序逻辑
     *
     * @param sortValue
     * @return
     */
    public static Aggregation getAggregationSort(String sortValue) {
        Aggregation aggSort ;
        if (SortTypeEnum.INTERACT_NUM.getVal().equals(sortValue)) {
            aggSort = AggregationBuilders.max(t ->t.script(c->c.inline(s->s.source("def sum = 0;\n" +
                    "            if (doc['commentNum'].size() > 0) sum += doc['commentNum'].value;\n" +
                    "            if (doc['likeNum'].size() > 0) sum += doc['likeNum'].value;\n" +
                    "            if (doc['reprintNum'].size() > 0) sum += doc['reprintNum'].value;\n" +
                    "            return sum;").lang("painless"))));
        }else  if (SortTypeEnum.REPRINT_NUM.getVal().equals(sortValue)){
            aggSort = AggregationBuilders.max(t -> t.field(EsBeanFieldEnum.REPRINT_NUM.getFieldName()));
        }else  if (SortTypeEnum.COMMENT_NUM.getVal().equals(sortValue)){
            aggSort = AggregationBuilders.max(t -> t.field(EsBeanFieldEnum.COMMENT_NUM.getFieldName()));
        }else  if (SortTypeEnum.LIKE_NUM.getVal().equals(sortValue)){
            aggSort = AggregationBuilders.max(t -> t.field(EsBeanFieldEnum.LIKE_NUM.getFieldName()));
        }else{
            //取时间最早的一条
            aggSort = AggregationBuilders.min(t -> t.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()));
        }
        return aggSort;
    }
    /**
     * 根据时间返回获取需要查询的索引*
     *
     * @param startTimeStr 开始时间
     * @param endTimeStr   结束时间
     * @return 结果
     */
//    @SafeVarargs
    public static List<String> getIndexes(String startTimeStr, String endTimeStr/*, List<String>...indexs*/) {
//        if (null!=indexs&&indexs.length>0&&null!=indexs[0]){
//            //如果指定索引名，优先查指定索引
//            return indexs[0];
//        }
        if (CharSequenceUtil.isEmpty(startTimeStr) && CharSequenceUtil.isEmpty(endTimeStr)) {
            startTimeStr = DateUtil.format(DateUtil.offsetDay(new Date(), -7), DatePattern.NORM_DATETIME_PATTERN);
            endTimeStr = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
        } else if (CharSequenceUtil.isNotEmpty(endTimeStr) && CharSequenceUtil.isEmpty(startTimeStr)) {
            endTimeStr = DateUtil.format(DateUtil.parse(endTimeStr), DatePattern.NORM_DATETIME_PATTERN);
            startTimeStr = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(endTimeStr), -7), DatePattern.NORM_DATETIME_PATTERN);
        } else {
            startTimeStr = DateUtil.format(DateUtil.parse(startTimeStr), DatePattern.NORM_DATETIME_PATTERN);
            endTimeStr = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
        }
        List<String> indexes = new ArrayList<>();
        if (CharSequenceUtil.isEmpty(startTimeStr) || CharSequenceUtil.isEmpty(endTimeStr)) {
            indexes.addAll(Arrays.asList(ESConstant.ES_INDEX, ESConstant.ES_INDEX_MONTH, ESConstant.ES_INDEX_WEEK));
        } else {
            Date now = new Date();
            Date oneWeekAgo = DateUtil.offsetDay(now, -7);
            Date oneMonthAgo = DateUtil.offsetDay(now, -30);
            Date startTime;
            Date endTime;
            Date start = DateUtil.parse(startTimeStr, BasicConstant.DATE_FORMAT_STR);
            Date end = DateUtil.parse(endTimeStr, BasicConstant.DATE_FORMAT_STR);
            if (end.before(start)) {
                startTime = end;
                endTime = start;
            } else {
                startTime = start;
                endTime = end;
            }
            //开始时间加三分钟防止时间卡点查询打到两个节点上
            startTime = DateUtil.offsetMinute(startTime, 3);
            if (startTime.after(oneWeekAgo)) {
                // 发文开始结束时间都在一周内
                indexes.add(ESConstant.ES_INDEX_WEEK);
            } else if (endTime.after(oneWeekAgo)) {
                indexes.add(ESConstant.ES_INDEX_WEEK);
                if (startTime.after(oneMonthAgo)) {
                    indexes.add(ESConstant.ES_INDEX_MONTH);
                } else {
                    indexes.add(ESConstant.ES_INDEX_MONTH);
                    indexes.add(ESConstant.ES_INDEX);
                }
            } else if (endTime.after(oneMonthAgo)) {
                indexes.add(ESConstant.ES_INDEX_MONTH);
                if (startTime.before(oneMonthAgo)) {
                    indexes.add(ESConstant.ES_INDEX);
                }
            } else {
                indexes.add(ESConstant.ES_INDEX);
            }
        }
        return indexes;

    }

    public static NamedValue<SortOrder> getBucketSort(String sortValue, String sortTimeAgg) {
        NamedValue<SortOrder> namedValue = null;
        SortTypeEnum sortTypeEnum = SortTypeEnum.getByValue(sortValue);
        switch (sortTypeEnum) {
            case TIME_DESC:
                namedValue = NamedValue.of(sortTimeAgg, SortOrder.Desc);
                break;
            case TIME_ASC:
                namedValue = NamedValue.of(sortTimeAgg, SortOrder.Asc);
                break;
            case SIMILAR_DESC:
                namedValue = NamedValue.of("_count", SortOrder.Desc);
                break;
            case PAGE_VIEW:
                namedValue = NamedValue.of(sortTimeAgg, SortOrder.Desc);
                break;
            default:
                namedValue = NamedValue.of(sortTimeAgg, SortOrder.Desc);
                break;
        }
        return namedValue;
    }

    public static Highlight getHighlight(int fragmentSize) {
        return Highlight.of(h -> h
                .fragmentSize(fragmentSize)
                .numberOfFragments(0)
                .fields(EsBeanFieldEnum.TEXT.getFieldName(), f -> f
                        .preTags("<em>")
                        .postTags("</em>")
                )
                .fields(EsBeanFieldEnum.TITLE.getFieldName(), f -> f
                        .preTags("<em>")
                        .postTags("</em>")
                ));
    }

    private static void buildTermsQueries(BoolQuery.Builder bool, String fieldName, String[] values) {
        if (values.length == 0) {
            return;
        }
        List<FieldValue> fieldValues = Arrays.stream(values).map(FieldValue::of).collect(Collectors.toList());
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(fieldName).terms(q -> q.value(fieldValues))));
        }
    }

    /**
     * 处理关键词和关键词位置条件
     *
     * @param keyWord         关键词
     * @param keyWordPosition 关键词位置
     * @return 返回值
     */
    public static QueryStringQuery.Builder buildKeyWord(String keyWord, String keyWordPosition) {
        QueryStringQuery.Builder kwQb = QueryBuilders.queryString();
        if (keyWord.contains("\"")) {
            kwQb.query(removeOuterQuotedNothing(keyWord));
        } else {
            kwQb.query(keyWord.trim());//模糊查询
        }
        setBuilderPosition(kwQb, keyWordPosition);
        return kwQb;
    }

    public static QueryStringQuery.Builder buildProWord(String keyWord, String keyWordPosition) {
        QueryStringQuery.Builder kwQb = QueryBuilders.queryString();
        kwQb.query(keyWord);
        setBuilderPosition(kwQb, keyWordPosition);
        return kwQb;
    }

    private static void setBuilderPosition(QueryStringQuery.Builder kwQb, String keyWordPosition) {
        // 处理关键词位置
        if (keyWordPosition == null) {
            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
        } else {
            String[] keyWordPositionArr = keyWordPosition.split(",");
            if (keyWordPositionArr.length > 0) {
                for (String str : keyWordPositionArr) {
                    switch (SearchPositionEnum.getByValue(str)) {
                        case TITLE:
                            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName());
                            break;
                        case TEXT:
                            kwQb.fields(EsBeanFieldEnum.TEXT.getFieldName());
                            break;
                        case AUTHOR:
                            kwQb.fields(EsBeanFieldEnum.AUTHOR.getFieldName());
                            break;
                        case ALL:
                            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName(), EsBeanFieldEnum.AUTHOR.getFieldName());
                            break;
                        default:
                            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName());
                            break;
                    }
                }
            }
        }
    }

    public static String quotedEveryKeyWord(String keyWord, boolean accurate) {
        if (!accurate) {
            return keyWord;
        }
        List<String> quotedList = new ArrayList<>();

        Pattern pattern = Pattern.compile("\"(.*?)\"");
        Matcher matcher = pattern.matcher(keyWord);

        StringBuilder remainingText = new StringBuilder(keyWord.length());

        while (matcher.find()) {
            quotedList.add(matcher.group());

            // 从原字符串中移除已匹配的部分
            int start = matcher.start();
            int end = matcher.end();
            remainingText.append(keyWord, 0, start);
            remainingText.append(keyWord, end, keyWord.length());
            // 更新keyWord为剩余未处理的部分
            keyWord = remainingText.toString();
            // 重置matcher以匹配剩余部分
            matcher.reset(keyWord);
            // 清空StringBuilder
            remainingText.setLength(0);
        }
        // 输出剩余的字符串（不含引号包裹的部分）
        if (keyWord.length() > 0) {
            keyWord = "\"" + keyWord.trim().replaceAll(" ", "\" \"").replaceAll("\"\"", "") + "\"";
        }

        return CollUtil.join(quotedList, " ").replaceAll("\"\"", "") + " " + keyWord;
    }

    public static void main(String[] args) {
//        System.out.println(getIndexes("2024-05-28 13:00:00","2024-06-04 13:00:00"));
//        String a = "安徽 + 合肥 + 蜀山区 + 大蜀山";
//        String b = "(安徽 + 合肥) | (蜀山区 + 高新区) | (包河 + (巢湖 | 滨湖))  + 大蜀山 ";
//        String c = "(合肥|庐江|巢湖|肥东|肥西|长丰|江苏|浙江|上海|武汉|长沙|浏阳|宁乡|南昌|安义|进贤)+(科技|技术|科创|创新|量子|半导体|新材料|创新药|人工智能|集成电路|光伏|新能源|锂电池|锂离子)+(合作|签约|签署)";
//        Query q1 = proSearchBuilder(a, 0 , null);
//        Query q2 = proSearchBuilder(b, 0 , null);
//        Query q3 = proSearchBuilder(c, 0 , null);
//        System.out.println(1);
        String a = "1&2";
        String a2 = "1&";
        String a3 = "&2";
        System.out.println(JSONUtil.parseArray(a.split("&")));
        System.out.println(JSONUtil.parseArray(a2.split("&")));
        System.out.println(JSONUtil.parseArray(a3.split("&")));
    }

    public static BoolQuery.Builder buildManyTypeQuery(EsSearchBO esSearchBO) {
        BoolQuery.Builder bool = QueryBuilders.bool();
        buildTypeAndKeyWord(esSearchBO, bool);

        // 二次检索词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticWord())) {
            QueryStringQuery.Builder keyWordQb = buildKeyWord(esSearchBO.getQuadraticWord(), esSearchBO.getSearchPosition());
            bool.must(keyWordQb.build()._toQuery());
        }

        // 处理排除id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeId())) {
            String excludeId = esSearchBO.getExcludeId();
            List<FieldValue> list = Arrays.stream(excludeId.split(","))
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ID.getFieldName()).terms(q -> q.value(list))));
        }
        // 处理排除词过滤词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord()) || CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
            StringBuilder stringBuilder = new StringBuilder();
            if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord())) {
                stringBuilder.append(esSearchBO.getExcludeWord());
            }
            if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
                stringBuilder.append(esSearchBO.getQuadraticFilterWord());
            }
            String[] keyWordArr = stringBuilder.toString().split("\\s+");
            List<String> keyWordList = new ArrayList<>();
            for (String str : keyWordArr) {
                keyWordList.add("\"" + str + "\"");
            }
            String keyword = StringUtils.join(keyWordList, " ");
            QueryStringQuery.Builder queryString = QueryBuilders.queryString();
            queryString.query(keyword).defaultOperator(Operator.Or);
            bool.mustNot(queryString.build()._toQuery());
        }
        // 处理是否原创条件
        if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
            bool.must(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_ORIGINAL.getFlag())));
        } else if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_NOT_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
            bool.must(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_NOT_ORIGINAL.getFlag())));
        }

        // 处理定向id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getId())) {
            buildTermsQueries(bool, EsBeanFieldEnum.ID.getFieldName(), esSearchBO.getId().split(","));
        }
        // 情感
        if (CharSequenceUtil.isNotBlank(esSearchBO.getEmotionFlag())) {
            buildTermsQueries(bool, EsBeanFieldEnum.EMOTION_FLAG.getFieldName(), esSearchBO.getEmotionFlag().split(","));
        }
        // 域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getHost())) {
            buildTermsQueries(bool, EsBeanFieldEnum.HOST.getFieldName(), esSearchBO.getHost().split(","));
        }
        // 排除域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeHost())) {
            List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getExcludeHost().split(",")).map(FieldValue::of).collect(Collectors.toList());
            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(fieldValues))));
        }
        // 信息分类标签
        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentMeta())) {
            buildTermsQueries(bool, EsBeanFieldEnum.CONTENT_META.getFieldName(), esSearchBO.getContentMeta().split(","));
        }
        // 作者
        if (CharSequenceUtil.isNotBlank(esSearchBO.getAuthor())) {
            buildTermsQueries(bool, EsBeanFieldEnum.AUTHOR.getFieldName(), esSearchBO.getAuthor().split(","));
        }
        // 定向信源
        if ("1".equals(esSearchBO.getSourceSetting()) || "2".equals(esSearchBO.getSourceSetting())) {
            BoolQuery.Builder sourceBool = QueryBuilders.bool();
            Map<String, String> sourceMap = esSearchBO.getSourceMap();
            for (Map.Entry<String, String> entry : sourceMap.entrySet()) {
                // 按照类型查询命中的author或host
                List<Query> queryList = new ArrayList<>();
                queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(entry.getKey())));
                queryList.add(
                        QueryBuilders.bool().should(
                                        Arrays.asList(
                                                QueryBuilders.term(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).value(entry.getValue())),
                                                QueryBuilders.term(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).value(entry.getValue()))
                                        )
                                )
                                .build()
                                ._toQuery()
                );
                sourceBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
            }
            // 1 must  2 mustnot
            if ("1".equals(esSearchBO.getSourceSetting())) {
                bool.must(sourceBool.build()._toQuery());
            } else {
                bool.mustNot(sourceBool.build()._toQuery());
            }
        }

        // 时间范围
        String timeField;

        if (esSearchBO.getTimeType() == TimeTypeEnum.PUBLISH.getVal()) {
            timeField = EsBeanFieldEnum.PUBLISH_TIME.getFieldName();
        } else {
            timeField = EsBeanFieldEnum.UPDATE_TIME.getFieldName();
        }
        String finalTimeField = timeField;
        Query range = QueryBuilders.range(r -> r.field(finalTimeField).from(esSearchBO.getStartTime()).to(esSearchBO.getEndTime()));
        bool.must(range);
        return bool;
    }

    private static void buildTypeAndKeyWord(EsSearchBO esSearchBO, BoolQuery.Builder bool) {
        // 拼接关键词
        if (esSearchBO.getConfigSelect() == 1) {
            // 专业搜索
            String[] wordArr = esSearchBO.getProWord().split("\\+");
            for (String word : wordArr) {
                word = word.substring(1, word.length() - 1);
                word = word.replace("|", " ").replaceAll("\\(", "").replaceAll("\\)", "");
                word = quotedEveryKeyWord(word, esSearchBO.getAccurate());
                MultiMatchQuery.Builder multiMatchBuilder = QueryBuilders.multiMatch().operator(Operator.Or).type(TextQueryType.BestFields).query(word);
                if (esSearchBO.getSearchPosition() == null) {
                    multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
                } else {
                    String[] keyWordPositionArr = esSearchBO.getSearchPosition().split(",");
                    if (keyWordPositionArr.length > 0) {
                        for (String str : keyWordPositionArr) {
                            switch (SearchPositionEnum.getByValue(str)) {
                                case TITLE:
                                    multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName());
                                    break;
                                case TEXT:
                                    multiMatchBuilder.fields(EsBeanFieldEnum.TEXT.getFieldName());
                                    break;
                                case AUTHOR:
                                    multiMatchBuilder.fields(EsBeanFieldEnum.AUTHOR.getFieldName());
                                    break;
                                default:
                                    multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
                                    break;
                            }
                        }
                    }
                }
                bool.must(multiMatchBuilder.build()._toQuery());
            }
            return;
        }
        // type里含有音视频（8 9）时， 音视频的词位置只在text, 无论传参的词位置含有什么

        String type = esSearchBO.getType();
        // 处理媒体类型
        if (StrUtil.isBlankIfStr(type)) {
            return;
        }
        BoolQuery.Builder allBool = QueryBuilders.bool();

        List<String> typeList = CharSequenceUtil.splitTrim(type, ",");
        List<String> typeMediaList = typeList.stream().filter(item -> item.contains("8") || item.contains("9"))
                .collect(Collectors.toList());
        List<String> typeOtherList = typeList.stream().filter(item -> !item.contains("8") && !item.contains("9"))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(typeMediaList)) {
            BoolQuery.Builder typeWordBool = QueryBuilders.bool();
            buildKeyWordPosition(esSearchBO, typeWordBool, PositionEnum.TEXT.getInfo());
            typeWordBool.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName())
                    .terms(q -> q.value(typeMediaList.stream().map(FieldValue::of).collect(Collectors.toList())))));
            allBool.should(typeWordBool.build()._toQuery());
        }
        if (CollUtil.isNotEmpty(typeOtherList)) {
            BoolQuery.Builder typeWordBool = QueryBuilders.bool();
            String searchPosition = esSearchBO.getSearchPosition();
            buildKeyWordPosition(esSearchBO, typeWordBool, searchPosition);
            typeWordBool.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName())
                    .terms(q -> q.value(typeOtherList.stream().map(FieldValue::of).collect(Collectors.toList())))));
            allBool.should(typeWordBool.build()._toQuery());
        }
        bool.must(allBool.build()._toQuery());

    }

    private static void buildKeyWordPosition(EsSearchBO esSearchBO, BoolQuery.Builder typeWordBool, String searchPosition) {
        // 非专业搜索
        if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord1())) {
            QueryStringQuery.Builder keyWordQb1 = buildKeyWord(esSearchBO.getKeyWord1(), searchPosition);
            typeWordBool.must(keyWordQb1.build()._toQuery());
        }
        if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord2())) {
            QueryStringQuery.Builder keyWordQb2 = buildKeyWord(esSearchBO.getKeyWord2(), searchPosition);
            typeWordBool.must(keyWordQb2.build()._toQuery());
        }
        if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord3())) {
            QueryStringQuery.Builder keyWordQb3 = buildKeyWord(esSearchBO.getKeyWord3(), searchPosition);
            typeWordBool.must(keyWordQb3.build()._toQuery());
        }
        if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord4())) {
            QueryStringQuery.Builder keyWordQb4 = buildKeyWord(esSearchBO.getKeyWord4(), searchPosition);
            typeWordBool.must(keyWordQb4.build()._toQuery());
        }
    }

    private static String removeOuterQuotedNothing(String expression) {
        Pattern pattern = Pattern.compile("\"\\s+\"");
        List<String> results = new ArrayList<>();
        int balance = 0; // 用于跟踪引号平衡
        char[] chars = expression.toCharArray();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < chars.length; i++) {
            //引号中的内容
            if (balance == 1) {
                builder.append(chars[i]);
            }
            //引号结束 排除每个空的关键词
            if ('\"' == chars[i] && balance == 1) {
                balance = 0;
                String word = builder.toString();
                if (!"\"\"".equals(word)) {
                    results.add(word);
                }
                builder = new StringBuilder();
            }
            //引号开始
            if ('\"' == chars[i] && balance == 0) {
                balance = 1;
                builder.append(chars[i]);
            }
        }
        List<String> kwlist = new ArrayList<>();
        if (results.isEmpty()) {
            return expression;
        } else {
            for (String result : results) {
                Matcher matcher = pattern.matcher(result);
                if (!matcher.find()) {
                    kwlist.add(result);
                }
            }
            return CollUtil.join(kwlist, " OR ");
        }

    }

    // 拆解专业搜索表达式+
    public static List<String> decomposeExpression(String expression) {
        List<String> parts = new ArrayList<>();
        int balance = 0; // 用于跟踪括号的平衡
        StringBuilder part = new StringBuilder();

        for (char ch : expression.toCharArray()) {
            if (ch == '(') {
                balance++;
                if (balance == 1) { // 新的子表达式的开始
                    if (part.length() > 0) {
                        parts.add(part.toString());
                        part = new StringBuilder();
                    }
                    continue; // 忽略这个括号
                }
            } else if (ch == ')') {
                balance--;
                if (balance == 0) { // 子表达式的结束
                    parts.add(part.toString());
                    part = new StringBuilder();
                    continue; // 忽略这个括号
                }
            } else if (balance == 0 && (ch == '+' || ch == '|')) {
                if (part.length() > 0) {
                    parts.add(part.toString());
                    part = new StringBuilder();
                }
                continue; // 忽略操作符
            }
            if (balance > 0 || ch != ' ') { // 忽略外部的空格
                part.append(ch);
            }
        }
        if (part.length() > 0) {
            parts.add(part.toString());
        }
        return parts;
    }

    // 拆解专业搜索表达式|
    public static List<String> splitExpression(String expression) {
        List<String> parts = new ArrayList<>();
        int balance = 0; // 用于跟踪括号的平衡
        StringBuilder part = new StringBuilder();

        for (char ch : expression.toCharArray()) {
            if (ch == '(') {
                balance++;
            } else if (ch == ')') {
                balance--;
            }

            // 当我们不在括号内时，检查是否遇到了逻辑或（OR）操作符
            if (balance == 0 && ch == '|') {
                parts.add(part.toString().trim());
                part = new StringBuilder();
            } else {
                part.append(ch);
            }
        }

        // 添加最后一个部分（如果有）
        if (part.length() > 0) {
            parts.add(part.toString().trim());
        }

        return parts;
    }

    // 去除最外层的括号
    public static String removeOuterParentheses(String expr) {
        if (expr.startsWith("(") && expr.endsWith(")")) {
            int depth = 0;
            for (int i = 0; i < expr.length() - 1; i++) {
                if (expr.charAt(i) == '(') {
                    depth++;
                } else if (expr.charAt(i) == ')') {
                    depth--;
                    if (depth == 0 && i != expr.length() - 2) {
                        // 如果在字符串末尾之前就达到了深度为0，说明不是最外层的括号
                        return expr;
                    }
                }
            }
            // 去除最外层的括号 递归调用看是否存在下一层  即  ((( (A + (B)) + C )))
            return removeOuterParentheses(expr.substring(1, expr.length() - 1));
        }
        return expr;
    }

    // 查找最外侧的分隔符
    public static int findOuterOperator(String expr, char operator) {
        int depth = 0;
        for (int i = 0; i < expr.length(); i++) {
            char ch = expr.charAt(i);
            if (ch == '(') {
                depth++;
            } else if (ch == ')') {
                depth--;
            } else if (ch == operator && depth == 0) {
                return i;
            }
        }
        return -1; // 如果没有找到操作符，返回-1
    }

    // 创建专业查询
    public static Query proSearchBuilder(String proWord, int queryType, String position) {
        proWord = removeOuterParentheses(proWord);
        List<String> parts = new ArrayList<>();
        // 处理有括号 且 括号外侧有|的专业词
        if (proWord.contains("(") && findOuterOperator(proWord, '|') != -1) {
            // 处理should
            parts = splitExpression(proWord);
            queryType = 1;
            // 处理有括号 且 括号外侧有+的专业词
        } else if (proWord.contains("(")) {
            // 处理must
            parts = decomposeExpression(proWord);
            queryType = 0;
            // 处理没有括号但同时有+和|的专业词
        } else if (proWord.contains("+") && proWord.contains("|")) {
            // 处理should
            parts = splitExpression(proWord);
            queryType = 1;
            // 处理没有括号但有+的专业词
        } else if (proWord.contains("+") && !proWord.contains("|")) {
            // 处理must
            parts = Arrays.asList(proWord.split("\\+"));
            queryType = 0;
        } else {
            // 此处根据queryType来设置should或者must查询 此时proWord只会有单个词 A 或 A|B|C 样的词
            if (queryType == 0) {
                return QueryBuilders.bool().must(getProQueryList(proWord, position)).build()._toQuery();
            } else {
                return QueryBuilders.bool().should(getProQueryList(proWord, position)).build()._toQuery();
            }
        }
        List<Query> queryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(parts)) {
            for (String s : parts) {
                queryList.add(proSearchBuilder(s, queryType, position));
            }
        }
        if (queryType == 0) {
            return QueryBuilders.bool().must(queryList).build()._toQuery();
        } else {
            return QueryBuilders.bool().should(queryList).build()._toQuery();
        }
    }

    private static List<Query> getProQueryList(String proWord, String position) {
        List<String> wordList = new ArrayList<>();
        if (proWord.contains("|")) {
            wordList.add(proWord.replaceAll("\\|", " "));
        } else {
            wordList.add(proWord);
        }
        List<Query> queryList = new ArrayList<>();
        for (String s : wordList) {
            queryList.add(buildKeyWord(quotedEveryKeyWord(s, true), position).build()._toQuery());
        }
        return queryList;
    }

//    public static String proSearchBuilder(String proWord) {
//        char[] words = proWord.replaceAll("\"", "").toCharArray();
//        StringBuilder resultBulider = new StringBuilder();
//        StringBuilder wordBuilder = new StringBuilder();
//        for (char c : words) {
//            switch (c) {
//                case '+':
//                    if (wordBuilder.toString().trim().length() > 0) {
//                        resultBulider.append("\"").append(wordBuilder.toString().trim()).append("\"");
//                        wordBuilder = new StringBuilder();
//                    }
//                    resultBulider.append(" AND ");
//                    break;
//                case '|':
//                    if (wordBuilder.toString().trim().length() > 0) {
//                        resultBulider.append("\"").append(wordBuilder.toString().trim()).append("\"");
//                        wordBuilder = new StringBuilder();
//                    }
//                    resultBulider.append(" OR ");
//                    break;
//                case '(':
//                    resultBulider.append(" ( ");
//                    break;
//                case ')':
//                    if (wordBuilder.toString().trim().length() > 0) {
//                        resultBulider.append("\"").append(wordBuilder.toString().trim()).append("\"");
//                        wordBuilder = new StringBuilder();
//                    }
//                    resultBulider.append(" ) ");
//                    break;
//                default:
//                    wordBuilder.append(c);
//                    break;
//            }
//        }
//        if (wordBuilder.length() > 0) {
//            resultBulider.append("\"").append(wordBuilder.toString().trim()).append("\"");
//        }
//        return resultBulider.toString();
//    }

    public static void simpleTermQuery(List<String> value, UpdateByQueryRequest.Builder builder, String filed) {
        if (CollUtil.isEmpty(value)) {
            return;
        }
        List<FieldValue> fieldValues = CollStreamUtil.toList(value, FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            Query terms = QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues)));
            builder.query(terms.terms()._toQuery());
        }

    }
}
