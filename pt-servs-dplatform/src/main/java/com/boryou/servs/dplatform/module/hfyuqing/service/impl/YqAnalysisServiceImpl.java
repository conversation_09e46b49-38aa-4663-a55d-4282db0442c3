package com.boryou.servs.dplatform.module.hfyuqing.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.boryou.servs.dplatform.constant.ESConstant;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.module.hfyuqing.service.YqAnalysisService;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2024/4/25 16:47
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class YqAnalysisServiceImpl implements YqAnalysisService {

    private final ElasticsearchClient esClient;

    @Override
    public Map<String, Long> getStatisticData(StatisticsBean statisticsBean) {
        AreaDistributionService areaDistributionService = SpringUtil.getBean(AreaDistributionService.class);
        BoolQuery.Builder bool = areaDistributionService.setCommonSearch(statisticsBean);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime today = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0, 0);
        LocalDateTime yesterday = today.minusDays(1);
        LocalDateTime sevenDaysAgo = now.minusDays(7);
        LocalDateTime thirtyDaysAgo = now.minusDays(30);

        List<DateRangeExpression> range = new ArrayList<>();
        // 今天
        range.add(new DateRangeExpression.Builder()
                .from(builder -> builder.value((double) (today.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .to(builder -> builder.value((double) (now.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .key("today").build());
        // 昨天
        range.add(new DateRangeExpression.Builder()
                .from(builder -> builder.value((double) (yesterday.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .to(builder -> builder.value((double) (today.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .key("yesterday").build());
        // 七天
        range.add(new DateRangeExpression.Builder()
                .from(builder -> builder.value((double) (sevenDaysAgo.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .to(builder -> builder.value((double) (now.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .key("week").build());
        // 30天
        range.add(new DateRangeExpression.Builder()
                .from(builder -> builder.value((double) (thirtyDaysAgo.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .to(builder -> builder.value((double) (now.toEpochSecond(ZoneOffset.UTC) * 1000)))
                .key("month").build());


        SearchResponse<Integer> response = null;
        String aggNameKey = "publish_time" + "Agg";
        try {
            response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(statisticsBean.getStartTime(),statisticsBean.getEndTime())).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .aggregations(aggNameKey, a -> a.dateRange(x -> x.field("publishTime").ranges(range))
                                    .aggregations("type_buckets", b -> b.terms(x -> x.field("type")))
                            )
                            .size(0), Integer.class);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        Map<String, Long> data = new HashMap<>(50);
        Map<String, Aggregate> aggregations = response.aggregations();
        Aggregate aggregate = aggregations.get(aggNameKey);
        List<RangeBucket> array = ((DateRangeAggregate) aggregate._get()).buckets().array();
        for (RangeBucket bucket : array) {
            List<LongTermsBucket> typeBuckets = ((LongTermsAggregate) bucket.aggregations().get("type_buckets")._get()).buckets().array();
            typeBuckets.forEach(typeBucket -> {
                data.put(bucket.key() + "_" + typeBucket.key(), typeBucket.docCount());
            });

        }
        return data;
    }
}
