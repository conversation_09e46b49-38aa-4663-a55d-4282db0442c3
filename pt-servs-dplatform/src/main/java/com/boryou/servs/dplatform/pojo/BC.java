package com.boryou.servs.dplatform.pojo;

/**
 * 基础常量（BC：BasicConstant）
 *
 * <AUTHOR>
 */
public interface BC {

    String ERR_WLGQ = "!_我勒个去_WLGQ_!";

    String YES = "1";

    Integer YES_NUM = 1;

    String NO = "0";

    Integer NO_NUM = 0;

    Integer ASC = 1;

    Integer DESC = -1;

    String OS_WIN = "windows";

    String LOCAL_IP = "127.0.0.1";

    String HTTP_HEAD = "http://";

    String HTTPS_HEAD = "https://";

    String COMMA_EN = ",";

    String COLON_EN = ":";

    String DASH = "-";

    String UNDERLINE = "_";
    String NEWLINE = "\n";
    String A_SPACE = " ";
    String ARROW_LEFT = "<-";
    String ARROW_RIGHT = "->";

    String JOIN_TITLE_TEXT = "#_=I.i#";
    int JOIN_TITLE_TEXT_LEN = 7;

    String ZERO_STR = "0";
    int ZERO = 0;
    int ONE = 1;
    int TWO = 2;
    int THREE = 3;
    int FOUR = 4;
    int FIVE = 5;
    int SIX = 6;
    int SEVEN = 7;
    int EIGHT = 8;
    int NINE = 9;
    int TEN = 10;
    int FIFTEEN = 15;
    int EIGHTEEN = 18;
    int TWENTY = 20;
    int THIRTY = 30;
    int FORTY = 40;
    int FIFTY = 50;
    int SIXTY = 60;
    int MINUS_ONE = -1;
    int MINUS_NINETEEN = -19;

    int ONE_HUNDRED = 100;
    int TWO_HUNDRED = 200;
    int THREE_HUNDRED = 300;
    int FOUR_HUNDRED = 400;
    int FIVE_HUNDRED = 500;
    int ONE_THOUSAND = 1000;
    int TWO_THOUSAND = 2000;
    int THREE_THOUSAND = 3000;
    int FOUR_THOUSAND = 4000;
    int FIVE_THOUSAND = 5000;
    int SIX_THOUSAND = 6000;
    int SEVEN_THOUSAND = 7000;
    int TEN_THOUSAND = 10000;
    int TWELVE_THOUSAND = 12000;
    int FIFTEEN_THOUSAND = 15000;
    int TWENTY_THOUSAND = 20000;

    int SIXTY_THOUSAND = 60000;
    int THREE_HUNDRED_THOUSAND = 300000;
    int THREE_MILLION = 3000000;

    int ONE_DAY_MS = 86400000;
    int TEN_DAY_MS = 864000000;

    String MIN_PUBLISH_TIME_STR = "2000-01-01 00:00:00";
    String FORMAT_YMDHMS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 项目Redis公共前缀
     */
    String PROJECT_PREFIX = "netxman-data:";

    /**
     * other 项目Redis公共前缀
     */
    String OTHER_PROJECT_PREFIX = "netxman:";

    String UNDEFINED = "未定义项目标识";
}
