package com.boryou.servs.dplatform.module.hfyuqing.enums;


public enum FieldEnum {
    ID("id", "索引id"),
    ALL("all", "目前的包含标题、正文、作者"),
    TITLE("title", "标题"),
    TEXT("text", "正文"),
    AUTHOR("author", "作者"),
    ORIGINAL("original", "原创"),
    TYPE("type", "信息类型"),
    URL("url", "文章链接"),
    FORWARD("forward", "微博数据特有的"),
    SOURCEWbId("sourceWbId", "微博来源id"),
    SPAM_INFO("spamFlag", "垃圾信息"),
    EMOTION("emotional", "情感倾向"),
    HOST("host", "站点"),
    SITE_META("siteMeta", "站点标签"),
    MD5("MD5", "文章的MD5值"),
    MAP_PATH("mapPath", "源码的hdfs路径"),
    READ_NUM("readNum", "阅读数"),
    COMMENT_NUM("commentNum", "评论数"),
    COMMENT_GOODS_NUM("commentGoodsNum", "点赞数"),
    AUTHOR_PORTRAIT_LINK("authorPortraitLink", "作者头像链接或封面链接"),
    PROGRAM_NAME("programName", "节目名"),
    DATE("date", "原文日期"),
    TIME("publishTime", "原文时间"),
    SUBMIT_TIME("submitTime", "采集时间"),
    PIC_URL("picUrl", "图片链接"),
    QQ_GROUP_ID("qqGroupId", "qq群id"),
    QQ_GROUP_NAME("qqGroupName", "qq群名"),
    QQ_ID("qqId", "qq号"),
    SCORE("score", "solr自带返回分数"),
    SITE_ADDRESS("siteAddress", "站点地址"),
    TEXT_ADDRESS("textAddress", "文中地址"),
    ALGORITHM_CLASSIFY("algorithmClassify", "算法分类"),
    BUSINESS_TYPE("businessType", "商机类型"),
    DELETED("deleted", "是都删除"),
    ARTICLE_SOURCE("articleSource", "文章来源"),
    REPRINT_NUM("reprintNum", "转载数"),
    WEIXIN_FUNC("weixinFunc", "微信号功能简介"),
    LANGUAGE("language", "语言"),
    AV_TYPE("avType", "音视频类型"),
    PARENT_URL("parentUrl", "图片的网页链接"),
    PIC_DESCRIPTION("picDescription", "图片描述"),
    CONTENT_TYPE("contentType", "商品类型"),
    PRICE("price", "商品的价格"),
    COMMODITY("commodity", "商品的详情"),
    AVERAGE_SCORE("averageScore", "平均星级"),
    BID("bid", "招投标分类"),
    BID_TYPE("bidType", "招投标项目类别"),
    INVITE_UNIT("inviteUnit", "采购单位"),
    BUDGET("budget", "预算"),
    WIN_UNIT("winUnit", "中标单位"),
    BID_OPEN_TIME("bidOpenTime", "开标时间"),
    BID_START_TIME("bidStartTime", "投标开始时间"),
    BID_END_TIME("bidEndTime", "投标结束时间"),
    TEL("tel", "联系方式"),
    PERSONNEL_CHANGE_TYPE("personnelChangeType", "人事变动类型"),
    CUSTOMER("customer", "客户类型"),
    UPDATE_STATUS("updateStatus", "网页更新状态"),
    CHILD_TYPE("childType", "子类型，type1和type16"),
    SIM_HASHS("simhashs", "用于存储simhash片段集合"),
    IS_DELETED("deleted", "是否删除"),
    CLASSIFY("classify", "聚类类别"),
    AUTHOR_ID("authorId", "作者id"),
    GEO_LOCARION("geoLocation", "地理定位地域码"),
    CNUEF_EDU_TYPE("cnuefEduType", "首师大教育类型"),
    EDU_WORD_NUM("eduWordNum", "教育词数量"),
    WB_ORIGINAL("wbOriginal", "微博是否原创");

    private final String name;
    private final String description;

    private FieldEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public String getExcludeName() {
        return "-" + this.name;
    }

    public String toString() {
        return this.name;
    }
}
