package com.boryou.servs.dplatform.module.zhejiangyuqing.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.zhejiangyuqing.service.home.SearchService;
import com.boryou.servs.dplatform.pojo.R;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 高院查询控制器
 *
 * <AUTHOR>
 * @date 2024-05-24 17:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zhejiangyuqing")
public class SearchController {

    private final SearchService searchService;

    /**
     *相似度查询
     *  @param esSearchBO 搜索BO
     * @return java.util.List<com.boryou.servs.dplatform.module.zhejiangyuqing.bo.AreaOverviewBO>
     * <AUTHOR>
     * 2024/5/27 14:57
     **/
    @PostMapping("/search/similarity")
    public R saveHistoryPlan(@RequestBody EsSearchBO esSearchBO) {
        try {
            PageResult<EsBean> flag = searchService.similarityQuery(esSearchBO);
            return R.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error();
        }
    }


}

