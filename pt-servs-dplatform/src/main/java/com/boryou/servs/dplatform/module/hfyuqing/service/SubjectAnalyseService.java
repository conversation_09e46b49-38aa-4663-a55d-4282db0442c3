package com.boryou.servs.dplatform.module.hfyuqing.service;

import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-29 15:24
 */
public interface SubjectAnalyseService {

    /**
     * 站点分布
     * @param statisBean
     * @return
     */
    Map<String, Long> getHostData(StatisticsBean statisBean);

    /**
     * 时间趋势
     * @param statisBean
     * @return
     */
    Map<String, Long> getTimeTrendMap(StatisticsBean statisBean);

    /**
     * 媒体频道
     * @param statisBean
     * @return
     */
    Map<String, Long> getMediaMapType(StatisticsBean statisBean);

    /**
     * 获取媒体饼状图
     * @param statisBean
     * @return
     */
    Map<String, Long> getMediaPie(StatisticsBean statisBean);

    /**
     * 对外接口-时间趋势
     *
     * @param bo
     * @return
     */
    Map<String, Map<String, Integer>> timeTrend(EsSearchBO bo);

    /**
     * 舆情大屏-时间趋势
     *
     * @param statisBean
     * @return
     */
    Map<String, Integer[]> getTimeTrendMap24H(StatisticsBean statisBean);

    /**
     * 博约舆情-首页-一周走势图
     * @param bo
     * @return
     */
    Integer[] getWeekCount(EsSearchBO bo);
}

