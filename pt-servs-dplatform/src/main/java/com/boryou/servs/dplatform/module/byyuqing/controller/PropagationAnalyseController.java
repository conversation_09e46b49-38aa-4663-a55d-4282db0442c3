//package com.boryou.servs.dplatform.module.byyuqing.controller;
//
//import cn.hutool.json.JSONUtil;
//import com.boryou.servs.dplatform.module.byyuqing.bean.AnalyseRequire;
//import com.boryou.servs.dplatform.module.byyuqing.bean.SectionData;
//import com.boryou.servs.dplatform.module.byyuqing.util.CommonUtil;
//import com.boryou.servs.dplatform.module.byyuqing.util.PropagateUtil;
//import lombok.RequiredArgsConstructor;
//
//import java.util.*;
//
///**
// *传播路径-eventcache
// * <AUTHOR>
// * @date 2024-05-14 14:16
// */
//@RequiredArgsConstructor
//public class PropagationAnalyseController {
//
//    private PropagateUtil propagateUtil;
//
//
//    public String computerData(AnalyseRequire analyseRequire) {
//        Map<String, Object> map = new HashMap<>();
//        String startTime = propagateUtil.getStartTime(analyseRequire);
//        Date dateStart = CommonUtil.stringToDateLongFormate(startTime);
//        Date dateEnd = CommonUtil.stringToDateLongFormate(analyseRequire.getEndTime());
//        int pageSize = 20;
//        List<SectionData> sections = new ArrayList<>();
//        List<Date> dateAxis = PropagateUtil.getTimeAxis(4, dateStart, dateEnd);
//
//        if (dateAxis != null && dateAxis.size() > 1) {
//            for (int i = 0; i < dateAxis.size() - 1; i++) {
//                // 计算区间数据 0-1,1-2,2-3,3-4
//                sections.add(propagateUtil.getPathMapInfo(analyseRequire, dateAxis.get(i), dateAxis.get(i + 1), pageSize * 5));
//            }
//        }
//        List<Map<String, Object>> nodes = new ArrayList<>();
//        List<Map<String, Object>> links = new ArrayList<>();
//        Map<String, String> hostFilter = new HashMap<>();
//        Map<Integer, Integer> rootIndex = new HashMap<>();
//        int index = 0;
//        int category = 0;
//        if (sections.size() > 1) {
//            // 找到数量最多的节点所在区域
//            int maxPostion = PropagateUtil.getMaxPosition(sections);
//            if (maxPostion != 0) {
//                // 优先处理最早点
//                rootIndex.put(0, index);
//                index = PropagateUtil.bean2nodes(dateAxis.get(0), dateAxis.get(0 + 1), hostFilter, nodes, links, index, 4,
//                        sections.get(0), pageSize, sections.size());
//            }
//            // 再处理爆发点，数据量会大一些
//            SectionData section = sections.get(maxPostion);
//            category = 2;
//            rootIndex.put(maxPostion, index);
//            index = PropagateUtil.bean2nodes(dateAxis.get(maxPostion), dateAxis.get(maxPostion + 1), hostFilter, nodes, links,
//                    index, category, section, pageSize, sections.size());
//            // 对每段内容进行遍历存储（普通节点）
//            PropagateUtil.handDateIntevel(dateAxis, sections, hostFilter, nodes, links, maxPostion, index, rootIndex, pageSize,
//                    sections.size());
//
//            // 处理区间根节点之间的连接线
//            PropagateUtil.linkRoots(nodes, links, rootIndex);
//        }
//        map.put("nodes", nodes);
//        map.put("links", links);
//        return JSONUtil.toJsonStr(map);
//    }
//}
//
