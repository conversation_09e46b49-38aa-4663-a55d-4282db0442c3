package com.boryou.servs.dplatform.module.warn.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.warn.domain.WarnDataRes;
import com.boryou.servs.dplatform.module.warn.domain.vo.WarnDataVO;
import com.boryou.servs.dplatform.module.warn.service.WarnDataService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class WarnDataController {

    private final WarnDataService warnDataService;

    @PostMapping("/api/warn/get")
    public PageResult<WarnDataRes> warnGet(@RequestBody WarnDataVO warnDataVO) {
        return warnDataService.warnGet(warnDataVO);
    }
    @PostMapping("/api/warn/add")
    public boolean warnAdd(@RequestBody List<WarnDataRes> warnDataResList) {
        return warnDataService.warnAdd(warnDataResList);
    }

    @PostMapping("/api/warn/push")
    public PageResult<EsBean> warnPush(@RequestBody EsSearchBO esSearchBO) {
        return warnDataService.warnPush(esSearchBO);
    }

    @PostMapping("/api/warn/check")
    public List<String> warnCheck(@RequestBody WarnDataVO warnDataVO) {
        return warnDataService.warnCheck(warnDataVO);
    }

    @PostMapping("/api/warn/check")
    public List<String> warnCheck(@RequestBody WarnDataVO warnDataVO) {
        return warnDataService.warnCheck(warnDataVO);
    }

}
