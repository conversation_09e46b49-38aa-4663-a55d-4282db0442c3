package com.boryou.servs.dplatform.common.controller;

import cn.hutool.core.util.StrUtil;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dplatform.common.service.EsCommonService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsUpdateBO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;

/**
 * @description ES公用Controller
 * <AUTHOR>
 * @date 2024/4/23 10:15
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/common/info")
public class EsCommonController {

    private final EsCommonService esCommonService;

    @GetMapping("/demo")
    public Return searchDemo(String author) {
        return Return.ok(esCommonService.searchDemo(author));
    }


    /**
     * 查询最近三小时的数据量*
     * @return
     */
    @GetMapping("/monitor")
    public Return searchLatestThreeHoursInfoCount() {
        return Return.ok(esCommonService.searchLatestThreeHoursInfoCount());
    }
    /**
     * 不推荐使用这个方法，会导致跨所有分区查询，性能极低，使用searchByIdTime替代
     * @param esSearchBO 参数
     * @return 返回
     */
    @Deprecated
    @PostMapping("/searchById")
    public EsBean searchById(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.searchById(esSearchBO.getId());
    }
    @PostMapping("/searchByIdTime")
    public EsBean searchByIdTime(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.searchByIdTime(esSearchBO);
    }
    /**
     * 根据id查询并高亮关键词*
     * @param esSearchBO 参数
     * @return 返回
     */
    @PostMapping("/searchAndHighlightById")
    public EsBean searchAndHighlightById(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.searchAndHighlightById(esSearchBO.getId(), esSearchBO.getKeyWord1());
    }    /**
     * 根据id查询并高亮关键词*
     * @param esSearchBO 参数
     * @return 返回
     */
    @PostMapping("/searchAndHighlightByIdX")
    public EsBean searchAndHighlightByIdX(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.searchAndHighlightByIdX(esSearchBO.getId(), esSearchBO.getKeyWord1(),esSearchBO.getAccurate(),esSearchBO.getStartTime(),esSearchBO.getEndTime());
    }

    @PostMapping("/searchAndHighlightByUrl")
    public Return searchAndHighlightByUrl(@RequestBody EsSearchBO esSearchBO) {
        if (StrUtil.isEmpty(esSearchBO.getUrl())) {
            return Return.error("缺少参数！");
        }
        return Return.ok(esCommonService.searchAndHighlightByUrl(esSearchBO.getUrl()));
    }

    @PostMapping("/searchByUrl")
    public Return searchByUrl(@RequestBody EsSearchBO esSearchBO) {
        if (StrUtil.isEmpty(esSearchBO.getUrl())) {
            return Return.error("缺少参数！");
        }
        return Return.ok(esCommonService.searchByUrl(esSearchBO));
    }
    /**
     * TODO 该方法目前已知处理不了微博原创isOriginal字段的业务场景，需要调用下方searchx
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/search")
    public PageResult<EsBean> search(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.search(esSearchBO);
    }

    @PostMapping("/mediaTypeCountForOriginal")
    public Map<String, Long> mediaTypeCountForOriginal(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.mediaTypeCountForOriginal(esSearchBO);
    }
    /**
     * 基础版本common search，不带if逻辑判断的版本，上面这个search方法已经被改的很臃肿了，不具备共用性，故此重写
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/searchx")
    public PageResult<EsBean> searchx(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.searchx(esSearchBO);
    }
    /**
     * 滑动分页查询全部匹配数据
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/searchAll")
    public PageResult<EsBean> getScrollData(@RequestBody EsSearchBO esSearchBO) {
        try {
            return esCommonService.queryScrollAllData(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/realTimeInfoCount")
    public Integer realTimeInfoCount(@RequestBody EsSearchBO esSearchBO) {
        return esCommonService.realTimeInfoCount(esSearchBO);
    }


    /**
     * 查询数据条数
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getInfoCount")
    public Long getInfoCount(@RequestBody EsSearchBO esSearchBO){
        return esCommonService.getInfoCount(esSearchBO);
    }
    /**
     * 查询相同的数据条数
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getSimilarCount")
    public Integer getSimilarCount(@RequestBody EsSearchBO esSearchBO){
        return esCommonService.getSimilarCount(esSearchBO);
    }

    /**
     * 查询微信，微博，网站指定日期时间范围内的曲线图
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getTypeCurve")
    public  Map<String,Map<String,Long>> getTypeCount(@RequestBody EsSearchBO esSearchBO){
        try {
            return esCommonService.getTypeCount(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 获取网站，微博，微信  类型的指定日期时间范围内总的曲线图
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getTotalCurve")
    public  Map<String,Long> getTotalCount(@RequestBody EsSearchBO esSearchBO){
        try {
            return esCommonService.getTotalCount(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 查询抖音，快手，头条的指定日期时间范围内的曲线图--可以单独获取抖音，快手，头条的曲线图。
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getDouyinCurve")
    public  Map<String,Map<String,Long>> getDouyinTotal(@RequestBody EsSearchBO esSearchBO){
        try {
            return esCommonService.getDouyinTotal(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通用分组统计类型对应的总数量接口
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/getTypeTotalCount")
    public  Map<String,Long> getTypeTotalCount(@RequestBody EsSearchBO esSearchBO){
        try {
            return esCommonService.getTypeTotalCount(esSearchBO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据id跟新信息
     * @param esUpdateBO 所有参数必传
     * @return 返回值
     */
    @PostMapping("/updateById")
    public boolean updateById(@RequestBody @Validated EsUpdateBO esUpdateBO){
        return esCommonService.updateById(esUpdateBO);
    }

}
