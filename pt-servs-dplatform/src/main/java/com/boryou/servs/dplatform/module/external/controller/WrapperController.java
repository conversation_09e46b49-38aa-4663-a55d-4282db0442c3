package com.boryou.servs.dplatform.module.external.controller;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.module.external.bo.WrapperRequest;
import com.boryou.servs.dplatform.module.external.service.WrapperService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class WrapperController {
    private final WrapperService wrapperService;

    @PostMapping("/wrapper/search")
    public PageResult<EsBean> wrapperSearch(@RequestBody WrapperRequest wrapperRequest) {
        return wrapperService.wrapperSearch(wrapperRequest);
    }

}
