package com.boryou.servs.dplatform.module.external.bo;

import com.boryou.servs.dplatform.pojo.bo.SortBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class StatisticsBean implements Serializable {

    // 关键词词组1-3 多个以英文逗号或空格分隔
    private String keywords1;
    private String keywords2;
    private String keywords3;
    private String keywords4;
    // 排除词词组
    private String excludeWords;
    // 开始和结束时间
    private String startTime;
    //日志时间ID
    private String logTimeId;
    private String endTime;
    // 时间段数
    private int count;
    // 开始页码和页大小
    private int pageNum = 1;
    private int pageSize = 10;
    private Integer emotion = -1;//情感类型 -1代表全部，0：代表中性，1：代表负面，2：代表正面

    private Integer original = 0;//当值为1时表示去重，0不去重


    //0全部  1标题   2 正文  3作者
    private String keywordsPosition;
    private String range = "all";
    private String mediaType;
    private String hotName;
    private String url;
    private String title;
    private String host;
    private String excludeHosts;
    private int timeType;
    private String author;
    private Boolean authorExists;
    private Integer spamFlag;
    private String videoType; //短视频媒体平台
//    private int type;

    //    private int sort; // 排序方式
    //排序字段
    private String sortField;
    //ASC,DESC
    private String sortType;
    /**
     * 多个或者单个排序
     */
    private List<SortBO> sorts;


    private String md5;

    private Integer aggSize = 10;

    private String[] siteMeta;
    /**
     * 内容地域 contentAreaCode
     */
    private String contentAreaCode;

    //TODO 我自己写的commsearch搜索方法的的临时type区分，后面需要优化这种处理方式,应该要废弃掉
    private String searchKewordType;

    private String aggName;

    private String authorId;

    private boolean accurate = true;//是否精确查询  默认一般是精确查询，只有舆情检索时选择模糊查询才是true
    /**
     * 搜索方式 1 专业模式  0 普通模式
     */
    private int configSelect;

    /**
     * 专业搜索词
     */
    private String proWord;


    /**
     * 项目接口调用标识
     */
    private Integer projectType;
    @Override
    public String toString() {
        return "StatisticsBean{" +
                "keywords1='" + keywords1 + '\'' +
                ", keywords2='" + keywords2 + '\'' +
                ", keywords3='" + keywords3 + '\'' +
                ", excludeWords='" + excludeWords + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", mediaType='" + mediaType + '\'' +
                ", host='" + host + '\'' +
                '}';
    }
}
