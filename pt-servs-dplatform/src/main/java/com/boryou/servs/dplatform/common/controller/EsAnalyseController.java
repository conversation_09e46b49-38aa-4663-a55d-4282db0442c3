package com.boryou.servs.dplatform.common.controller;

import com.boryou.servs.dplatform.common.service.EsAnalyseService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.pojo.bo.EsSearchTimeBO;
import com.boryou.servs.dplatform.pojo.vo.TimeCountFlowVO;
import com.boryou.servs.dplatform.pojo.vo.TimeFlowVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @description ES统计分析Controller
 * <AUTHOR>
 * @date 2024/4/26 9:14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/analyse")
public class EsAnalyseController {
    private final EsAnalyseService esAnalyseService;
    /**
     * 情感分析*
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    @PostMapping("/emotionAnalyse")
    public Map<String,Long> emotionAnalysis(@RequestBody EsSearchBO esSearchBO) {
        return esAnalyseService.emotionAnalysis(esSearchBO);
    }

    /**
     * 情感分析*
     * @param esSearchBO 查询BO
     * @return 返回值
     */
    @PostMapping("/emotionAnalysisForOriginal")
    public Map<String,Long> emotionAnalysisForOriginal(@RequestBody EsSearchBO esSearchBO) {
        return esAnalyseService.emotionAnalysisForOriginal(esSearchBO);
    }

    /**
     * 媒体标签统计
     * <AUTHOR>
     * @date 2024/4/29
     */
    @PostMapping("/siteMetaAnalysis")
    public Map<String, Long> siteMetaAnalysis(EsSearchBO esSearchBO) {
        return esAnalyseService.siteMetaAnalysis(esSearchBO);
    }

    /**
     * 意见领袖
     */
    @PostMapping("/opinionLeader")
    public List<EsBean> opinionLeader(@RequestBody EsSearchBO esSearchBO){
        return esAnalyseService.opinionLeader(esSearchBO);
    }

    /**
     * 网民观点
     */
    @PostMapping("/netizenOpinion")
    public List<EsBean> netizenOpinion(@RequestBody EsSearchBO esSearchBO){
        return esAnalyseService.opinionLeader(esSearchBO);
    }


    /**
     * 媒体类型分析
     * @param esSearchBO
     * @return
     */
    @PostMapping("/mediaTypeMap")
    public Map<String, Long> getMediaTypeMap(@RequestBody EsSearchBO esSearchBO){
        return esAnalyseService.getMediaTypeMap(esSearchBO);
    }

    /**
     * 媒体类型分析
     * <AUTHOR>
     * @date 2024/4/29
     */
    @PostMapping("/mediaTypeMapForOriginal")
    public Map<String, Long> mediaTypeMapForOriginal(@RequestBody EsSearchBO esSearchBO) {
        return esAnalyseService.getMediaTypeMapForOriginal(esSearchBO);
    }

    /**
     * 媒体活跃度分析  查询前10位最活跃的媒体
     * <AUTHOR>
     * @date 2024/4/29
     */
    @PostMapping("/mediaActiveMap")
    public Map<String, Long> mediaActiveMap(@RequestBody EsSearchBO esSearchBO) {
        return esAnalyseService.mediaActiveMap(esSearchBO);
    }

    /**
     * 地域分布图
     * <AUTHOR>
     * @date 2024/4/29
     */
    @PostMapping("/areaMap")
    public Map<String, Long> areaMap(@RequestBody EsSearchBO esSearchBO) {
        return esAnalyseService.areaMap(esSearchBO);
    }

    @PostMapping("/time")
    public List<TimeFlowVO> time(@RequestBody EsSearchTimeBO esSearchTimeBO) {
        return esAnalyseService.time(esSearchTimeBO);
    }
    @PostMapping("/timeCount")
    public List<TimeCountFlowVO> timeCount(@RequestBody EsSearchTimeBO esSearchTimeBO) {
        return esAnalyseService.timeCount(esSearchTimeBO);
    }
}
