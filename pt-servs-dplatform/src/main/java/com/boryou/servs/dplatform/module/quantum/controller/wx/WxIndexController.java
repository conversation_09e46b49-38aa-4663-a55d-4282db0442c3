package com.boryou.servs.dplatform.module.quantum.controller.wx;

import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.dplatform.module.quantum.service.IWxIndexService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-07-31 09:21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/quantum")
public class WxIndexController {

    private final IWxIndexService wxIndexService;

    /**
     * 量子-信息库搜索
     *
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/index/search")
    public PageResult<EsBean> search(@RequestBody EsSearchBO esSearchBO) {
        return wxIndexService.search(esSearchBO);
    }
    /**
     * 量子-信息库type字段的数量搜索
     *
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @PostMapping("/search/mediaTypeCountForOriginal")
    public Map<String, Long> mediaTypeCountForOriginal(@RequestBody EsSearchBO esSearchBO) {
        return wxIndexService.mediaTypeCountForOriginal(esSearchBO);
    }

    /**
     * 更新浏览量
     *
     * @param esSearchBO 查询参数
     * @return 返回值
     */
    @GetMapping("/updatePageView/{esId}")
    public Return updatePageView(@PathVariable String esId) {
        boolean flag= wxIndexService.updatePageView(esId);
        return Return.ok(flag);
    }

}

