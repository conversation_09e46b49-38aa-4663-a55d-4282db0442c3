
package com.boryou.servs.dplatform.module.byyuqing.bean;

import lombok.Data;

/**
 * 所有统计分析请求参数
 * <AUTHOR>
 * @date 2017-07-05 上午10:14:30
 */
@Data
public class AnalyseRequire {

    private String chart;//图表类型
    private String source;//来源（检索、版块或事件）
    private int ignoreTime;//忽略时间(截取长时间的长度)

    //版块、检索、事件共用
    private String startTime;//开始时间
    private String endTime;//结束时间

    //版块、检索共用
    private String plateId;//板块id
    private String keyWord;//关键词
    private String regexRegion;//关键词位置
    private String zfflag;//倾向性
    private String mediaType;//媒体类型
    private int quchong;//去重
    private int trash;//去除垃圾
    private int configSelect;//配置选择

    //以下为检索专用
    private int accurate; // 精确度 0-模糊 1-精确
    private String quadraticWord; // 二次搜索包含的词，多个词之间以空格间隔
    private String quadraticFilter; // 二次搜索不包含的词，多个词之间以空格间隔
    private String hostNames; // 站点名称范围
    private String areaNames; // 地域名称范围

    //版块
    private String regexArea; // 地域词
    private String regexSubject; // 主体词（单位/人物）
    private String regexEvent; // 事件词
    private String regexNo; // 屏蔽词
    private String secondWord;
    private String monitorWords;//监控关键词
    private String excludeWords;//排除关键词

    //事件分析
    private String eventId;//事件id
    private String keywords;// 关键词
    private String areaName;// 地域名（地域统计用）
    private String types;// 信息类型

}
