package com.boryou.servs.dplatform.enums;

import com.boryou.servs.dplatform.pojo.BC;

import java.util.Objects;

/**
 *  项目调用标识
 * <AUTHOR>
 *  2024-07-01 09:33
 */
public enum ProjectFlagEnum {
    //博约舆情
    BYYQ(1, "博约舆情"),
    //合肥舆情
    HFYQ(2, "合肥舆情"),
    //对外接口
    OPENAPI (3, "卓望接口"),
    //浙江高院
    ZJGY(4, "浙江高院"),
    //中电信量子
    QUANTUM(5, "中电信量子"),
    NEW_BY(6, "新博约舆情"),
    SPREAD_IMPACT(7,"新媒管家");

    Integer value;
    String desc;

    ProjectFlagEnum(int value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(Integer value) {
        if (null==value){
            return BC.UNDEFINED;
        }
        for (ProjectFlagEnum mediaTypeEnum : ProjectFlagEnum.values()) {
            if (Objects.equals(mediaTypeEnum.value, value)) {
                return mediaTypeEnum.desc;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getAllType() {
        StringBuilder str = new StringBuilder();
        for (ProjectFlagEnum item : ProjectFlagEnum.values()) {
            if (str.length() != 0) {
                str.append(",");
            }
            str.append(item.getValue());
        }
        return str.toString();
    }
}
