package com.boryou.servs.dplatform.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import com.boryou.servs.dplatform.enums.*;
import com.boryou.servs.dplatform.module.hfyuqing.enums.PositionEnum;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.boryou.servs.dplatform.util.EsUtil.buildProWord;

/**
 * <AUTHOR>
 * @description Es工具类, 进行各种条件组装拼接
 * @date 2024/4/23 9:51
 */
public class EsManyTypeUtil {

    private EsManyTypeUtil() {
    }

    private static void buildTermsQueries(BoolQuery.Builder bool, String fieldName, String[] values) {
        if (values.length == 0) {
            return;
        }
        List<FieldValue> fieldValues = Arrays.stream(values).map(FieldValue::of).collect(Collectors.toList());
        bool.filter(QueryBuilders.terms(t -> t.field(fieldName).terms(q -> q.value(fieldValues))));
    }

    private static String removeOuterQuotedNothing(String expression) {
        Pattern pattern = Pattern.compile("\"\\s+\"");
        List<String> results = new ArrayList<>();
        int balance = 0; // 用于跟踪引号平衡
        char[] chars = expression.toCharArray();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < chars.length; i++) {
            //引号中的内容
            if (balance == 1) {
                builder.append(chars[i]);
            }
            //引号结束 排除每个空的关键词
            if ('\"' == chars[i] && balance == 1) {
                balance = 0;
                String word = builder.toString();
                if (!"\"\"".equals(word) && !"\" \"".equals(word)) {
                    results.add(word);
                }
                builder = new StringBuilder();
            }
            //引号开始
            if ('\"' == chars[i] && balance == 0) {
                balance = 1;
                builder.append(chars[i]);
            }
        }
        List<String> kwlist = new ArrayList<>();
        for (String result : results) {
            Matcher matcher = pattern.matcher(result);
            if (!matcher.find()) {
                kwlist.add(result);
            }
        }
        return CollUtil.join(kwlist, " OR ");
    }
    /**
     * 处理关键词和关键词位置条件
     *
     * @param keyWord         关键词
     * @param keyWordPosition 关键词位置
     * @return 返回值
     */
    // public static QueryStringQuery.Builder buildKeyWord(String keyWord, String keyWordPosition, Boolean useIkSmart) {
    //     QueryStringQuery.Builder kwQb = QueryBuilders.queryString();
    //     if (useIkSmart != null && useIkSmart) {
    //         kwQb.analyzer("ik_smart");
    //     }
    //     //给单个关键词前后加引号
    //     kwQb.query(removeOuterQuotedNothing(quotedEveryKeyWord(keyWord)));
    //     //处理关键词位置
    //     if (keyWordPosition == null) {
    //         kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
    //     } else {
    //         String[] keyWordPositionArr = keyWordPosition.split(",");
    //         if (keyWordPositionArr.length > 0) {
    //             for (String str : keyWordPositionArr) {
    //                 switch (SearchPositionEnum.getByValue(str)) {
    //                     case TITLE:
    //                         kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName());
    //                         break;
    //                     case TEXT:
    //                         kwQb.fields(EsBeanFieldEnum.TEXT.getFieldName());
    //                         break;
    //                     case AUTHOR:
    //                         kwQb.fields(EsBeanFieldEnum.AUTHOR.getFieldName());
    //                         break;
    //                     default:
    //                         kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
    //                         break;
    //
    //                 }
    //             }
    //         }
    //     }
    //     BoolQuery.Builder bool = QueryBuilders.bool();
    //     return kwQb;
    // }

    public static Query buildKeyWord(String keyWord, String keyWordPosition, Boolean useIkSmart) {
        QueryStringQuery.Builder kwQb = QueryBuilders.queryString();
        if (useIkSmart != null && useIkSmart) {
            kwQb.analyzer("ik_smart");
        }
        //给单个关键词前后加引号
        kwQb.query(removeOuterQuotedNothing(quotedEveryKeyWord(keyWord)));
        //处理关键词位置
        if (keyWordPosition == null) {
            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
        } else {
            String[] keyWordPositionArr = keyWordPosition.split(",");
            if (keyWordPositionArr.length > 0) {
                for (String str : keyWordPositionArr) {
                    switch (SearchPositionEnum.getByValue(str)) {
                        case TITLE:
                            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName());
                            break;
                        case TEXT:
                            kwQb.fields(EsBeanFieldEnum.TEXT.getFieldName());
                            break;
                        case AUTHOR:
                            if (useIkSmart == null || !useIkSmart) {
                                kwQb.fields(EsBeanFieldEnum.AUTHOR.getFieldName());
                            }
                            break;
                        default:
                            kwQb.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
                            break;

                    }
                }
            }
        }
        if (CharSequenceUtil.isNotBlank(keyWordPosition)
                && keyWordPosition != null
                && keyWordPosition.contains("3")
                && Boolean.TRUE.equals(useIkSmart)) {
            BoolQuery.Builder bool = QueryBuilders.bool();
            bool.should(kwQb.build()._toQuery());
            List<String> split = CharSequenceUtil.split(keyWord, " ");
            buildTermsQueriesShould(bool, EsBeanFieldEnum.AUTHOR.getFieldName(), split);
            bool.minimumShouldMatch("1");
            return bool.build()._toQuery();
        }
        return kwQb.build()._toQuery();
    }

    private static void buildTermsQueriesShould(BoolQuery.Builder bool, String fieldName, List<String> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        List<FieldValue> fieldValues = values.stream().map(FieldValue::of).collect(Collectors.toList());
        bool.should(QueryBuilders.terms(t -> t.field(fieldName).terms(q -> q.value(fieldValues))));
    }

    public static BoolQuery.Builder buildFuzzyKeyWord(String keyWord, String keyWordPosition) {
        BoolQuery.Builder bool = QueryBuilders.bool();
        //专业搜索
        String[] wordArr = keyWord.split("\\+");
        for (String word : wordArr) {
            word = word.substring(1, word.length() - 1);
            word = word.replace("|", " ").replaceAll("\\(", "").replaceAll("\\)", "");
            word = quotedEveryKeyWord(word);
            MultiMatchQuery.Builder multiMatchBuilder = QueryBuilders.multiMatch().operator(Operator.Or).type(TextQueryType.BestFields).query(word);
            if (keyWordPosition == null) {
                multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
            } else {
                String[] keyWordPositionArr = keyWordPosition.split(",");
                if (keyWordPositionArr.length > 0) {
                    for (String str : keyWordPositionArr) {
                        switch (SearchPositionEnum.getByValue(str)) {
                            case TITLE:
                                multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName());
                                break;
                            case TEXT:
                                multiMatchBuilder.fields(EsBeanFieldEnum.TEXT.getFieldName());
                                break;
                            case AUTHOR:
                                multiMatchBuilder.fields(EsBeanFieldEnum.AUTHOR.getFieldName());
                                break;
                            default:
                                multiMatchBuilder.fields(EsBeanFieldEnum.TITLE.getFieldName(), EsBeanFieldEnum.TEXT.getFieldName()/*, EsBeanFieldEnum.AUTHOR.getFieldName()*/);
                                break;
                        }
                    }
                }
            }
            bool.filter(multiMatchBuilder.build()._toQuery());
        }
        return bool;
    }

    private static String quotedEveryKeyWord(String keyWord) {
        List<String> quotedList = new ArrayList<>();

        Pattern pattern = Pattern.compile("\"(.*?)\"");

        Matcher matcher = pattern.matcher(keyWord);

        StringBuilder remainingText = new StringBuilder(keyWord.length());

        while (matcher.find()) {
            quotedList.add(matcher.group());

            // 从原字符串中移除已匹配的部分
            int start = matcher.start();
            int end = matcher.end();
            remainingText.append(keyWord, 0, start);
            remainingText.append(keyWord, end, keyWord.length());
            // 更新keyWord为剩余未处理的部分
            keyWord = remainingText.toString();
            // 重置matcher以匹配剩余部分
            matcher.reset(keyWord);
            // 清空StringBuilder
            remainingText.setLength(0);
        }
        // 输出剩余的字符串（不含引号包裹的部分）
        if (keyWord.length() > 0) {
            keyWord = "\"" + keyWord.trim().replaceAll(" ", "\" \"").replaceAll("\"\"", "") + "\"";
        }

        return CollUtil.join(quotedList, " ").replaceAll("\"\"", "") + " " + keyWord;
    }

    public static BoolQuery.Builder buildManyTypeQuery(EsSearchBO esSearchBO, boolean typePosition) {
        BoolQuery.Builder bool = QueryBuilders.bool();
        if (typePosition) {
            buildTypeAndKeyWord(esSearchBO, bool);
        } else {
            buildTypeAndWordAway(esSearchBO, bool);
        }

        //二次检索词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticWord())) {
            Query keyWordQb = buildKeyWord(esSearchBO.getQuadraticWord(), esSearchBO.getSearchPosition(), esSearchBO.getUseIkSmart());
            bool.filter(keyWordQb);
        }

        //处理排除id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeId())) {
            String excludeId = esSearchBO.getExcludeId();
            List<FieldValue> list = Arrays.stream(excludeId.split(","))
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.ID.getFieldName()).terms(q -> q.value(list))));
        }
        //处理排除词过滤词
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord()) || CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
            StringBuilder stringBuilder = new StringBuilder();
            if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeWord())) {
                stringBuilder.append(esSearchBO.getExcludeWord());
            }
            if (CharSequenceUtil.isNotBlank(esSearchBO.getQuadraticFilterWord())) {
                stringBuilder.append(esSearchBO.getQuadraticFilterWord());
            }
            String[] keyWordArr = stringBuilder.toString().split("\\s+");
            List<String> keyWordList = new ArrayList<>();
            for (String str : keyWordArr) {
                keyWordList.add("\"" + str + "\"");
            }
            String keyword = StringUtils.join(keyWordList, " OR ");
            QueryStringQuery.Builder queryString = buildProWord(keyword, esSearchBO.getSearchPosition());
            bool.mustNot(queryString.build()._toQuery());
        }
        //处理是否原创条件
        //if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
        //    bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_ORIGINAL.getFlag())));
        //} else if (esSearchBO.getIsOriginal() != null && IsOriginalEnum.IS_NOT_ORIGINAL.getFlag() == esSearchBO.getIsOriginal()) {
        //    bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_ORIGINAL.getFieldName()).value(IsOriginalEnum.IS_NOT_ORIGINAL.getFlag())));
        //}

        //是否垃圾数据
        if (esSearchBO.getIsSpam() != null && IsSpamEnum.IS_SPAM.getFlag() == esSearchBO.getIsSpam()) {
            bool.filter(QueryBuilders.term(t -> t.field(EsBeanFieldEnum.IS_SPAM.getFieldName()).value(IsSpamEnum.IS_SPAM.getFlag())));
        }

        //处理定向id
        if (CharSequenceUtil.isNotBlank(esSearchBO.getId())) {
            buildTermsQueries(bool, EsBeanFieldEnum.ID.getFieldName(), esSearchBO.getId().split(","));
        }
        //情感
        if (CharSequenceUtil.isNotBlank(esSearchBO.getEmotionFlag())) {
            buildTermsQueries(bool, EsBeanFieldEnum.EMOTION_FLAG.getFieldName(), esSearchBO.getEmotionFlag().split(","));
        }
        //域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getHost())) {
            buildTermsQueries(bool, EsBeanFieldEnum.HOST.getFieldName(), esSearchBO.getHost().split(","));
        }
        //排除域名
        if (CharSequenceUtil.isNotBlank(esSearchBO.getExcludeHost())) {
            List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getExcludeHost().split(",")).map(FieldValue::of).collect(Collectors.toList());
            bool.mustNot(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).terms(q -> q.value(fieldValues))));
        }
        String url = esSearchBO.getUrl();
        if (CharSequenceUtil.isNotBlank(url)) {
            bool.filter(QueryBuilders.term(item -> item.field(EsBeanFieldEnum.URL.getFieldName())
                    .value(url)));
        }
        //作者
        if (CharSequenceUtil.isNotBlank(esSearchBO.getAuthor())) {
            buildTermsQueries(bool, EsBeanFieldEnum.AUTHOR.getFieldName(), esSearchBO.getAuthor().split(","));
        }
        //定向信源
        if ("1".equals(esSearchBO.getSourceSetting()) || "2".equals(esSearchBO.getSourceSetting())) {
            BoolQuery.Builder sourceBool = QueryBuilders.bool();
            Map<String, String> sourceMap = esSearchBO.getSourceMap();
            for (Map.Entry<String, String> entry : sourceMap.entrySet()) {
                //按照类型查询命中的author或host
                List<Query> queryList = new ArrayList<>();
                queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(entry.getKey())));
                queryList.add(
                        QueryBuilders.bool().should(
                                        Arrays.asList(
                                                QueryBuilders.term(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).value(entry.getValue())),
                                                QueryBuilders.term(t -> t.field(EsBeanFieldEnum.HOST.getFieldName()).value(entry.getValue()))
                                        )
                                )
                                .build()
                                ._toQuery()
                );
                sourceBool.should(QueryBuilders.bool().must(queryList).build()._toQuery());
            }
            //1 must  2 mustnot
            if ("1".equals(esSearchBO.getSourceSetting())) {
                bool.filter(sourceBool.build()._toQuery());
            } else {
                bool.mustNot(sourceBool.build()._toQuery());
            }
        }
        //站点标签
        if (CharSequenceUtil.isNotBlank(esSearchBO.getSiteMeta())) {
            buildTermsQueries(bool, EsBeanFieldEnum.SITE_META.getFieldName(), esSearchBO.getSiteMeta().split(","));
        }
        //信息分类标签
        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentMeta())) {
            buildTermsQueries(bool, EsBeanFieldEnum.CONTENT_META.getFieldName(), esSearchBO.getContentMeta().split(","));
        }
        //时间范围
        // 时间范围
        if (CharSequenceUtil.isNotEmpty(esSearchBO.getStartTime()) || CharSequenceUtil.isNotEmpty(esSearchBO.getEndTime())) {
            // yyyy-MM-dd HH:mm:ss = 10 + 1 + 8 转换yyyy-MM-dd HH:mm 和 yyyyMMddHHmmss
            String timeField;
            if (esSearchBO.getTimeType() == TimeTypeEnum.PUBLISH.getVal()) {
                timeField = EsBeanFieldEnum.PUBLISH_TIME.getFieldName();
            } else {
                timeField = EsBeanFieldEnum.UPDATE_TIME.getFieldName();
            }
            String finalTimeField = timeField;
            if (CharSequenceUtil.isNotEmpty(esSearchBO.getStartTime())) {
                if (esSearchBO.getStartTime().length() < 19) {
                    esSearchBO.setStartTime(DateUtil.format(DateUtil.parse(esSearchBO.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
                }
                Query range = QueryBuilders.range(r -> r.field(finalTimeField).from(esSearchBO.getStartTime()));
                bool.filter(range);
            }
            if (CharSequenceUtil.isNotEmpty(esSearchBO.getEndTime())) {
                if (esSearchBO.getEndTime().length() < 19) {
                    esSearchBO.setEndTime(DateUtil.format(DateUtil.parse(esSearchBO.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
                }
                Query range = QueryBuilders.range(r -> r.field(finalTimeField).to(esSearchBO.getEndTime()));
                bool.filter(range);
            }
        }
        //地域
        if (CharSequenceUtil.isNotBlank(esSearchBO.getContentAreaCode())) {
            buildTermsQueries(bool, EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName(), esSearchBO.getContentAreaCode().split(","));
        }
        return bool;
    }

    private static void buildTypeAndWordAway(EsSearchBO esSearchBO, BoolQuery.Builder bool) {
        String searchPosition = esSearchBO.getSearchPosition();
        buildKeyWordPosition(esSearchBO, bool, searchPosition);
        //处理媒体类型
        if (CharSequenceUtil.isNotBlank(esSearchBO.getType())) {
            BoolQuery.Builder typeBool = QueryBuilders.bool();
            List<String> typeArray = new ArrayList<>(Arrays.asList(esSearchBO.getType().split(",")));
            boolean typeShouldFlag = false;
            //增加微博专属条件contentForm accountLevel
            if (typeArray.contains(String.valueOf(MediaTypeEnum.WEIBO.getValue()))) {
                List<Query> queryList = new ArrayList<>();
                if (StrUtil.isNotEmpty(esSearchBO.getContentForm())) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.CONTENT_FORM.getFieldName()).value(esSearchBO.getContentForm())));
                }
                if (StrUtil.isNotEmpty(esSearchBO.getAccountLevel())) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.ACCOUNT_LEVEL.getFieldName()).value(esSearchBO.getAccountLevel())));
                }
                if (!queryList.isEmpty()) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.WEIBO.getValue())));
                    typeBool.should(queryList);
                    typeArray.removeIf(String.valueOf(MediaTypeEnum.WEIBO.getValue())::equals);
                    typeShouldFlag = true;
                }
            }
            //增加短视频专属条件videoHost
            if (typeArray.contains(String.valueOf(MediaTypeEnum.VIDEO.getValue()))) {
                List<Query> queryList = new ArrayList<>();
                if (StrUtil.isNotEmpty(esSearchBO.getVideoHost())) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.HOST.getFieldName()).value(esSearchBO.getVideoHost())));
                }
                if (!queryList.isEmpty()) {
                    queryList.add(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.VIDEO.getValue())));
                    typeBool.should(queryList);
                    typeArray.removeIf(String.valueOf(MediaTypeEnum.VIDEO.getValue())::equals);
                    typeShouldFlag = true;
                }
            }
            if (CollUtil.isNotEmpty(typeArray)) {
                List<FieldValue> fieldValues = typeArray.stream().map(FieldValue::of).collect(Collectors.toList());
                Query terms = QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName()).terms(q -> q.value(fieldValues)));
                if (typeShouldFlag) {
                    typeBool.should(terms);
                    typeBool.minimumShouldMatch("1");
                    bool.filter(typeBool.build()._toQuery());
                }else{
                    bool.filter(terms);
                }

            }
        }
    }

    private static void buildTypeAndKeyWord(EsSearchBO esSearchBO, BoolQuery.Builder bool) {
        //type里含有音视频（8 9）时， 音视频的词位置只在text, 无论传参的词位置含有什么

        String type = esSearchBO.getType();
        //处理媒体类型
        if (StrUtil.isBlankIfStr(type)) {
            return;
        }
        BoolQuery.Builder allBool = QueryBuilders.bool();

        List<String> typeList = CharSequenceUtil.splitTrim(type, ",");
        List<String> typeMediaList = typeList.stream().filter(item -> item.contains("8") || item.contains("9"))
                .collect(Collectors.toList());
        List<String> typeOtherList = typeList.stream().filter(item -> !item.contains("8") && !item.contains("9"))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(typeMediaList)) {
            BoolQuery.Builder typeWordBool = QueryBuilders.bool();
            buildKeyWordPosition(esSearchBO, typeWordBool, PositionEnum.TEXT.getInfo());
            typeWordBool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName())
                    .terms(q -> q.value(typeMediaList.stream().map(FieldValue::of).collect(Collectors.toList())))));
            allBool.should(typeWordBool.build()._toQuery());
        }
        if (CollUtil.isNotEmpty(typeOtherList)) {
            BoolQuery.Builder typeWordBool = QueryBuilders.bool();
            String searchPosition = esSearchBO.getSearchPosition();
            buildKeyWordPosition(esSearchBO, typeWordBool, searchPosition);
            typeWordBool.filter(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.TYPE.getFieldName())
                    .terms(q -> q.value(typeOtherList.stream().map(FieldValue::of).collect(Collectors.toList())))));
            allBool.should(typeWordBool.build()._toQuery());
        }
        bool.filter(allBool.build()._toQuery());

    }

    private static void buildKeyWordPosition(EsSearchBO esSearchBO, BoolQuery.Builder typeWordBool, String searchPosition) {
        boolean accurate = esSearchBO.getAccurate();
        //非专业搜索
        String keyWord1 = esSearchBO.getKeyWord1();
        if (CharSequenceUtil.isNotBlank(keyWord1)) {
            Query query;
            if (accurate) {
                query = buildKeyWord(keyWord1, searchPosition, esSearchBO.getUseIkSmart());
            } else {
                BoolQuery.Builder keyWordQb1 = buildFuzzyKeyWord(keyWord1, searchPosition);
                query = keyWordQb1.build()._toQuery();
            }
            typeWordBool.must(query);
        }
        String keyWord2 = esSearchBO.getKeyWord2();
        if (CharSequenceUtil.isNotBlank(keyWord2)) {
            Query query;
            if (accurate) {
                query = buildKeyWord(keyWord2, searchPosition, esSearchBO.getUseIkSmart());
            } else {
                BoolQuery.Builder keyWordQb2 = buildFuzzyKeyWord(keyWord2, searchPosition);
                query = keyWordQb2.build()._toQuery();
            }
            typeWordBool.must(query);
        }
        String keyWord3 = esSearchBO.getKeyWord3();
        if (CharSequenceUtil.isNotBlank(keyWord3)) {
            Query query;
            if (accurate) {
                query = buildKeyWord(keyWord3, searchPosition, esSearchBO.getUseIkSmart());
            } else {
                BoolQuery.Builder keyWordQb3 = buildFuzzyKeyWord(keyWord3, searchPosition);
                query = keyWordQb3.build()._toQuery();
            }
            typeWordBool.must(query);
        }
        String keyWord4 = esSearchBO.getKeyWord4();
        if (CharSequenceUtil.isNotBlank(keyWord4)) {
            Query query;
            if (accurate) {
                query = buildKeyWord(keyWord4, searchPosition, esSearchBO.getUseIkSmart());
            } else {
                BoolQuery.Builder keyWordQb4 = buildFuzzyKeyWord(keyWord4, searchPosition);
                query = keyWordQb4.build()._toQuery();
            }
            typeWordBool.must(query);
        }
    }

}
