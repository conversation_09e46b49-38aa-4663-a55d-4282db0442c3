package com.boryou.servs.dplatform.module.byyuqing.util;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.alibaba.fastjson.JSONObject;
import com.boryou.servs.dplatform.module.byyuqing.bean.AnalyseRequire;
import com.boryou.servs.dplatform.module.byyuqing.bean.SectionData;
import com.boryou.servs.dplatform.module.byyuqing.constant.Constant;
import com.boryou.servs.dplatform.module.external.bo.IndexResultBean;
import com.boryou.servs.dplatform.module.external.bo.StatisticsBean;
import com.boryou.servs.dplatform.module.external.service.AreaDistributionService;
import com.boryou.servs.dplatform.pojo.bean.PageInfo;
import com.boryou.servs.dplatform.pojo.bo.SortBO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 传播力专用
 *
 * @author: Young
 * @Date: 2021/12/9
 */
@Component
@RequiredArgsConstructor
public class PropagateUtil {

    private final AreaDistributionService areaDistributionService;
    /**
     * 获取时间范围内最早的信息时间
     *
     * @author: Young
     * @Date: 2021/12/9 14:27
     */
    public  String getStartTime(AnalyseRequire analysisSubject) {
        StatisticsBean analysisSubject1 = JSONUtil.toBean(JSONUtil.toJsonStr(analysisSubject),StatisticsBean.class);
        analysisSubject1.setKeywords1(analysisSubject.getRegexArea());
        analysisSubject1.setKeywords2(analysisSubject.getRegexSubject());
        analysisSubject1.setKeywords3(analysisSubject.getRegexEvent());
        analysisSubject1.setExcludeWords(analysisSubject1.getExcludeWords());
        String types = analysisSubject.getTypes();
        if (StrUtil.isNotEmpty(types) || "-1".equals(types)) {
            if (analysisSubject.getChart().equals("AreaAnalyse")) {
                analysisSubject1.setMediaType(Constant.BORYOUYUQING_MVWB_TYPE.replaceAll(" ",","));
            } else {
                analysisSubject1.setMediaType(Constant.BORYOUYUQING_ALL_TYPE.replaceAll(" ",","));
            }
        } else {
            analysisSubject1.setMediaType(types);
        }
        ArrayList<SortBO> sorts = new ArrayList<>();
        sorts.add(new SortBO("publishTime","Asc"));
        sorts.add(new SortBO("_score","Desc"));
        analysisSubject1.setSorts(sorts);
        return areaDistributionService.getPropagationStartTime(analysisSubject1);
    }


    /**
     * 获取传播路径索引数据
     *
     * @author: Young
     * @Date: 2021/12/9 15:06
     */
    public  SectionData getPathMapInfo(AnalyseRequire analysisSubject, Date startDate, Date endDate, int pageSize) {
        StatisticsBean analysisSubject1 = JSONUtil.toBean(JSONUtil.toJsonStr(analysisSubject),StatisticsBean.class);
        analysisSubject1.setKeywords1(analysisSubject.getRegexArea());
        analysisSubject1.setKeywords2(analysisSubject.getRegexSubject());
        analysisSubject1.setKeywords3(analysisSubject.getRegexEvent());
        analysisSubject1.setExcludeWords(analysisSubject1.getExcludeWords());
        analysisSubject1.setStartTime(DateUtil.formatDateTime(startDate));
        analysisSubject1.setEndTime(DateUtil.formatDateTime(endDate));
        String types = analysisSubject.getTypes();
        if (StrUtil.isNotEmpty(types) || "-1".equals(types)) {
            if (analysisSubject.getChart().equals("AreaAnalyse")) {
                analysisSubject1.setMediaType(Constant.BORYOUYUQING_MVWB_TYPE.replaceAll(" ",","));
            } else {
                analysisSubject1.setMediaType(Constant.BORYOUYUQING_ALL_TYPE.replaceAll(" ",","));
            }
        } else {
            analysisSubject1.setMediaType(types);
        }
        ArrayList<SortBO> sorts = new ArrayList<>();
        sorts.add(new SortBO("publishTime","Asc"));
        sorts.add(new SortBO("_score","Desc"));
        analysisSubject1.setSorts(sorts);
        BoolQuery.Builder bool = SpringUtil.getBean(AreaDistributionService.class).setCommonSearch(JSONUtil.toBean(JSONObject.toJSONString(analysisSubject1), StatisticsBean.class));
        PageInfo<IndexResultBean> queryPageInfoList = areaDistributionService.getQueryPageInfoList(new StatisticsBean(), bool);
        SectionData section = new SectionData();
        section.setResults(queryPageInfoList.getList());
        section.setAllNum(Integer.parseInt(queryPageInfoList.getTotal()+""));
        return section;
    }


    /**
     * 将时间区间按节点数目分段
     *
     * @param sectionNum 段数(小区间数目)
     * @param dateStart  开始时间
     * @param dateEnd    结束时间
     * @return List<Date> 时间轴数据
     * <AUTHOR>
     * @date 2015-12-30 下午9:06:26
     */
    public static List<Date> getTimeAxis(int sectionNum, Date dateStart, Date dateEnd) {
        List<Date> dateAxis = new ArrayList<>();
        long times = dateEnd.getTime() - dateStart.getTime();
        long seconds = times / sectionNum / 1000;
        int field = Calendar.SECOND;
        int amount = (int) seconds;
        if (seconds > Integer.MAX_VALUE) {
            field = Calendar.MINUTE;
            amount = (int) (seconds / 100);
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(dateStart);
        // 开始时间
        dateAxis.add(dateStart);
        for (int i = 0; i < sectionNum - 1; i++) {
            cal.add(field, amount);
            // 中间时间
            dateAxis.add(cal.getTime());
        }
        // 结束时间
        dateAxis.add(dateEnd);
//        Collections.reverse(dateAxis);
        return dateAxis;
    }

    /**
     * 获得除第一个节点外数量最多的区间
     *
     * @param sections
     * @return
     * <AUTHOR>
     * @date 2015-12-31 上午9:05:16
     */
    public static int getMaxPosition(List<SectionData> sections) {
        int maxPostion = 0;
        if (sections != null && sections.size() > 2) {
            for (int i = 1; i < sections.size(); i++) {
                if (sections.get(i).getAllNum() > sections.get(maxPostion).getAllNum()) {
                    maxPostion = i;
                }
            }
        }
        return maxPostion;
    }


    public static int bean2nodes(Date startDate, Date endDate, Map<String, String> hostFilter,
                                 List<Map<String, Object>> nodes, List<Map<String, Object>> links, int index, int category,
                                 SectionData section, int pageSize, int sectionSize) {
        int rootIndex = -1;
        Map<String, Object> node = null;
        Map<String, Object> link = null;
        String host = null;
        String siteHost = null;
        List<IndexResultBean> beans = section.getResults();

        if (beans != null && beans.size() > 0) {

            // int size = beans.size();
            // 控制原始点转发数，防止大于爆发点
            int size = category == 4 && beans.size() > 10 ? 10 : beans.size();
            IndexResultBean rootBean = null;
            for (int i = 0; i < size; i++) {
                node = new HashMap<>();

                siteHost = beans.get(i).getHost();
                if (siteHost == null) {
                    siteHost = "";
                }
                host = beans.get(i).getHost() == null ? "" : beans.get(i).getHost();
                if (hostFilter.get(host) != null || nodes.size() > sectionSize * pageSize - 1) {
                    // 该host已经存在不处理
                    continue;
                }
                if (rootIndex == -1) {
                    rootBean = beans.get(i);
                    // 该区间的根节点
                    if (category == 4) {
                        node.put("category", "开始");
                    } else if (category == 2) {
                        node.put("category", "爆发");
                    } else {
                        node.put("category", "升温");
                    }
                    node.put("name", host);
//                    node.put("siteHost", SysSite.getName(siteHost));
                    node.put("value", section.getAllNum());
                    node.put("index", index);
                    node.put("isRoot", 1);
                    node.put("startTime", CommonUtil.dateToStringLong(startDate));
                    node.put("endTime", CommonUtil.dateToStringLong(endDate));
                    rootIndex = index;
                    index++;
                    nodes.add(node);
                    hostFilter.put(host, host);
                    continue;
                }
                // 根节点上的普通转发节点
                node.put("index", index);
                node.put("category", "转发"); // 3表示转发
                // host = beans.get(i).getHost() == null ? "" :
                // SysSite.getName(beans.get(i).getHost());
                node.put("name", host);
//                node.put("siteHost", SysSite.getName(siteHost));
                node.put("isRoot", 0);
                nodes.add(node);
                hostFilter.put(host, host);
                link = new HashMap<>();
                link.put("source", rootBean.getHost());
                link.put("target", host);
                links.add(link);
                index++;

            }
        }
        return index;
    }


    public static void handDateIntevel(List<Date> dateAxis, List<SectionData> sections, Map<String, String> hostFilter,
                                       List<Map<String, Object>> nodes, List<Map<String, Object>> links, int maxPostion, int index,
                                       Map<Integer, Integer> rootIndex, int pageSize, int sectionSize) {
        int category = 4;
        SectionData section = null;
        for (int i = 0; i < sections.size(); i++) {
            section = sections.get(i);

            if (i < maxPostion && i == 0) {
                // 最早点
                category = 4;
                continue;
            } else if (i < maxPostion) {
                // 作为升温过程处理
                category = 1;
            } else if (i == maxPostion) {
                // 作为爆发处理
                category = 2;
                // 上面已预先处理好，跳过此步骤
                continue;
            } else if (i > maxPostion) {
                // 作为减弱趋势去处理
                category = 0;
            }
            // 根节点索引保存
            rootIndex.put(i, index);
            index = bean2nodes(dateAxis.get(i), dateAxis.get(i + 1), hostFilter, nodes, links, index, category,
                    section, pageSize, sectionSize);

        }
    }


    /**
     * 链接所有的根节点
     *
     * @param nodes     节点
     * @param links     连接线
     * @param rootIndex
     * <AUTHOR>
     * @date 2015-12-31 上午11:36:27
     */
    public static void linkRoots(List<Map<String, Object>> nodes, List<Map<String, Object>> links,
                                 Map<Integer, Integer> rootIndex) {
        // 构造map，用以判断根节点是否存在
        Map<Integer, Integer> hash = new HashMap<>();
        for (Map<String, Object> map : nodes) {
            if (map.get("isRoot") != null && map.get("isRoot").toString().equals("1")) {
                hash.put(Integer.parseInt(map.get("index").toString()), 1);
            }
        }
        Map<String, Object> link = null;
        int maxIndex = rootIndex.size();
        for (int i = 0; i < maxIndex - 1; i++) {
            if (hash.get(rootIndex.get(i)) == null) {
                // 根节点实际不存在
                continue;
            }
            link = new HashMap<>();
            link.put("source", rootIndex.get(i));
            for (int j = i + 1; j < maxIndex; j++) {
                if (hash.get(rootIndex.get(j)) != null && hash.get(rootIndex.get(j)) == 1) {
                    link.put("target", rootIndex.get(j));
                    links.add(link);
                    break;
                }
            }
        }
    }
}
