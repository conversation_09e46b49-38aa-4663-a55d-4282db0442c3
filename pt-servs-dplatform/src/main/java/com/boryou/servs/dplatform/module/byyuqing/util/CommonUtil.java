package com.boryou.servs.dplatform.module.byyuqing.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {
    /**
     * 通用String转Date方法
     * @param str    字符串
     * @param format 字符串时间原格式，如“yyyyMMddHHmmss”
     * @return 转换后的Date对象
     * <AUTHOR>
     * @date 2016-6-27 下午4:21:26
     */
    public static Date stringToDateWithFormat(String str, String format) {
        Date date = null;
        if (!isNullOrEmpty(str)) {
            try {
                date = new SimpleDateFormat(format).parse(str);
            } catch (ParseException ignored) {
            }
        }
        return date;
    }

    /**
     * 将时间转换为规定格式字符串
     * @param date 日期
     * @param format 格式
     * <AUTHOR>
     * @date Jun 29, 2016 1:48:09 PM
     */
    public static String dateToStringWithFormat(Date date, String format) {
        String str = "";
        try {
            str = new SimpleDateFormat(format).format(date);
        } catch (Exception ignored) {
        }
        return str;
    }

    /**
     * 字符串转换为日期，支持yyyy-MM-dd HH:mm:ss格式、yyyyMMddHHmmss格式、yyyy-MM-dd格式、yyyyMMdd格式
     * @param str 日期字符串
     * @return java.util.Date 对象
     */
    public static Date stringToDateLong(String str) {
        Date date = null;
        if (!isNullOrEmpty(str)) {
            str = str.replaceAll("[-: ]", "");// 将字符串日期中的横杠冒号，空格去掉
            try {
                date = new SimpleDateFormat("yyyyMMddHHmmss").parse(str);
            } catch (ParseException ignored) {
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyyMMdd").parse(str);
                } catch (ParseException ignored) {
                }
            }
        }
        return date;
    }

    /**
     * 字符串转换为日期，yyyy-MM-dd 格式
     * @param str 日期字符串
     * @return java.util.Date 对象
     */
    public static Date stringToDateShort(String str) {
        Date date = null;
        if (!isNullOrEmpty(str)) {
            try {
                date = new SimpleDateFormat("yyyy-MM-dd").parse(str);
            } catch (ParseException ignored) {
            }
            // 如果上述转换不成功，换另外一种转换方式
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(str);
                } catch (ParseException ignored) {
                }
            }
        }
        return date;
    }

    /**
     * 日期转换为字符串，yyyy-MM-dd HH:mm:ss 格式
     * @param date 对象
     * @return 日期字符串
     */
    public static String dateToStringLong(Date date) {
        String strDate = null;
        if (date != null) {
            strDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        }
        return strDate;
    }

    /**
     * 日期转换为字符串，yyyyMMddHHmmss 格式
     * @param date 对象
     * @return 日期字符串
     */
    public static String dateToLongStringNoSep(Date date) {
        String strDate = null;
        if (date != null) {
            strDate = new SimpleDateFormat("yyyyMMddHHmmss").format(date);
        }
        return strDate;
    }

    /**
     * 日期转换为字符串，yyyy-MM-dd 格式
     * @param date 对象
     * @return 日期字符串
     */
    public static String dateToStringShort(Date date) {
        String strDate = null;
        if (date != null) {
            strDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
        }
        return strDate;
    }

    /**
     * 将日期转换为指定格式的字符串
     * @param date   日期
     * @param format 格式化串
     * @return String
     */
    public static String dateToString(Date date, String format) {
        String strDate = null;
        if (date != null) {
            strDate = new SimpleDateFormat(format).format(date);
        }
        return strDate;
    }

    /**
     * 判断字符串是否为 null 或者为空字符串
     * @param str 字符串
     */
    public static boolean isNullOrEmpty(String str) {
        boolean isValid = false;
        if (str == null || "".equals(str.trim())) {
            isValid = true;
        }
        return isValid;
    }

    /**
     * 截取字符串
     * @param str    源字符串
     * @param length 截取的长度
     * @return 截取后的字符串
     */
    public static String cutStr(String str, int length) {
        if (!isNullOrEmpty(str)) {
            if (str.length() > length) {
                str = str.substring(0, length) + "...";
            }
        }
        return str;
    }

    /**
     * 将字符串数组转换成以符号 c 分割的字符串
     * @param values 数据值
     * @param c 分隔符
     * @return
     */
    public static String join(String[] values, String c) {
        StringBuilder result = new StringBuilder();
        if (values != null && values.length > 0) {
            for (Object value : values) {
                result.append(value).append(c);
            }
            result = new StringBuilder(result.substring(0, result.length() - 1));
        }
        return result.toString();
    }

    /**
     * 将各种类型的对象数组或者集合转换成字符串数组
     * @param obj 对象
     * @return
     */
    public static String[] toStringArray(Object obj) {
        String[] strArray = null;
        if (obj != null) { // 当不为空时
            if (obj instanceof Object[]) { // 如果为对象数组
                Object[] objArray = (Object[]) obj;
                if (objArray.length > 0) {
                    strArray = new String[objArray.length];
                    for (int i = 0; i < objArray.length; i++) {
                        strArray[i] = String.valueOf(objArray[i]);
                    }
                }
            } else if (obj instanceof List) { // 如果为集合
                List<String> list = (List<String>) obj;
                if (list.size() > 0) {
                    strArray = new String[list.size()];
                    for (int i = 0; i < list.size(); i++) {
                        strArray[i] = String.valueOf(list.get(i));
                    }
                }
            }
        }
        return strArray;
    }

    /**
     * 将符号 c 分割的字符串转换成 List&lt;String&gt;
     * @param str 字符串
     * @param c 分隔符
     */
    public static List<String> stringToList(String str, String c) {
        List<String> list = new ArrayList<String>();
        if (str != null && str.trim().length() > 0) {
            String[] strArray = str.split(c);
            // list = Arrays.asList(strArray);
            list.addAll(Arrays.asList(strArray));
        }
        return list;
    }

    /**
     * 查找字符串中关于某个组的索引
     * @param regex 正则表达式
     * @param input 给定输入的字符串
     * @param group 组号
     * @return 组所在字符串的索引
     */
    public static int getIndex(String regex, String input, int group) {
        int index = -1;
        try {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(input);
            if (m.matches()) {
                index = m.start(group);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return index;
    }

    /**
     * 换行
     * @param str 字符串
     */
    public static String changeLine(String str) {
        if (!isNullOrEmpty(str)) {
            if (str.length() > 20) {
                str = str.substring(0, 20) + "<BR />" + str.substring(20);
            }
            if (str.length() > 46) {
                str = str.substring(0, 46) + "<BR />" + str.substring(46);
            }
            if (str.length() > 72) {
                str = str.substring(0, 72) + "<BR />" + str.substring(72);
            }
            if (str.length() > 90) {
                str = str.substring(0, 90) + "<BR />" + str.substring(90);
            }
        }
        return str;
    }

    /**
     * 数字日期格式转换为汉字日期格式
     * @param s “2012-02-13”形式的
     * @return
     */
    public static String change2Hanzi(String s) {
        // 传参数的格式为 2009-02-06
        StringBuilder k = new StringBuilder();
        String[] ss = s.split("-");
        // ss[0]为年 ，ss[1]为月，ss[2]为日

        for (int j = 0; j < ss[0].length(); j++) {
            switch (ss[0].charAt(j)) {
                case '0':
                    k.append("零");
                    break;
                case '1':
                    k.append("一");
                    break;
                case '2':
                    k.append("二");
                    break;
                case '3':
                    k.append("三");
                    break;
                case '4':
                    k.append("四");
                    break;
                case '5':
                    k.append("五");
                    break;
                case '6':
                    k.append("六");
                    break;
                case '7':
                    k.append("七");
                    break;
                case '8':
                    k.append("八");
                    break;
                case '9':
                    k.append("九");
                    break;
            }
        }
        k.append("年");

        switch (ss[1]) {
            case "01":
                k.append("一");
                break;
            case "02":
                k.append("二");
                break;
            case "03":
                k.append("三");
                break;
            case "04":
                k.append("四");
                break;
            case "05":
                k.append("五");
                break;
            case "06":
                k.append("六");
                break;
            case "07":
                k.append("七");
                break;
            case "08":
                k.append("八");
                break;
            case "09":
                k.append("九");
                break;
            case "10":
                k.append("十");
                break;
            case "11":
                k.append("十一");
                break;
            default:
                k.append("十二");
                break;
        }
        k.append("月");

        switch (ss[2]) {
            case "01":
                k.append("一");
                break;
            case "02":
                k.append("二");
                break;
            case "03":
                k.append("三");
                break;
            case "04":
                k.append("四");
                break;
            case "05":
                k.append("五");
                break;
            case "06":
                k.append("六");
                break;
            case "07":
                k.append("七");
                break;
            case "08":
                k.append("八");
                break;
            case "09":
                k.append("九");
                break;
            case "10":
                k.append("十");
                break;
            case "11":
                k.append("十一");
                break;
            case "12":
                k.append("十二");
                break;
            case "13":
                k.append("十三");
                break;
            case "14":
                k.append("十四");
                break;
            case "15":
                k.append("十五");
                break;
            case "16":
                k.append("十六");
                break;
            case "17":
                k.append("十七");
                break;
            case "18":
                k.append("十八");
                break;
            case "19":
                k.append("十九");
                break;
            case "20":
                k.append("二十");
                break;
            case "21":
                k.append("二十一");
                break;
            case "22":
                k.append("二十二");
                break;
            case "23":
                k.append("二十三");
                break;
            case "24":
                k.append("二十四");
                break;
            case "25":
                k.append("二十五");
                break;
            case "26":
                k.append("二十六");
                break;
            case "27":
                k.append("二十七");
                break;
            case "28":
                k.append("二十八");
                break;
            case "29":
                k.append("二十九");
                break;
            case "30":
                k.append("三十");
                break;
            case "31":
                k.append("三十一");
                break;
        }
        k.append("日");
        return k.toString();
    }

    /**
     * 将一种格式的日期字符转换成目标格式的字符串
     * @param str          日期串
     * @param sourceFormat 原格式，如"yyyyMMddHHmmss"
     * @param destFormat   目标格式，如"yyyy-MM-dd HH:mm:ss"
     * @return String 格式化后的日期串
     * <AUTHOR>
     * @date 2015-8-10 下午6:09:41
     */
    public static String stringDateFormat(String str, String sourceFormat, String destFormat) {
        try {
            SimpleDateFormat source = new SimpleDateFormat(sourceFormat);
            SimpleDateFormat dest = new SimpleDateFormat(destFormat);
            return dest.format(source.parse(str));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * date2大于date1返回true，否则返回false
     * @param date1 日期1
     * @param date2 日期2
     * @param format 日期格式“yyyy-MM-dd”
     * @return boolean
     * <AUTHOR>
     * @date 2016-4-26 下午2:27:23
     */
    public static boolean compareDate(String date1, String date2, String format) {
        DateFormat df = new SimpleDateFormat(format);
        try {
            Date dt1 = df.parse(date1);
            Date dt2 = df.parse(date2);
            if (dt1.getTime() > dt2.getTime()) {
                return false;
            } else return dt1.getTime() < dt2.getTime();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return false;
    }

    /**
     * date2大于date1返回true，否则返回false
     * @param dt1 日期1
     * @param dt2 日期2
     * @return boolean
     * <AUTHOR>
     * @date 2016-4-26 下午2:40:04
     */
    public static boolean compareDate(Date dt1, Date dt2) {
        try {
            if (dt1.getTime() > dt2.getTime()) {
                return false;
            } else return dt1.getTime() < dt2.getTime();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return false;
    }

    /**
     * 日期增减计算
     * @param dt       需要计算的基数日期
     * @param timeUnit 需要计算的时间单位，如DATE、MONTH、YEAR
     * @param count    需要计算的值，如：3、5、-1负数表示减
     * @return Date 运算后的日期
     * <AUTHOR>
     * @date 2016-4-26 下午2:29:52
     */
    public static Date dayShift(Date dt, int timeUnit, int count) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        cal.add(timeUnit, count);
        return new Date(cal.getTimeInMillis());
    }

    /**
     * 返回指定年度的所有周。List中包含的是String[2]对象<br>
     * string[0]本周的开始日期,string[1]是本周的结束日期。<br>
     * 日期的格式为yyyy-MM-dd。<br>
     * 每年的第一个周，必须包含星期一且是完整的七天。<br>
     * 例如：2009年的第一个周开始日期为2009-01-05，结束日期为2009-01-11。 <br>
     * 星期一在哪一年，那么包含这个星期的周就是哪一年的周。<br>
     * 例如：2008-12-29是星期一，2009-01-04是星期日，哪么这个周就是2008年度的最后一个周。<br>
     * @param year 格式 yyyy ，必须大于1900年度 小于9999年
     * @return
     */
    public static List<String[]> getWeeksByYear(final int year) {
        if (year < 1900 || year > 9999) {
            throw new NullPointerException("年度必须大于等于1900年小于等于9999年");
        }
        int weeks = getWeekNumByYear(year);
        List<String[]> result = new ArrayList<String[]>(weeks);
        // 今年的周，只返回到当前周
        if (year == getThisYear()) {
            weeks = getThisWeek();
        }
        for (int i = 1; i <= weeks; i++) {
            String[] tempWeek = new String[2];
            tempWeek[0] = getYearWeekFirstDayTime(year, i);
            tempWeek[1] = getYearWeekEndDayTime(year, i);
            result.add(tempWeek);
            // System.out.println(i+"="+tempWeek[0]+"_"+tempWeek[1]);
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        CommonUtil.getWeeksByYear(2016);
        // System.out.println(Common.getThisWeek());
    }

    /**
     * 计算指定年度共有多少个周。
     * @param year 格式 yyyy ，必须大于1900年度 小于9999年
     * @return
     */
    public static int getWeekNumByYear(final int year) {
        if (year < 1900 || year > 9999) {
            throw new NullPointerException("年度必须大于等于1900年小于等于9999年");
        }
        int result = 52;// 每年至少有52个周 ，最多有53个周。
        String date = getYearWeekFirstDayTime(year, 53);
        if (date.substring(0, 4).equals(year + "")) { // 判断年度是否相符，如果相符说明有53个周。
            result = 53;
        }
        return result;
    }

    /**
     * 计算某年某周的开始日期
     * @param yearNum 格式 yyyy ，必须大于1900年度 小于9999年
     * @param weekNum 1到52或者53
     * @return 日期，格式为yyyy-MM-dd
     */
    public static String getYearWeekFirstDayTime(int yearNum, int weekNum) {
        if (yearNum < 1900 || yearNum > 9999) {
            throw new NullPointerException("年度必须大于等于1900年小于等于9999年");
        }
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为星期一
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);// 每周从周一开始
        // 上面两句代码配合，才能实现，每年度的第一个周，是包含第一个星期一的那个周
        cal.setMinimalDaysInFirstWeek(7); // 设置每周最少为7天
        cal.set(Calendar.YEAR, yearNum);
        cal.set(Calendar.WEEK_OF_YEAR, weekNum);
        // 分别取得当前日期的年、月、日
        return dateToStringShort(cal.getTime());
    }

    /**
     * 计算某年某周的结束日期
     * @param yearNum 格式 yyyy ，必须大于1900年度 小于9999年
     * @param weekNum 1到52或者53
     * @return 日期，格式为yyyy-MM-dd
     */
    public static String getYearWeekEndDayTime(int yearNum, int weekNum) {
        if (yearNum < 1900 || yearNum > 9999) {
            throw new NullPointerException("年度必须大于等于1900年小于等于9999年");
        }
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY); // 设置每周的第一天为星期一
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);// 每周从周一开始
        // 上面两句代码配合，才能实现，每年度的第一个周，是包含第一个星期一的那个周
        cal.setMinimalDaysInFirstWeek(7); // 设置每周最少为7天
        cal.set(Calendar.YEAR, yearNum);
        cal.set(Calendar.WEEK_OF_YEAR, weekNum);
        return dateToStringShort(cal.getTime());
    }

    /**
     * 判断带有集合性质的字符串是否包含某个字串
     * @param str  字符串集合（逗号分隔）
     * @param code 字串
     * @return boolean true/false
     * <AUTHOR>
     * @date 2016-7-14 下午8:30:44
     */
    public static boolean stringHasCode(String str, String code) {
        if (str == null || str.length() == 0) {
            return false;
        }
        if (code == null || code.length() == 0) {
            return true;
        }
        str = str.replaceAll("\\s+", "");
        // 集合中包含字串
        return str.equals(code) || str.startsWith(code + ",") || str.contains("," + code + ",")
                || str.endsWith("," + code);
    }

    /**
     * 获取当前 年份
     * @return int this year
     * <AUTHOR>
     * @date 2016-7-15 下午07:38:44
     */
    public static int getThisYear() {
        Calendar cal = Calendar.getInstance();
        return cal.get(Calendar.YEAR);
    }

    /**
     * 获取上一周
     * @return int this week;
     * <AUTHOR>
     * @date 2016-7-15 下午09:02:01
     */
    public static int getThisWeek() {
        Calendar cal = Calendar.getInstance();
        int week = cal.get(Calendar.WEEK_OF_YEAR);
        return week <= 2 ? 2 : week - 2;
    }

    /**
     * 日期增减计算
     * @param time     初始时间
     * @param interval 日期间隔（1天、-1天）
     * @return Date 增减后的日期
     * <AUTHOR>
     * @date 2016-8-25 上午10:37:31
     */
    public static Date dateAdd(Date time, int interval) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.DAY_OF_MONTH, interval);
        return cal.getTime();
    }

    /**
     * 日期增减计算
     * @param interval 日期间隔（1天、-1天）
     * @return Date 增减后的日期(基于当前时间)
     * <AUTHOR>
     * @date 2016-8-25 上午10:37:31
     */
    public static Date dateAdd(int interval) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, interval);
        return cal.getTime();
    }

    /**
     * 获取当前时间距离次日凌晨还有多长时间
     * @return int 分钟数
     * <AUTHOR>
     * @date 2016-11-16 下午2:37:37
     */
    public static int getWeeHoursDiff(int minuteDelay) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, minuteDelay);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        int diff = (int) ((cal.getTimeInMillis() - System.currentTimeMillis()) / 60000);
        System.out.println(diff + " minutes left.");
        return diff;
    }

    /**
     * 计算day1与day2之间相差的天数
     * @param day1 日期1 格式yyyy-MM-dd
     * @param day2 日期2 格式yyyy-MM-dd
     * @return int 相差天数
     * <AUTHOR>
     * @date 2016-11-16 下午5:11:22
     */
    public static int getDayDiff(String day1, String day2) {
        Date date1 = CommonUtil.stringToDateShort(day1);
        Date date2 = CommonUtil.stringToDateShort(day2);
        long diff = date1.getTime() - date2.getTime();
        return (int) (diff / (1000 * 3600 * 24));
    }

    /**
     * 格式化主键集合（添加单引号，数据库中使用）
     * @param ids 主键集合
     * @return String
     * <AUTHOR>
     * @date 2016-12-26 下午4:53:42
     */
    public static String formatIds(String ids) {
        if (ids == null) {
            return ids;
        }
        // 替换空格
        ids = ids.replaceAll("\\s+", "");
        ids = ids.replace(",", "','");
        ids = "'" + ids + "'";
        return ids;
    }


    /**
     * 字符串转换为日期，yyyy-MM-dd HH:mm:ss 格式
     * @param str 日期字符串
     * @return java.util.Date 对象
     */
    public static Date stringToDateLongFormate(String str) {
        Date date = null;
        if (!isNullOrEmpty(str)) {
            try {
                date = new SimpleDateFormat("yyyy-MM-dd HH:m").parse(str);
            } catch (ParseException e) {
            }
            try {
                date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(str);
            } catch (ParseException e) {
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyyMMddHHmmss").parse(str);
                } catch (ParseException e) {
                }
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(str);
                } catch (ParseException e) {
                }
            }
            // 如果上述转换不成功，换另外一种转换方式
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyy-MM-dd").parse(str);
                } catch (ParseException e) {
                }
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyyMMdd").parse(str);
                } catch (ParseException e) {
                }
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒").parse(str);
                } catch (ParseException e) {
                }
            }
            if (date == null) {
                try {
                    date = new SimpleDateFormat("yyyy年MM月dd日").parse(str);
                } catch (ParseException e) {
                }
            }
        }
        return date;
    }
}
