package com.boryou.servs.dplatform.config;

import cn.hutool.core.text.CharSequenceUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientOptions;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.client.HttpAsyncResponseConsumerFactory;

import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * es数据库配置
 * <AUTHOR>
 */
@Configuration
@Data
public class ESConfig {

    @Value("${es.hosts}")
    private String hosts;

    @Value("${es.username}")
    private String username;

    @Value("${es.password}")
    private String password;

    @Value("${es.certPath}")
    private String certPath;

    @Value("${app.env}")
    private String env;

    @Bean
    public ElasticsearchClient esClient() {
        if ("quantum".equals(env)) {
            return esClientWithOutCert();
        }else{
            return esClientWithCert();
        }
    }

    @SneakyThrows
    public ElasticsearchClient esClientWithCert() {
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) certificateFactory.generateCertificate(getClass().getClassLoader().getResourceAsStream(certPath));
        // 创建 KeyStore，并将认证文件加入其中
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("esCert", cert);
        // 创建 TrustManagerFactory，并初始化 KeyStore
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(keyStore);
        // 创建 SSLContext，并使用 TrustManagerFactory 初始化
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustManagerFactory.getTrustManagers(), null);


        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));

        String[] hostArray = hosts.split(",");
        HttpHost[] httpHosts = new HttpHost[hostArray.length];
        HttpHost httpHost;
        for (int i = 0; i < hostArray.length; i++) {
            String[] strings = hostArray[i].split(":");
            httpHost = new HttpHost(strings[0], Integer.parseInt(strings[1]), "https");
            httpHosts[i] = httpHost;
        }

        // Create the low-level client
        RestClient restClient = RestClient
                .builder(httpHosts)
                // 配置探测保活策略，解决远程主机强迫关闭一个连接的问题
                .setHttpClientConfigCallback(
                        httpClientBuilder -> {
                            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                                    // 禁用身份验证缓存
                                    .disableAuthCaching()
                                    // 显示设置保活策略
                                    .setKeepAliveStrategy((httpResponse, httpContext) -> TimeUnit.MINUTES.toMillis(3))
                                    // 显示开启tcp keepalive
                                    .setDefaultIOReactorConfig(IOReactorConfig.custom().setSoKeepAlive(true).build())
                                    // 设置自定义的 SSLContext
                                    .setSSLContext(sslContext);
                            return httpClientBuilder;
                        })
                .setRequestConfigCallback(requestConfigBuilder ->
                        requestConfigBuilder.setConnectTimeout(10000)
                                .setSocketTimeout(10*60*1000)//5分钟  不可过长，会导致异常请求或卡住导致其他工作线程阻塞，资源长时间被锁定  说明：客户端将在发起请求后等待最多xx秒来接收完整的响应数据。
                                .setConnectionRequestTimeout(30000)).build();


        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());

        // 默认为100M（104857600），现设置为300M
        final int MY_BUFFER_LIMIT = 300 * 1048576;

        RequestOptions.Builder requestOptionsBuilder = RequestOptions.DEFAULT.toBuilder();
        HttpAsyncResponseConsumerFactory.HeapBufferedResponseConsumerFactory responseConsumerFactory = new HttpAsyncResponseConsumerFactory.HeapBufferedResponseConsumerFactory(MY_BUFFER_LIMIT);
        requestOptionsBuilder.setHttpAsyncResponseConsumerFactory(responseConsumerFactory);
        RestClientOptions myOptions = new RestClientOptions(requestOptionsBuilder.build());

        return new ElasticsearchClient(transport, myOptions);
    }


    @SneakyThrows
    public ElasticsearchClient esClientWithOutCert() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        HttpHost[] httpHosts;
        if (CharSequenceUtil.contains(hosts, ",")) {
            String[] hostArray = hosts.split(",");
            httpHosts = new HttpHost[hostArray.length];
            HttpHost httpHost;
            for (int i = 0; i < hostArray.length; i++) {
                String[] strings = hostArray[i].split(":");
                httpHost = new HttpHost(strings[0], Integer.parseInt(strings[1]), "http");
                httpHosts[i] = httpHost;
            }
        } else {
            String[] strings = hosts.split(":");
            HttpHost httpHost = new HttpHost(strings[0], Integer.parseInt(strings[1]), "http");
            httpHosts = new HttpHost[]{httpHost};
        }

        // Create the low-level client
        RestClient restClient = RestClient
                .builder(httpHosts)
                // 配置探测保活策略，解决远程主机强迫关闭一个连接的问题
                .setHttpClientConfigCallback(
                        httpClientBuilder -> {
                            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
                                    // 禁用身份验证缓存
                                    .disableAuthCaching()
                                    // 显示设置保活策略
                                    .setKeepAliveStrategy((httpResponse, httpContext) -> TimeUnit.MINUTES.toMillis(3))
                                    // 显示开启tcp keepalive
                                    .setDefaultIOReactorConfig(IOReactorConfig.custom().setSoKeepAlive(true).build());
                            return httpClientBuilder;
                        })
                .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder.setConnectTimeout(10000).setSocketTimeout(Integer.MAX_VALUE).setConnectionRequestTimeout(10000)).build();


        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());

        // 默认为100M（104857600），现设置为300M
        final int MY_BUFFER_LIMIT = 300 * 1048576;

        RequestOptions.Builder requestOptionsBuilder = RequestOptions.DEFAULT.toBuilder();
        HttpAsyncResponseConsumerFactory.HeapBufferedResponseConsumerFactory responseConsumerFactory = new HttpAsyncResponseConsumerFactory.HeapBufferedResponseConsumerFactory(MY_BUFFER_LIMIT);
        requestOptionsBuilder.setHttpAsyncResponseConsumerFactory(responseConsumerFactory);
        RestClientOptions myOptions = new RestClientOptions(requestOptionsBuilder.build());

        return new ElasticsearchClient(transport, myOptions);
    }
}
