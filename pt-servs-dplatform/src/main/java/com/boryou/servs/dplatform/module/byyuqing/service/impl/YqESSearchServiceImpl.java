package com.boryou.servs.dplatform.module.byyuqing.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryStringQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import com.boryou.servs.common.bean.PageResult;
import com.boryou.servs.dplatform.common.service.impl.EsCommonServiceImpl;
import com.boryou.servs.dplatform.enums.EsBeanFieldEnum;
import com.boryou.servs.dplatform.enums.MediaTypeEnum;
import com.boryou.servs.dplatform.module.byyuqing.service.YqESSearchService;
import com.boryou.servs.dplatform.pojo.bean.EsBean;
import com.boryou.servs.dplatform.pojo.bo.EsSearchBO;
import com.boryou.servs.dplatform.util.EsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.servs.dplatform.util.EsUtil.buildKeyWord;
import static com.boryou.servs.dplatform.util.EsUtil.quotedEveryKeyWord;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class YqESSearchServiceImpl implements YqESSearchService {

    private final ElasticsearchClient esClient;

    @Override
    public Map<String, Integer> getWeiboAuthorSortByTime(EsSearchBO esSearchBO) {
        final String authorAgg = "authorAgg";
        Map<String, Integer> resultMap = new HashMap<>();

        BoolQuery.Builder boolQuery = EsUtil.buildQuery(esSearchBO);
        if (StrUtil.isNotEmpty(esSearchBO.getContentAreaCode())) {
            boolQuery.must(QueryBuilders.wildcard(w -> w.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).value(esSearchBO.getContentAreaCode() + "*")));
        }
        try {
            SearchResponse<Integer> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(boolQuery.build()._toQuery())
                            .aggregations(authorAgg, a -> a.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).size(60)))
                            .size(0), Integer.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(authorAgg);
                List<StringTermsBucket> array = ((StringTermsAggregate) aggregate._get()).buckets().array();
                for (StringTermsBucket bucket : array) {
                    resultMap.put(bucket.key().stringValue(), (int) bucket.docCount());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultMap;
    }

    @Override
    public PageResult<EsBean> getWeiboSimilarData(EsSearchBO esSearchBO) {
        PageResult<EsBean> esBeanPageResult = new PageResult<>();
        List<EsBean> esBeanList = new ArrayList<>();
        BoolQuery.Builder bool = QueryBuilders.bool();

        bool.must(QueryBuilders.term(q -> q.field(EsBeanFieldEnum.TYPE.getFieldName()).value(MediaTypeEnum.WEIBO.getValue())));
        if (CharSequenceUtil.isNotBlank(esSearchBO.getKeyWord1())) {
            QueryStringQuery.Builder keyWordQb1 = buildKeyWord(quotedEveryKeyWord(esSearchBO.getKeyWord1(), true), esSearchBO.getSearchPosition());
            bool.must(keyWordQb1.build()._toQuery());
        }
        if (CharSequenceUtil.isNotBlank(esSearchBO.getAuthor())) {
            List<FieldValue> fieldValues = Arrays.stream(esSearchBO.getAuthor().split(",")).map(FieldValue::of).collect(Collectors.toList());
            bool.must(QueryBuilders.terms(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).terms(q -> q.value(fieldValues))));
        } else {
            bool.must(QueryBuilders.wildcard(t -> t.field(EsBeanFieldEnum.AUTHOR.getFieldName()).value("*")));
        }
        if (StrUtil.isNotEmpty(esSearchBO.getStartTime()) && StrUtil.isNotEmpty(esSearchBO.getEndTime())) {
            if (esSearchBO.getStartTime().length() < 19) {
                esSearchBO.setStartTime(DateUtil.format(DateUtil.parse(esSearchBO.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (esSearchBO.getEndTime().length() < 19) {
                esSearchBO.setEndTime(DateUtil.format(DateUtil.parse(esSearchBO.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
            }
            bool.must(QueryBuilders.range(r -> r.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).from(esSearchBO.getStartTime()).to(esSearchBO.getEndTime())));
        }

        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .highlight(EsUtil.getHighlight(300))
                            .sort(
                                    sort -> sort.field(
                                            f -> f.field(EsBeanFieldEnum.COMMENT_NUM.getFieldName()).order(SortOrder.Desc)
                                    )
                            )
                            .size(esSearchBO.getPageSize()), EsBean.class);
            if (response != null) {
                EsCommonServiceImpl.getBeansFromHighlight(esBeanList, response, 110);
                esBeanPageResult.setRecords(esBeanList);
                esBeanPageResult.setTotal(response.hits().total().value());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return esBeanPageResult;
    }

    @Override
    public PageResult<EsBean> searchByKeyWordsByPublishTimeAscSort(EsSearchBO esSearchBO) {
        PageResult<EsBean> esBeanPageResult = new PageResult<>();
        List<EsBean> esBeanList = new ArrayList<>();
        BoolQuery.Builder boolQuery = EsUtil.buildQuery(esSearchBO);
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(boolQuery.build()._toQuery())
                            .highlight(EsUtil.getHighlight(110))
                            .sort(sortOptions)
                            .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                            .size(esSearchBO.getPageSize()), EsBean.class);
            EsCommonServiceImpl.getBeansFromHighlight(esBeanList, response, 50);
            esBeanPageResult.setTotal(response.hits().total().value());
        } catch (IOException e) {
            e.printStackTrace();
        }
        esBeanPageResult.setRecords(esBeanList);
        return esBeanPageResult;
    }

    @Override
    public Map<String, Long> getAreaData(EsSearchBO esSearchBO) {
        Map<String, Long> map = new LinkedHashMap<>();
        BoolQuery.Builder boolQuery = EsUtil.buildQuery(esSearchBO);
        if (StrUtil.isNotEmpty(esSearchBO.getContentAreaCode())) {
            boolQuery.must(QueryBuilders.wildcard(w -> w.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).value(esSearchBO.getContentAreaCode() + "*")));
        }
        try {
            String aggKey = "areaMap";
            SearchResponse<Long> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(boolQuery.build()._toQuery())
                            .aggregations(aggKey, a -> a.terms(t -> t.field(EsBeanFieldEnum.CONTENT_AREA_CODE.getFieldName()).size(3)))
                            .size(0), Long.class);
            if (response != null) {
                StringTermsAggregate stringTermsAggregate = (StringTermsAggregate) response.aggregations().get(aggKey)._get();
                List<StringTermsBucket> array = stringTermsAggregate.buckets().array();
                for (StringTermsBucket stringTermsBucket : array) {
                    String key = stringTermsBucket.key().stringValue();
                    map.put(key, stringTermsBucket.docCount());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public List<EsBean> searchByKeyWordAndUrl(EsSearchBO esSearchBO) {
        List<EsBean> esBeanList = new ArrayList<>();

        String keyWord = esSearchBO.getKeyWord1();
        BoolQuery.Builder boolQuery;
        //默认只使用URL查询
        if (StrUtil.isNotEmpty(esSearchBO.getUrl())) {
            esSearchBO.setKeyWord1(null);
            boolQuery = EsUtil.buildQuery(esSearchBO);
            simpleSearchFromES(esBeanList, boolQuery, esSearchBO);
        }
        //当使用Url查询未获得数据 或者 未传URL参数时，且传了keyword参数
        if (CollUtil.isEmpty(esBeanList) || StrUtil.isNotEmpty(keyWord)) {
            esSearchBO.setKeyWord1(keyWord);
            esSearchBO.setUrl(null);
            boolQuery = EsUtil.buildQuery(esSearchBO);
            simpleSearchFromES(esBeanList, boolQuery, esSearchBO);
        }
        return esBeanList;
    }

    private void simpleSearchFromES(List<EsBean> esBeanList, BoolQuery.Builder boolQuery, EsSearchBO esSearchBO) {
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO))
                            .query(boolQuery.build()._toQuery()), EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                esBeanList.add(hit.source());
            }
        } catch (IOException e) {
            log.error("searchByKeyWordAndUrl查询有问题");
        }
    }

    @Override
    public PageResult<EsBean> getSimilarDataById(EsSearchBO esSearchBO) {
        String id = esSearchBO.getId();
        int pageSize = esSearchBO.getPageSize();
        Query queryId = QueryBuilders.term(item -> item.field(EsBeanFieldEnum.ID.getFieldName())
                .value(id));
        List<EsBean> esBeanList = new ArrayList<>();
        String md5 = "";
        // 执行查询
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO))
                            .trackTotalHits(t -> t.enabled(true))
                            .query(queryId), EsBean.class);
            for (Hit<EsBean> hit : response.hits().hits()) {
                EsBean source = hit.source();
                if (source != null) {
                    md5 = source.getMd5();
                }
            }
        } catch (IOException e) {
            log.error("getSimilarDataById查询有问题");
        }
        PageResult<EsBean> pageResult = new PageResult<>();
        if (CharSequenceUtil.isEmpty(md5)) {
            pageResult.setTotal(0L);
            pageResult.setRecords(new ArrayList<>());
            return pageResult;
        }
        String finalMd = md5;
        Query queryMD5 = QueryBuilders.term(item -> item.field(EsBeanFieldEnum.MD5.getFieldName()).value(finalMd));
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(queryMD5)
                            .size(pageSize), EsBean.class);
            HitsMetadata<EsBean> hits = response.hits();
            long value = hits.total().value();
            pageResult.setTotal(value);
            for (Hit<EsBean> hit : hits.hits()) {
                EsBean source = hit.source();
                if (source != null) {
                    esBeanList.add(source);
                }
            }
            pageResult.setRecords(esBeanList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return pageResult;
    }

    public PageResult<EsBean> getSimilarDataByMd5(EsSearchBO esSearchBO) {
        String id = esSearchBO.getId();
        int pageSize = esSearchBO.getPageSize();
        int pageNum = esSearchBO.getPageNum();
        PageResult<EsBean> pageResult = new PageResult<>();
        List<EsBean> esBeanList = new ArrayList<>();
        String md5 = esSearchBO.getMd5();
        Query queryMD5 = QueryBuilders.term(item -> item.field(EsBeanFieldEnum.MD5.getFieldName()).value(md5));
        List<SortOptions> sorts=new ArrayList<>();
        String sortField = esSearchBO.getSortType();
        if (StrUtil.isNotEmpty(sortField)) {
            sorts.add(EsUtil.getSortOptions(esSearchBO.getSortType()));
        }else{
            sorts.add(SortOptionsBuilders.field(f -> f.field(EsBeanFieldEnum.PUBLISH_TIME.getFieldName()).order(SortOrder.Desc)));
        }
        try {
            SearchResponse<EsBean> response = esClient.search(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(queryMD5)
                            .sort(sorts)
                            .from(pageNum)
                            .size(pageSize), EsBean.class);
            HitsMetadata<EsBean> hits = response.hits();
            long value = hits.total().value();
            pageResult.setTotal(value);
            for (Hit<EsBean> hit : hits.hits()) {
                EsBean source = hit.source();
                if (source != null) {
                    esBeanList.add(source);
                }
            }
            pageResult.setRecords(esBeanList);
        } catch (IOException e) {
            pageResult.setTotal(0L);
            pageResult.setRecords(new ArrayList<>());
            return pageResult;
        }
        return pageResult;
    }

    @Override
    public PageResult<EsBean> policyList(EsSearchBO esSearchBO) {
        PageResult<EsBean> esBeanPageResult = new PageResult<>();
        List<EsBean> esBeanList = new ArrayList<>();
        BoolQuery.Builder bool = EsUtil.buildQuery(esSearchBO);
        bool.filter(QueryBuilders.term(t -> t.field("host").value("www.gov.cn")));
        bool.mustNot(QueryBuilders.term(t -> t.field("title.keyword").value("")));
        SortOptions sortOptions = EsUtil.getSortOptions(esSearchBO.getSortType());
        try {
            SearchRequest searchRequest = SearchRequest.of(
                    s -> s.index(EsUtil.getIndexes(esSearchBO)).trackTotalHits(t -> t.enabled(true))
                            .query(bool.build()._toQuery())
                            .sort(sortOptions)
                            .from(((esSearchBO.getPageNum() - 1)) * esSearchBO.getPageSize())
                            .size(esSearchBO.getPageSize()));
            log.info("查询语句为:" + searchRequest);
            SearchResponse<EsBean> response = esClient.search(searchRequest, EsBean.class);
            EsCommonServiceImpl.getBeansFromHighlight(esBeanList, response, 50);
            esBeanPageResult.setTotal(response.hits().total().value());
        } catch (IOException e) {
            e.printStackTrace();
        }
        esBeanPageResult.setRecords(esBeanList);
        return esBeanPageResult;
    }
}
