package com.boryou.servs.dplatform.enums;

import java.util.Objects;

/**
 * @description 检索位置枚举类
 * <AUTHOR>
 * @date 2024/4/22 17:34
 */
public enum SearchPositionEnum {
    //全部
    ALL("0"),
    //标题
    TITLE("1"),
    //正文
    TEXT("2"),
    //作者
    AUTHOR("3");
    String value;
    SearchPositionEnum(String s) {
        this.value = s;
    }

    public static SearchPositionEnum getByValue(String value){
        for(SearchPositionEnum x:values()){
            if(Objects.equals(x.value, value)){
                return x;
            }
        }
        return ALL;

    }
}
