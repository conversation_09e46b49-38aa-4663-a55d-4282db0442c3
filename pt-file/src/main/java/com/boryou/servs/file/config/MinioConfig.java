package com.boryou.servs.file.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
//@ConditionalOnExpression("${minio.enabled:true}")
public class MinioConfig {

    @Value("${minio.endpoint}")
    private String endpoint;

    @Value("${minio.accessKey}")
    private String accessKey;

    @Value("${minio.secretKey}")
    private String secretKey;

    @Value("${minio.connectTimeout}")
    private Long connectTimeout;

    @Value("${minio.writeTimeout}")
    private Long writeTimeout;

    @Value("${minio.readTimeout}")
    private Long readTimeout;

    @Bean("minioClient")
    @ConditionalOnMissingBean
    public MinioClient minioClient() {
        MinioClient minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();

        minioClient.setTimeout(connectTimeout, writeTimeout, readTimeout);
        return minioClient;
    }

}
