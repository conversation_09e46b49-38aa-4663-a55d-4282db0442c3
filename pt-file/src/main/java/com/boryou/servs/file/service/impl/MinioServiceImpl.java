package com.boryou.servs.file.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.HashUtil;
import cn.hutool.crypto.SecureUtil;
import com.boryou.servs.file.pojo.bo.FileInfoBO;
import com.boryou.servs.file.service.MinioService;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MinioServiceImpl implements MinioService {

    @Autowired
    private MinioClient minioClient;

    public static final String URI_DELIMITER = "/";

    @Override
    public Boolean makeBucket(String bucketName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException {
        boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!exist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            return true;
        } else {
            throw new MinioException(bucketName + "存储桶已存在");
        }
    }

    @Override
    public Boolean bucketExists(String bucketName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }

    @Override
    public List<Bucket> bucketList() throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return minioClient.listBuckets();
    }

    /**
     * year（适用于按年分表的情形）
     * newContentType（针对application/octet-stream，可改为 image/png、application/pdf等）
     */
    @Override
    public FileInfoBO putObject(String bucketName, MultipartFile file, String year, String newContentType) throws IOException, MinioException, NoSuchAlgorithmException, InvalidKeyException {
        if (file.isEmpty()) {
            throw new MinioException("文件不能为空");
        }
        boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!exist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }

        // UUID重命名文件
        String fileName = UUID.randomUUID().toString().replace("-", "") + "." + FileUtil.getSuffix(file.getOriginalFilename());
        // 最终存储文件名（含路径）：年/月/日/fileName
        String finalPath = new StringBuilder(String.join(URI_DELIMITER, getDateFolder()))
                .append(URI_DELIMITER)
                .append(fileName)
                .toString();

        long fileSize = file.getSize();
        String fileType = file.getContentType();
        if (newContentType != null) {
            fileType = newContentType;
        }

        minioClient.putObject(PutObjectArgs.builder()
                .bucket(bucketName)
                .object(finalPath)
                .stream(file.getInputStream(), fileSize, -1)
                .contentType(fileType)
                .build());

        String raw = bucketName + finalPath;
        if (year == null) {
            year = "";
        }
        String fileId = new StringBuilder(year).append(SecureUtil.md5(raw)).append(HashUtil.fnvHash(raw)).append(HashUtil.sdbmHash(raw)).toString();

        return new FileInfoBO(bucketName, fileId, finalPath, fileType, fileSize);
    }

    @Override
    public StatObjectResponse getMetadata(String bucketName, String objectName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        StatObjectResponse statObject = minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
        return statObject;
    }

    @Override
    public void removeObject(String bucketName, String objectName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException {
        boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (exist) {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
        } else {
            throw new MinioException(bucketName + "存储桶不存在");
        }
    }

    @Override
    public Iterable<Result<DeleteError>> removeObject(String bucketName, List<String> objectNames) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException {
        boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (exist) {
            List<DeleteObject> objects = objectNames.stream().map(DeleteObject::new).collect(Collectors.toList());
            return minioClient.removeObjects(RemoveObjectsArgs.builder().bucket(bucketName).objects(objects).build());
        } else {
            throw new MinioException(bucketName + "存储桶不存在");
        }
    }

    @Override
    public String getObjectUrl(String bucketName, String objectName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException {
        boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (exist) {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .bucket(bucketName).object(objectName).method(Method.GET)
                    .build());
        } else {
            throw new MinioException(bucketName + "存储桶不存在");
        }
    }

    @Override
    public void downloadObject(String bucketName, String originName, String objectName, HttpServletResponse httpServletResponse) {
        try {
            StatObjectResponse statObjectResponse = minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originName, "UTF-8"));
            httpServletResponse.setContentType(statObjectResponse.contentType());
            httpServletResponse.setCharacterEncoding("UTF-8");
            InputStream inputStream = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
            IOUtils.copy(inputStream, httpServletResponse.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void downloadObject(String bucketName, String objectName, String downToPathname) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        DownloadObjectArgs args = DownloadObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .filename(downToPathname)
                .build();
        minioClient.downloadObject(args);
    }

    /**
     * 获取年月日数组 [2021, 09, 08]
     */
    protected static String[] getDateFolder() {
        String[] ret = new String[3];

        LocalDate localDate = LocalDate.now();
        ret[0] = localDate.getYear() + "";

        int month = localDate.getMonthValue();
        ret[1] = month < 10 ? "0" + month : month + "";

        int day = localDate.getDayOfMonth();
        ret[2] = day < 10 ? "0" + day : day + "";

        return ret;
    }
}
