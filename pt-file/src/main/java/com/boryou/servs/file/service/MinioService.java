package com.boryou.servs.file.service;

import com.boryou.servs.file.pojo.bo.FileInfoBO;
import io.minio.Result;
import io.minio.StatObjectResponse;
import io.minio.errors.*;
import io.minio.messages.Bucket;
import io.minio.messages.DeleteError;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * MinIO服务
 *
 * <AUTHOR>
 */
public interface MinioService {

    /**
     * 创建桶
     */
    Boolean makeBucket(String bucketName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 判断存储桶是否存在
     */
    Boolean bucketExists(String bucketName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    /**
     * 获取所有桶
     */
    List<Bucket> bucketList() throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    /**
     * 上传文件
     */
    FileInfoBO putObject(String bucketName, MultipartFile file, String year, String newContentType) throws IOException, MinioException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 获取对象元数据
     */
    StatObjectResponse getMetadata(String bucketName, String objectName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    /**
     * 删除文件
     */
    void removeObject(String bucketName, String objectName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 删除多个对象
     */
    Iterable<Result<DeleteError>> removeObject(String bucketName, List<String> objectNames) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 获取文件路径，可直接查看的路径
     */
    String getObjectUrl(String bucketName, String objectName) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 下载文件
     * originName：附件名称（如：附件123.pdf）
     */
    void downloadObject(String bucketName, String originName, String objectName, HttpServletResponse httpServletResponse);

    /**
     * 下载文件并保存到指定位置
     */
    void downloadObject(String bucketName, String objectName, String downToPathname) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;
}
