package com.boryou.servs.monitor.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.boryou.hongliansms.HlSmsCenter;
import com.boryou.hongliansms.bean.HlSmsResult;
import com.boryou.hongliansms.template.byyuqing.MsgSendDealTemplate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
public class ProxyIpTask {
    private static String proxyIpUrl;

    public void ipsMonitor() {
        String s1 = "18134536172,19156591196,15856913410";
        System.out.println("T->ipsMonitor() --- " + DateUtil.date());
        String src = "";
        try {
            src = HttpRequest.get(proxyIpUrl).header("Authorization", "29brGZ0QTeyBqH0c").setConnectionTimeout(20000).setReadTimeout(20000).execute().body();
        } catch (Exception e) {
            HlSmsResult<List<String>> sendResult = HlSmsCenter.sendSms(s1, new MsgSendDealTemplate("博约人", "【警报】代理ip接口服务异常", "暂无"));
            e.printStackTrace();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> resultMap;
            Map<String, Object> dataMap;
            List<Map<String, String>> ipList;
            resultMap = mapper.readValue(src, new TypeReference<Map<String, Object>>() {
            });
            int code = (int) resultMap.get("code");
            if (code != 200) {
                String msg = (String) resultMap.get("msg");
                HlSmsCenter.sendSms(s1, new MsgSendDealTemplate("博约人", "【警报】代理ip接口服务异常，" + msg, "暂无"));
            } else {
                dataMap = mapper.readValue(mapper.writeValueAsString(resultMap.get("data")), new TypeReference<Map<String, Object>>() {
                });
                ipList = mapper.readValue(mapper.writeValueAsString(dataMap.get("ips")), new TypeReference<List<Map<String, String>>>() {
                });
                if (ipList != null && ipList.size() > 0) {

                } else {
                    HlSmsCenter.sendSms(s1, new MsgSendDealTemplate("博约人", "【警报】对外代理ip接口服务获取到的ip数量为0", "暂无"));
                }
            }
        } catch (JsonProcessingException e) {
            HlSmsResult<List<String>> sendResult = HlSmsCenter.sendSms(s1, new MsgSendDealTemplate("博约人", "【警报】对外代理ip接口服务异常", "暂无"));
            e.printStackTrace();
        }
    }

    @Value("${monitor.proxyip.pro-url}")
    public void setProxyIpUrl(String proxyIpUrl) {
        ProxyIpTask.proxyIpUrl = proxyIpUrl;
    }
}
