##################################################
######  proxyip服务
##################################################

CREATE TABLE `pi_req_log_202110`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '调用日志ID',
    `tenant_flag`  char(5)      NOT NULL COMMENT '租户标记',
    `ip_port`      varchar(30)  NOT NULL COMMENT 'IP:PORT',
    `day`          tinyint(1)   NOT NULL COMMENT '日（1-31）',
    `req_time`     datetime     NOT NULL COMMENT '请求时间',
    `req_time_str` varchar(5000) NOT NULL DEFAULT '' COMMENT '重复记录请求时间串',
    `wipe_time`     datetime     NOT NULL COMMENT 'IP:PORT过期时间',
    `exp_time_str` varchar(5000) NOT NULL DEFAULT '' COMMENT 'API返回过期时间串',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_day_tflag_ipp` (`day`, `tenant_flag`, `ip_port`) USING BTREE,
    KEY `idx_tflag_rtime` (`tenant_flag`, `req_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='调用日志月表（202110）';

CREATE TABLE `pi_req_log_202111`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '调用日志ID',
    `tenant_flag`  char(5)      NOT NULL COMMENT '租户标记',
    `ip_port`      varchar(30)  NOT NULL COMMENT 'IP:PORT',
    `day`          tinyint(1)   NOT NULL COMMENT '日（1-31）',
    `req_time`     datetime     NOT NULL COMMENT '请求时间',
    `req_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT '重复记录请求时间串',
    `wipe_time`     datetime     NOT NULL COMMENT 'IP:PORT过期时间',
    `exp_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT 'API返回过期时间串',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_day_tflag_ipp` (`day`, `tenant_flag`, `ip_port`) USING BTREE,
    KEY `idx_tflag_rtime` (`tenant_flag`, `req_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='调用日志月表（202111）';

CREATE TABLE `pi_req_log_202112`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '调用日志ID',
    `tenant_flag`  char(5)      NOT NULL COMMENT '租户标记',
    `ip_port`      varchar(30)  NOT NULL COMMENT 'IP:PORT',
    `day`          tinyint(1)   NOT NULL COMMENT '日（1-31）',
    `req_time`     datetime     NOT NULL COMMENT '请求时间',
    `req_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT '重复记录请求时间串',
    `wipe_time`     datetime     NOT NULL COMMENT 'IP:PORT过期时间',
    `exp_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT 'API返回过期时间串',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_day_tflag_ipp` (`day`, `tenant_flag`, `ip_port`) USING BTREE,
    KEY `idx_tflag_rtime` (`tenant_flag`, `req_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='调用日志月表（202112）';

CREATE TABLE `pi_req_log_202201`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '调用日志ID',
    `tenant_flag`  char(5)      NOT NULL COMMENT '租户标记',
    `ip_port`      varchar(30)  NOT NULL COMMENT 'IP:PORT',
    `day`          tinyint(1)   NOT NULL COMMENT '日（1-31）',
    `req_time`     datetime     NOT NULL COMMENT '请求时间',
    `req_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT '重复记录请求时间串',
    `wipe_time`     datetime     NOT NULL COMMENT 'IP:PORT过期时间',
    `exp_time_str` varchar(2000) NOT NULL DEFAULT '' COMMENT 'API返回过期时间串',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_day_tflag_ipp` (`day`, `tenant_flag`, `ip_port`) USING BTREE,
    KEY `idx_tflag_rtime` (`tenant_flag`, `req_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='调用日志月表（202201）';

