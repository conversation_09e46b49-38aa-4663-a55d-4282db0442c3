##################################################
######  sms服务
##################################################

CREATE TABLE `ss_log`
(
    `id`          bigint(20)   NOT NULL COMMENT '调用日志ID',
    `log_content` varchar(500) NOT NULL DEFAULT '' COMMENT '日志内容',
    `proj_flag`   char(10)     NOT NULL DEFAULT '' COMMENT '项目标记',
    `tenant_flag` char(10)     NOT NULL DEFAULT '' COMMENT '租户标记',
    `biz_flag`    varchar(60)  NOT NULL DEFAULT '' COMMENT '业务标识',
    `req_time`    datetime     NOT NULL COMMENT '请求时间',
    `resp_time`   datetime     NOT NULL COMMENT '响应时间',
    `resp_code`   int(3)       NOT NULL DEFAULT 0 COMMENT '响应状态码',
    `resp_msg`    varchar(100) NOT NULL DEFAULT '' COMMENT '响应信息',
    `ctime`       datetime     NOT NULL COMMENT '记录落表时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='短信服务调用日志表';

CREATE TABLE `ss_template`
(
    `id`          bigint(20)   NOT NULL COMMENT '短信模板ID',
    `tpl_name`    varchar(20)  NOT NULL DEFAULT '' COMMENT '模板名称',
    `tpl_content` varchar(500) NOT NULL DEFAULT '' COMMENT '模板内容',
    `tpl_sign`    varchar(20)  NOT NULL DEFAULT '' COMMENT '模板标识',
    `proj_flag`   char(10)     NOT NULL DEFAULT '' COMMENT '项目标记',
    `tpl_status`  tinyint(1)   NOT NULL DEFAULT '1' COMMENT '模板状态（0禁用 *1启用）',
    `remark`      varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='短信模板表';
