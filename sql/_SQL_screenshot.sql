##################################################
######  screenshot服务
##################################################


CREATE TABLE `sc_prj`
(
    `id`         smallint(3) NOT NULL COMMENT '项目ID（3位数字）',
    `prj_name`   varchar(60) NOT NULL COMMENT '项目名称',
    `prj_code`   varchar(60) NOT NULL COMMENT '项目编码',
    `prj_secret` varchar(60) NOT NULL COMMENT '项目私钥',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目表';


CREATE TABLE `sc_record_2021`
(
    `id`          bigint(20)   NOT NULL COMMENT '截图记录ID',
    `prj_id`      smallint(3)  NOT NULL COMMENT '项目ID（3位数字）',
    `page_url`    varchar(500) NOT NULL COMMENT '目标页面URL',
    `file_id`     varchar(80)  NOT NULL COMMENT '文件ID',
    `file_url`    varchar(255) NOT NULL COMMENT '文件存储URL',
    `file_type`   varchar(50)  NOT NULL COMMENT '文件类型',
    `file_size`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '文件大小',
    `begin_time`  datetime     NOT NULL COMMENT '请求时间',
    `finish_time` datetime     NOT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_prjid_fileid` (`prj_id`, `file_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='截图记录表（2021）';
CREATE TABLE `sc_record_2022`
(
    `id`          bigint(20)   NOT NULL COMMENT '截图记录ID',
    `prj_id`      smallint(3)  NOT NULL COMMENT '项目ID（3位数字）',
    `page_url`    varchar(500) NOT NULL COMMENT '目标页面URL',
    `file_id`     varchar(80)  NOT NULL COMMENT '文件ID',
    `file_url`    varchar(255) NOT NULL COMMENT '文件存储URL',
    `file_type`   varchar(50)  NOT NULL COMMENT '文件类型',
    `file_size`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '文件大小',
    `begin_time`  datetime     NOT NULL COMMENT '请求时间',
    `finish_time` datetime     NOT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_prjid_fileid` (`prj_id`, `file_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='截图记录表（2022）';
CREATE TABLE `sc_record_2023`
(
    `id`          bigint(20)   NOT NULL COMMENT '截图记录ID',
    `prj_id`      smallint(3)  NOT NULL COMMENT '项目ID（3位数字）',
    `page_url`    varchar(500) NOT NULL COMMENT '目标页面URL',
    `file_id`     varchar(80)  NOT NULL COMMENT '文件ID',
    `file_url`    varchar(255) NOT NULL COMMENT '文件存储URL',
    `file_type`   varchar(50)  NOT NULL COMMENT '文件类型',
    `file_size`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '文件大小',
    `begin_time`  datetime     NOT NULL COMMENT '请求时间',
    `finish_time` datetime     NOT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_prjid_fileid` (`prj_id`, `file_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='截图记录表（2023）';
CREATE TABLE `sc_record_2024`
(
    `id`          bigint(20)   NOT NULL COMMENT '截图记录ID',
    `prj_id`      smallint(3)  NOT NULL COMMENT '项目ID（3位数字）',
    `page_url`    varchar(500) NOT NULL COMMENT '目标页面URL',
    `file_id`     varchar(80)  NOT NULL COMMENT '文件ID',
    `file_url`    varchar(255) NOT NULL COMMENT '文件存储URL',
    `file_type`   varchar(50)  NOT NULL COMMENT '文件类型',
    `file_size`   bigint(20)   NOT NULL DEFAULT '0' COMMENT '文件大小',
    `begin_time`  datetime     NOT NULL COMMENT '请求时间',
    `finish_time` datetime     NOT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_prjid_fileid` (`prj_id`, `file_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='截图记录表（2024）';

