##################################################
######  pt-servs（公共）
##################################################

CREATE DATABASE IF NOT EXISTS `pt-servs` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_general_ci';


CREATE TABLE `pt_config`
(
    `id`           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_name`  varchar(50)  NOT NULL COMMENT '配置名',
    `config_value` varchar(500) NOT NULL DEFAULT '' COMMENT '配置值',
    `module`       varchar(50)  NOT NULL DEFAULT '' COMMENT '模块',
    `remark`       varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_cfgname_module` (`config_name`, `module`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '配置表';
