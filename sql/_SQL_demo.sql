##################################################
######  demo示例
##################################################


CREATE TABLE IF NOT EXISTS `aa_demo`
(
    `id`          bigint(20)     NOT NULL COMMENT 'demoID',
    `cat_id`      bigint(20)     NOT NULL COMMENT 'demo分类ID',
    `tag_id`      bigint(20)     NOT NULL COMMENT 'demo标签ID',
    `demo_name`   varchar(100)   NOT NULL COMMENT 'demo名称',
    `price`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '价格（元）',
    `demo_status` tinyint(1)     NOT NULL DEFAULT '0' COMMENT 'demo状态（*0禁用 1启用 2冻结）',
    `ctime`       datetime                DEFAULT NULL COMMENT '创建时间',
    `utime`       datetime                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `idx_cat_id` (`cat_id`) USING BTREE,
    KEY `idx_tag_id` (`tag_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='demo表';


CREATE TABLE IF NOT EXISTS `aa_demo_tag`
(
    `id`           bigint(20)    NOT NULL COMMENT 'demo标签ID',
    `tag_name`     varchar(50)   NOT NULL COMMENT 'demo标签名称',
    `tag_img_url`  varchar(200)  NOT NULL DEFAULT '' COMMENT 'demo标签图片URL',
    `first_letter` char(1)       NOT NULL COMMENT 'demo标签名称首字母（大写）',
    `tag_desc`     varchar(1000) NOT NULL DEFAULT '' COMMENT 'demo标签描述',
    `tag_status`   tinyint(1)    NOT NULL DEFAULT '1' COMMENT 'demo标签状态（0禁用 *1启用）',
    `ctime`        datetime               DEFAULT NULL COMMENT '创建时间',
    `utime`        datetime               DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unq_tag_name` (`tag_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='demo标签表';


CREATE TABLE IF NOT EXISTS `aa_demo_category`
(
    `id`         bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'demo分类ID',
    `pid`        bigint(20)  NOT NULL COMMENT 'demo分类父ID',
    `cat_name`   varchar(50) NOT NULL COMMENT 'demo分类名称',
    `cat_level`  tinyint(1)  NOT NULL COMMENT '层级（1顶级 2二级 3三级）',
    `cat_icon`   varchar(30) NOT NULL DEFAULT '' COMMENT '图标CSS类名',
    `sort_by`    int(11)     NOT NULL DEFAULT '0' COMMENT '排序值',
    `cat_status` tinyint(1)  NOT NULL DEFAULT '1' COMMENT 'demo分类状态（0禁用 *1启用）',
    `ctime`      datetime             DEFAULT NULL COMMENT '创建时间',
    `utime`      datetime             DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_pid` (`pid`) USING BTREE,
    UNIQUE KEY `unq_cat_name` (`cat_name`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4 COMMENT ='demo分类表';

