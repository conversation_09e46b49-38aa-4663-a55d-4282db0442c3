package com.boryou.servs.proxyip.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.boryou.servs.common.bean.Output;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.proxyip.pojo.bo.TenantBO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static com.boryou.servs.proxyip.constant.RedisConstant.AU_IP_WHITE;

/**
 * <AUTHOR> qiqd
 */
public class AheadController {

    @Autowired
    protected HttpServletRequest request;

    @Autowired
    protected HttpServletResponse response;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取租户标识（tenantFlag），包含对Authorization和IP的验证
     */
    protected String getTenantToken() {
        String authorization = request.getHeader("Authorization");
        String clientIp = ServletUtil.getClientIP(request);

        Map<String, TenantBO> tenantFlagMap = new HashMap<>();

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> auIpWhiteMap = null;
        try {
            auIpWhiteMap = objectMapper.readValue(redisUtil.get(AU_IP_WHITE), new TypeReference<Map<String, String>>() {
            });
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if (auIpWhiteMap.size() > 0) {
            for (Map.Entry<String, String> entry : auIpWhiteMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String[] keyArr = key.split(",");
                TenantBO tenantBO = new TenantBO();
                tenantBO.setTenantFlag(keyArr[1]);
                tenantBO.setIpWhiteListStr(value);
                tenantFlagMap.put(keyArr[0], tenantBO);
            }
        }
        String tenantFlag = "";
        if (StrUtil.isNotBlank(authorization)) {
            if (tenantFlagMap.containsKey(authorization)) {
                TenantBO tenantBO = tenantFlagMap.get(authorization);
                tenantFlag = tenantBO.getTenantFlag();
                String ipWhiteListStr = tenantBO.getIpWhiteListStr();
                String[] ipWhiteAry = ipWhiteListStr.split(",");
                List<String> ipWhiteAndLocalList = new ArrayList<>();
                Collections.addAll(ipWhiteAndLocalList, ipWhiteAry);

                if (clientIp.startsWith("192.168.")) {
                    // 内网放行
                } else {
                    ipWhiteAndLocalList.add("127.0.0.1");
                    ipWhiteAndLocalList.add("0:0:0:0:0:0:0:1");
                    if (!ipWhiteAndLocalList.contains(clientIp)) {
                        Output.error(response, "非法访问1");
                        return null;
                    }
                }

                // 验证通过，返回租户标识
                return tenantFlag;
            }
        }
        Output.error(response, "非法访问2");
        return null;
    }

}
