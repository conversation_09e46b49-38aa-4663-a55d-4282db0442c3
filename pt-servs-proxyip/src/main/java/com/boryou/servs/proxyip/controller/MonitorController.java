package com.boryou.servs.proxyip.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.common.util.TimeUtil;
import com.boryou.servs.proxyip.constant.RedisConstant;
import com.boryou.servs.proxyip.pojo.bo.ProxyipBO;
import com.boryou.servs.proxyip.pojo.bo.SrcProxyipBO;
import com.boryou.servs.proxyip.pojo.vo.IpTimeVO;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpTimeVO;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.servs.proxyip.constant.RedisConstant.TTL_BATCH;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor")
public class MonitorController {
    @Autowired
    private RedisUtil redisUtil;

    @RequestMapping("/ips")
    public Return ips(Integer num, Integer dataType) {
        if (dataType == null) {
            dataType = 1;
        }
        // 单次可调用的IP数范围[1,1000]
        if (num < 1 || num > 1000) {
            return Return.error("参数有误");
        }

        String redisKeyBatch = RedisConstant.BATCH_PREFIX + "*";
        Set<String> keys = redisUtil.keys(redisKeyBatch);
        if (keys.size() > 0) {
            // 逆序（key中拼接了代理IP过期时间）的目的：优先给租户提供有效时间相对更长的IP
            List<String> keyList = keys.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

            // 组装后，存入redis，等待定时任务消费到DB中
            List<ProxyipBO> proxyipBOList = new ArrayList<>();

            // 使用Set的目的：确保单次返回给租户的IP:PORT不重复
            Set<String> proxyipSet = new HashSet<>();

            MARK_OUT:
            for (String key : keyList) {
                // key中后14位是过期时间（yyyyMMddHHmmss）
                String expireTimeStr = TimeUtil.getDateTimeStrFromNumStr(StrUtil.subSuf(key, -14));

                String batchJsonStr = redisUtil.get(key);
                JSONArray objects = JSONUtil.parseArray(batchJsonStr);
                List<SrcProxyipBO> srcProxyipBOList = objects.toList(SrcProxyipBO.class);

                // 打乱的目的：某一批次IP如果无需全量返回时，可以尽量保证其随机性
                Collections.shuffle(srcProxyipBOList);

                // 遍历缓存中存储的某批次IP信息
                for (SrcProxyipBO srcProxyipBO : srcProxyipBOList) {
                    String ip = srcProxyipBO.getIp();
                    Integer port = srcProxyipBO.getPort();
                    String ipPort = ip + ":" + port;
                    proxyipSet.add(ipPort);

                    ProxyipBO proxyipBO = new ProxyipBO();
                    proxyipBO.setIp(ip);
                    proxyipBO.setPort(port);
                    proxyipBO.setExpTimeStr(expireTimeStr);
                    proxyipBOList.add(proxyipBO);

                    // 达到请求的IP数（num）时跳出外层循环
                    if (proxyipSet.size() == num) {
                        break MARK_OUT;
                    }
                }
            }
            // 包装成租户需要的VO形式返回
            if (dataType == 2) {
                ProxyIpTimeVO proxyIpTimeVO = new ProxyIpTimeVO();
                List<IpTimeVO> ipTimeVOList = new ArrayList<>();
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                for (ProxyipBO proxyipBO : proxyipBOList) {
                    IpTimeVO ipTimeVO = new IpTimeVO();
                    ipTimeVO.setIp(proxyipBO.getIp() + ":" + proxyipBO.getPort());
                    LocalDateTime ttlTime = LocalDateTime.parse(proxyipBO.getExpTimeStr(), df);
                    LocalDateTime createTime = ttlTime.minusSeconds(TTL_BATCH + 5);
                    ipTimeVO.setGenTime(df.format(createTime));
                    ipTimeVO.setExpTime(df.format(createTime.plusSeconds(300)));
                    ipTimeVOList.add(ipTimeVO);
                }
                proxyIpTimeVO.setIps(ipTimeVOList);

                return Return.ok("ok", proxyIpTimeVO);
            } else {
                ProxyIpVO proxyipVO = new ProxyIpVO();
                proxyipVO.setIps(new ArrayList<>(proxyipSet));

                return Return.ok("ok", proxyipVO);
            }
        }
        return Return.error("err", "");
    }
}
