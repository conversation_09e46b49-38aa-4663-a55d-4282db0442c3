package com.boryou.servs.proxyip.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;

/**
 * 定时任务（配置见：pt-common:src/main/resources/config/cron.setting）
 *
 * <AUTHOR>
 */
public class CronTask {

    /**
     * 定时获取IP
     */
    public void runGain() {
        System.out.println("T->runGain() --- " + DateUtil.date());
        HttpUtil.get("http://127.0.0.1:36520/proxy/gain");
    }

    /**
     * 定时消费租户日志
     */
    public void runReqLog() {
//        System.out.println("T->runReqLog() --- " + DateUtil.date());
        HttpUtil.get("http://127.0.0.1:36520/proxy/reqLog");
    }
}
