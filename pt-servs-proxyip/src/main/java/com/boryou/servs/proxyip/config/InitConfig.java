package com.boryou.servs.proxyip.config;

import com.boryou.servs.proxyip.service.ProxyIpConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;

/**
 * 启动时自动加载系统配置
 *
 * <AUTHOR>
 */
@Component
@ResponseBody
@RequestMapping("/config")
public class InitConfig {
    @Autowired
    private ProxyIpConfigService proxyIpConfigService;

    @PostConstruct
    @GetMapping("/init")
    public void init() {
        System.out.println("开始加载proxyip模块系统配置");
        proxyIpConfigService.initConfig();
        System.out.println("proxyip配置加载结束");
    }
}
