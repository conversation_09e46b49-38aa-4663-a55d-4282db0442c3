package com.boryou.servs.proxyip.pojo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 调用日志月表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("pi_req_log")
public class ReqLogPO {

    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    public Long id;

    private String tenantFlag;

    private String ipPort;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reqTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wipeTime;

    private Integer day;

    private String reqTimeStr;

    private String expTimeStr;

}
