package com.boryou.servs.proxyip.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.proxyip.mapper.ProxyIpConfigMapper;
import com.boryou.servs.proxyip.pojo.sb.ProxyIpConfigSB;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpConfigVO;
import com.boryou.servs.proxyip.service.ProxyIpConfigService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.boryou.servs.proxyip.constant.RedisConstant.AU_IP_WHITE;
import static com.boryou.servs.proxyip.constant.RedisConstant.URL;

/**
 * <AUTHOR>
 */
@Service
public class ProxyIpConfigServiceImpl extends ServiceImpl<ProxyIpConfigMapper, ProxyIpConfigVO> implements ProxyIpConfigService {
    @Autowired
    private ProxyIpConfigMapper proxyIpConfigMapper;

    @Autowired
    private ProxyIpConfigService proxyIpConfigService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<ProxyIpConfigVO> selectProxyIpConfigList(ProxyIpConfigSB proxyIpConfigSB) {
        String configName = proxyIpConfigSB.getConfigName();
        String module = proxyIpConfigSB.getModule();
        QueryWrapper<ProxyIpConfigVO> queryWrapper = new QueryWrapper<>();
        if (configName != null) {
            queryWrapper.eq("config_name", configName);
        }
        if (module != null) {
            queryWrapper.eq("module", module);
        }
        queryWrapper.orderByDesc("id");
        return proxyIpConfigMapper.selectList(queryWrapper);
    }

    @Override
    public ProxyIpConfigVO selectProxyIpConfig(ProxyIpConfigSB proxyIpConfigSB) {
        QueryWrapper<ProxyIpConfigVO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_name", proxyIpConfigSB.getConfigName());
        queryWrapper.eq("module", proxyIpConfigSB.getModule());
        return proxyIpConfigMapper.selectOne(queryWrapper);
    }

    @Override
    public void initConfig() {
        ObjectMapper objectMapper = new ObjectMapper();
        ProxyIpConfigSB proxyIpConfigSB = new ProxyIpConfigSB();
        proxyIpConfigSB.setModule("proxyip");
        List<ProxyIpConfigVO> proxyIpConfigVOList = proxyIpConfigService.selectProxyIpConfigList(proxyIpConfigSB);
        if (proxyIpConfigVOList != null && proxyIpConfigVOList.size() > 0) {
            List<String> auIpWhiteList = new ArrayList<>();
            String url = "";
            for (ProxyIpConfigVO entity : proxyIpConfigVOList) {
                String configName = entity.getConfigName();
                String configValue = entity.getConfigValue();
                if ("url".equals(configName)) {
                    url = configValue;
                } else if (configName.startsWith("au_ipWhite")) {
                    auIpWhiteList.add(configValue);
                }
            }
            Map<String, String> auIpWhiteMap = new HashMap<>();
            if (auIpWhiteList.size() > 0) {
                for (String str : auIpWhiteList) {
                    try {
                        Map<String, String> map = objectMapper.readValue(str, new TypeReference<Map<String, String>>() {
                        });
                        auIpWhiteMap.putAll(map);
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                }
            }
            try {
                redisUtil.set(AU_IP_WHITE, objectMapper.writeValueAsString(auIpWhiteMap));
                redisUtil.set(URL, url);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
    }
}
