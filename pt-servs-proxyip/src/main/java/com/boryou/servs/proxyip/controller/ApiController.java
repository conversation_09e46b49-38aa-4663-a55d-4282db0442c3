package com.boryou.servs.proxyip.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.common.constant.BasicConstant;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.common.util.TimeUtil;
import com.boryou.servs.proxyip.constant.RedisConstant;
import com.boryou.servs.proxyip.pojo.bo.ProxyipBO;
import com.boryou.servs.proxyip.pojo.bo.SrcProxyipBO;
import com.boryou.servs.proxyip.pojo.po.ReqLogPO;
import com.boryou.servs.proxyip.pojo.vo.IpTimeVO;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpTimeVO;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpVO;
import com.boryou.servs.proxyip.service.ReqLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.boryou.servs.proxyip.constant.RedisConstant.TTL_BATCH;
import static com.boryou.servs.proxyip.constant.RedisConstant.URL;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/proxy")
public class ApiController extends AheadController {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ReqLogService reqLogService;

    /**
     * 从供应商获取代理IP
     */
    @GetMapping("/gain")
    public void gain() {
        // 写死的数据
//        String jsonStr = "{\"serialNo\":\"ee196547626041848a99cdc68bdd70e4\",\"code\":0,\"data\":[{\"realIp\":\"**************\",\"pid\":27,\"cid\":340,\"area\":\"江苏-南通\",\"ip\":\"**************\",\"port\":26390},{\"realIp\":\"**************\",\"pid\":22,\"cid\":261,\"area\":\"福建-泉州\",\"ip\":\"**************\",\"port\":27768},{\"realIp\":\"*************\",\"pid\":28,\"cid\":344,\"area\":\"安徽-淮北\",\"ip\":\"**************\",\"port\":29919},{\"realIp\":\"**************\",\"pid\":21,\"cid\":250,\"area\":\"广东-中山\",\"ip\":\"**************\",\"port\":30604},{\"realIp\":\"************\",\"pid\":6,\"cid\":35,\"area\":\"辽宁-辽阳\",\"ip\":\"**************\",\"port\":31830},{\"realIp\":\"***************\",\"pid\":25,\"cid\":305,\"area\":\"江西-鹰潭\",\"ip\":\"**************\",\"port\":32028},{\"realIp\":\"**************\",\"pid\":18,\"cid\":195,\"area\":\"四川-达州\",\"ip\":\"**************\",\"port\":32710},{\"realIp\":\"*************\",\"pid\":28,\"cid\":356,\"area\":\"安徽-芜湖\",\"ip\":\"**************\",\"port\":33852},{\"realIp\":\"**************\",\"pid\":22,\"cid\":254,\"area\":\"福建-莆田\",\"ip\":\"**************\",\"port\":34463},{\"realIp\":\"*************\",\"pid\":27,\"cid\":335,\"area\":\"江苏-连云港\",\"ip\":\"**************\",\"port\":34775},{\"realIp\":\"*************\",\"pid\":18,\"cid\":192,\"area\":\"四川-乐山\",\"ip\":\"**************\",\"port\":35344},{\"realIp\":\"*************\",\"pid\":27,\"cid\":331,\"area\":\"江苏-镇江\",\"ip\":\"**************\",\"port\":35405},{\"realIp\":\"**************\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":28031},{\"realIp\":\"*************\",\"pid\":21,\"cid\":252,\"area\":\"广东-揭阳\",\"ip\":\"**************\",\"port\":32096},{\"realIp\":\"**************\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"**************\",\"port\":37844},{\"realIp\":\"**************\",\"pid\":28,\"cid\":348,\"area\":\"安徽-池州\",\"ip\":\"**************\",\"port\":30101},{\"realIp\":\"*************\",\"pid\":24,\"cid\":288,\"area\":\"湖北-咸宁\",\"ip\":\"**************\",\"port\":30141},{\"realIp\":\"************\",\"pid\":27,\"cid\":331,\"area\":\"江苏-镇江\",\"ip\":\"**************\",\"port\":36707},{\"realIp\":\"**************\",\"pid\":3,\"cid\":4,\"area\":\"重庆-重庆\",\"ip\":\"**************\",\"port\":21441},{\"realIp\":\"**************\",\"pid\":13,\"cid\":120,\"area\":\"山东-淄博\",\"ip\":\"**************\",\"port\":21595},{\"realIp\":\"************\",\"pid\":28,\"cid\":345,\"area\":\"安徽-铜陵\",\"ip\":\"**************\",\"port\":21708},{\"realIp\":\"*************\",\"pid\":28,\"cid\":356,\"area\":\"安徽-芜湖\",\"ip\":\"**************\",\"port\":23787},{\"realIp\":\"************\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"**************\",\"port\":25158},{\"realIp\":\"***************\",\"pid\":26,\"cid\":324,\"area\":\"浙江-温州\",\"ip\":\"**************\",\"port\":25699},{\"realIp\":\"**************\",\"pid\":22,\"cid\":257,\"area\":\"福建-宁德\",\"ip\":\"**************\",\"port\":22821},{\"realIp\":\"*************\",\"pid\":18,\"cid\":201,\"area\":\"四川-德阳\",\"ip\":\"**************\",\"port\":35699},{\"realIp\":\"**************\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"**************\",\"port\":35869},{\"realIp\":\"***************\",\"pid\":25,\"cid\":305,\"area\":\"江西-鹰潭\",\"ip\":\"**************\",\"port\":38990},{\"realIp\":\"***********\",\"pid\":27,\"cid\":331,\"area\":\"江苏-镇江\",\"ip\":\"**************\",\"port\":39200},{\"realIp\":\"**************\",\"pid\":14,\"cid\":140,\"area\":\"河北-唐山\",\"ip\":\"**************\",\"port\":39089},{\"realIp\":\"**************\",\"pid\":18,\"cid\":192,\"area\":\"四川-乐山\",\"ip\":\"**************\",\"port\":20395},{\"realIp\":\"**************\",\"pid\":26,\"cid\":316,\"area\":\"浙江-嘉兴\",\"ip\":\"**************\",\"port\":20264},{\"realIp\":\"*************\",\"pid\":25,\"cid\":305,\"area\":\"江西-鹰潭\",\"ip\":\"**************\",\"port\":20067},{\"realIp\":\"**************\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":20191},{\"realIp\":\"***************\",\"pid\":25,\"cid\":307,\"area\":\"江西-抚州\",\"ip\":\"**************\",\"port\":20221},{\"realIp\":\"***********\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":20298},{\"realIp\":\"************\",\"pid\":28,\"cid\":350,\"area\":\"安徽-亳州\",\"ip\":\"**************\",\"port\":21234},{\"realIp\":\"**************\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":20926},{\"realIp\":\"**************\",\"pid\":6,\"cid\":35,\"area\":\"辽宁-辽阳\",\"ip\":\"**************\",\"port\":39935},{\"realIp\":\"*************\",\"pid\":6,\"cid\":35,\"area\":\"辽宁-辽阳\",\"ip\":\"**************\",\"port\":39955},{\"realIp\":\"**************\",\"pid\":22,\"cid\":254,\"area\":\"福建-莆田\",\"ip\":\"**************\",\"port\":33070},{\"realIp\":\"************\",\"pid\":13,\"cid\":115,\"area\":\"山东-滨州\",\"ip\":\"**************\",\"port\":31469},{\"realIp\":\"*************\",\"pid\":13,\"cid\":115,\"area\":\"山东-滨州\",\"ip\":\"**************\",\"port\":38081},{\"realIp\":\"*************\",\"pid\":21,\"cid\":236,\"area\":\"广东-汕头\",\"ip\":\"**************\",\"port\":33130},{\"realIp\":\"***************\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":38141},{\"realIp\":\"*************\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":38211},{\"realIp\":\"**************\",\"pid\":6,\"cid\":43,\"area\":\"辽宁-抚顺\",\"ip\":\"**************\",\"port\":33459},{\"realIp\":\"***************\",\"pid\":18,\"cid\":205,\"area\":\"四川-眉山\",\"ip\":\"**************\",\"port\":33689},{\"realIp\":\"*************\",\"pid\":27,\"cid\":335,\"area\":\"江苏-连云港\",\"ip\":\"**************\",\"port\":33819},{\"realIp\":\"**************\",\"pid\":7,\"cid\":58,\"area\":\"内蒙古-鄂尔多斯\",\"ip\":\"**************\",\"port\":25411},{\"realIp\":\"175.155.48.232\",\"pid\":18,\"cid\":192,\"area\":\"四川-乐山\",\"ip\":\"**************\",\"port\":28426},{\"realIp\":\"220.161.101.49\",\"pid\":22,\"cid\":254,\"area\":\"福建-莆田\",\"ip\":\"**************\",\"port\":28863},{\"realIp\":\"175.175.194.125\",\"pid\":6,\"cid\":43,\"area\":\"辽宁-抚顺\",\"ip\":\"**************\",\"port\":27122},{\"realIp\":\"114.99.1.158\",\"pid\":28,\"cid\":345,\"area\":\"安徽-铜陵\",\"ip\":\"**************\",\"port\":27332},{\"realIp\":\"183.165.249.127\",\"pid\":28,\"cid\":353,\"area\":\"安徽-六安\",\"ip\":\"**************\",\"port\":26670},{\"realIp\":\"101.206.104.150\",\"pid\":18,\"cid\":201,\"area\":\"四川-德阳\",\"ip\":\"**************\",\"port\":29737},{\"realIp\":\"115.209.212.56\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":30505},{\"realIp\":\"219.159.100.58\",\"pid\":20,\"cid\":214,\"area\":\"广西-桂林\",\"ip\":\"**************\",\"port\":31563},{\"realIp\":\"117.94.221.151\",\"pid\":27,\"cid\":332,\"area\":\"江苏-泰州\",\"ip\":\"**************\",\"port\":30870},{\"realIp\":\"117.43.52.196\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"**************\",\"port\":31182},{\"realIp\":\"113.120.62.39\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":33533},{\"realIp\":\"114.99.252.174\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":37035},{\"realIp\":\"183.7.113.25\",\"pid\":21,\"cid\":236,\"area\":\"广东-汕头\",\"ip\":\"**************\",\"port\":38848},{\"realIp\":\"221.226.192.211\",\"pid\":27,\"cid\":337,\"area\":\"江苏-南京\",\"ip\":\"**************\",\"port\":37912},{\"realIp\":\"27.191.61.24\",\"pid\":14,\"cid\":140,\"area\":\"河北-唐山\",\"ip\":\"**************\",\"port\":39330},{\"realIp\":\"121.207.84.129\",\"pid\":22,\"cid\":261,\"area\":\"福建-泉州\",\"ip\":\"**************\",\"port\":36343},{\"realIp\":\"119.7.136.226\",\"pid\":18,\"cid\":198,\"area\":\"四川-泸州\",\"ip\":\"**************\",\"port\":27086},{\"realIp\":\"59.61.164.191\",\"pid\":22,\"cid\":258,\"area\":\"福建-福州\",\"ip\":\"**************\",\"port\":26403},{\"realIp\":\"*************\",\"pid\":22,\"cid\":260,\"area\":\"福建-三明\",\"ip\":\"**************\",\"port\":25830},{\"realIp\":\"*************\",\"pid\":28,\"cid\":345,\"area\":\"安徽-铜陵\",\"ip\":\"**************\",\"port\":23870},{\"realIp\":\"**************\",\"pid\":7,\"cid\":50,\"area\":\"内蒙古-锡林郭勒盟\",\"ip\":\"**************\",\"port\":36249},{\"realIp\":\"*************\",\"pid\":22,\"cid\":254,\"area\":\"福建-莆田\",\"ip\":\"**************\",\"port\":28226},{\"realIp\":\"**************\",\"pid\":14,\"cid\":142,\"area\":\"河北-沧州\",\"ip\":\"**************\",\"port\":35920},{\"realIp\":\"*************\",\"pid\":28,\"cid\":350,\"area\":\"安徽-亳州\",\"ip\":\"**************\",\"port\":38455},{\"realIp\":\"*************\",\"pid\":22,\"cid\":256,\"area\":\"福建-南平\",\"ip\":\"**************\",\"port\":35410},{\"realIp\":\"***************\",\"pid\":18,\"cid\":188,\"area\":\"四川-成都\",\"ip\":\"**************\",\"port\":37267},{\"realIp\":\"*************\",\"pid\":14,\"cid\":141,\"area\":\"河北-保定\",\"ip\":\"**************\",\"port\":37245},{\"realIp\":\"**************\",\"pid\":28,\"cid\":355,\"area\":\"安徽-合肥\",\"ip\":\"**************\",\"port\":34903},{\"realIp\":\"**************\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":35961},{\"realIp\":\"************\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"**************\",\"port\":39188},{\"realIp\":\"**********\",\"pid\":27,\"cid\":340,\"area\":\"江苏-南通\",\"ip\":\"**************\",\"port\":33189},{\"realIp\":\"*************\",\"pid\":3,\"cid\":4,\"area\":\"重庆-重庆\",\"ip\":\"**************\",\"port\":35969},{\"realIp\":\"***************\",\"pid\":18,\"cid\":195,\"area\":\"四川-达州\",\"ip\":\"**************\",\"port\":20208},{\"realIp\":\"************\",\"pid\":27,\"cid\":336,\"area\":\"江苏-淮安\",\"ip\":\"**************\",\"port\":21424},{\"realIp\":\"*************\",\"pid\":14,\"cid\":140,\"area\":\"河北-唐山\",\"ip\":\"**************\",\"port\":23629},{\"realIp\":\"************\",\"pid\":22,\"cid\":262,\"area\":\"福建-漳州\",\"ip\":\"**************\",\"port\":25367},{\"realIp\":\"***************\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"**************\",\"port\":25645},{\"realIp\":\"*************\",\"pid\":27,\"cid\":332,\"area\":\"江苏-泰州\",\"ip\":\"**************\",\"port\":26127},{\"realIp\":\"**************\",\"pid\":23,\"cid\":269,\"area\":\"湖南-益阳\",\"ip\":\"**************\",\"port\":25720},{\"realIp\":\"***********\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":27293},{\"realIp\":\"************\",\"pid\":14,\"cid\":140,\"area\":\"河北-唐山\",\"ip\":\"**************\",\"port\":27925},{\"realIp\":\"*************\",\"pid\":22,\"cid\":260,\"area\":\"福建-三明\",\"ip\":\"**************\",\"port\":29795},{\"realIp\":\"**************\",\"pid\":28,\"cid\":355,\"area\":\"安徽-合肥\",\"ip\":\"**************\",\"port\":27211},{\"realIp\":\"**************\",\"pid\":14,\"cid\":134,\"area\":\"河北-石家庄\",\"ip\":\"**************\",\"port\":30275},{\"realIp\":\"***************\",\"pid\":27,\"cid\":341,\"area\":\"江苏-常州\",\"ip\":\"**************\",\"port\":30516},{\"realIp\":\"***************\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":30826},{\"realIp\":\"*************\",\"pid\":22,\"cid\":258,\"area\":\"福建-福州\",\"ip\":\"**************\",\"port\":27972},{\"realIp\":\"***************\",\"pid\":22,\"cid\":256,\"area\":\"福建-南平\",\"ip\":\"**************\",\"port\":33478},{\"realIp\":\"*************\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":32320},{\"realIp\":\"************\",\"pid\":27,\"cid\":336,\"area\":\"江苏-淮安\",\"ip\":\"**************\",\"port\":31600},{\"realIp\":\"***********\",\"pid\":26,\"cid\":318,\"area\":\"浙江-金华\",\"ip\":\"**************\",\"port\":27014},{\"realIp\":\"*************\",\"pid\":13,\"cid\":120,\"area\":\"山东-淄博\",\"ip\":\"**************\",\"port\":34411},{\"realIp\":\"**************\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":34540},{\"realIp\":\"************\",\"pid\":27,\"cid\":336,\"area\":\"江苏-淮安\",\"ip\":\"**************\",\"port\":33847},{\"realIp\":\"************\",\"pid\":22,\"cid\":261,\"area\":\"福建-泉州\",\"ip\":\"**************\",\"port\":35858},{\"realIp\":\"**************\",\"pid\":21,\"cid\":250,\"area\":\"广东-中山\",\"ip\":\"**************\",\"port\":34847},{\"realIp\":\"**************\",\"pid\":21,\"cid\":252,\"area\":\"广东-揭阳\",\"ip\":\"**************\",\"port\":35007},{\"realIp\":\"************\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":30454},{\"realIp\":\"*************\",\"pid\":28,\"cid\":344,\"area\":\"安徽-淮北\",\"ip\":\"**************\",\"port\":37286},{\"realIp\":\"***************\",\"pid\":6,\"cid\":35,\"area\":\"辽宁-辽阳\",\"ip\":\"**************\",\"port\":36204},{\"realIp\":\"************\",\"pid\":25,\"cid\":416,\"area\":\"江西-吉安\",\"ip\":\"**************\",\"port\":32451},{\"realIp\":\"**************\",\"pid\":21,\"cid\":229,\"area\":\"广东-韶关\",\"ip\":\"**************\",\"port\":22657},{\"realIp\":\"**************\",\"pid\":22,\"cid\":257,\"area\":\"福建-宁德\",\"ip\":\"**************\",\"port\":23536},{\"realIp\":\"**************\",\"pid\":26,\"cid\":324,\"area\":\"浙江-温州\",\"ip\":\"**************\",\"port\":25289},{\"realIp\":\"***************\",\"pid\":18,\"cid\":198,\"area\":\"四川-泸州\",\"ip\":\"**************\",\"port\":25967},{\"realIp\":\"************\",\"pid\":21,\"cid\":250,\"area\":\"广东-中山\",\"ip\":\"**************\",\"port\":28719},{\"realIp\":\"*************\",\"pid\":28,\"cid\":345,\"area\":\"安徽-铜陵\",\"ip\":\"**************\",\"port\":27365},{\"realIp\":\"**************\",\"pid\":14,\"cid\":136,\"area\":\"河北-张家口\",\"ip\":\"**************\",\"port\":30249},{\"realIp\":\"117.69.150.126\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"**************\",\"port\":30297},{\"realIp\":\"183.165.224.57\",\"pid\":28,\"cid\":353,\"area\":\"安徽-六安\",\"ip\":\"**************\",\"port\":30507},{\"realIp\":\"14.134.200.111\",\"pid\":8,\"cid\":63,\"area\":\"宁夏-固原\",\"ip\":\"**************\",\"port\":31403},{\"realIp\":\"120.38.240.120\",\"pid\":22,\"cid\":256,\"area\":\"福建-南平\",\"ip\":\"**************\",\"port\":31658},{\"realIp\":\"183.143.38.183\",\"pid\":26,\"cid\":308,\"area\":\"浙江-湖州\",\"ip\":\"**************\",\"port\":26234},{\"realIp\":\"115.226.226.203\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":30405},{\"realIp\":\"122.241.11.113\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"112.28.231.246\",\"port\":25759},{\"realIp\":\"183.165.227.250\",\"pid\":28,\"cid\":353,\"area\":\"安徽-六安\",\"ip\":\"112.28.231.246\",\"port\":24511},{\"realIp\":\"114.104.239.218\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"112.28.231.246\",\"port\":24911},{\"realIp\":\"223.244.179.19\",\"pid\":28,\"cid\":346,\"area\":\"安徽-滁州\",\"ip\":\"112.28.231.246\",\"port\":28067},{\"realIp\":\"119.41.207.94\",\"pid\":30,\"cid\":367,\"area\":\"海南-海口\",\"ip\":\"112.28.231.246\",\"port\":32171},{\"realIp\":\"180.109.126.182\",\"pid\":27,\"cid\":337,\"area\":\"江苏-南京\",\"ip\":\"112.28.231.246\",\"port\":34780},{\"realIp\":\"180.105.146.36\",\"pid\":27,\"cid\":335,\"area\":\"江苏-连云港\",\"ip\":\"112.28.231.246\",\"port\":35575},{\"realIp\":\"140.224.112.46\",\"pid\":22,\"cid\":258,\"area\":\"福建-福州\",\"ip\":\"112.28.231.246\",\"port\":30338},{\"realIp\":\"59.55.162.103\",\"pid\":25,\"cid\":416,\"area\":\"江西-吉安\",\"ip\":\"112.28.231.246\",\"port\":35739},{\"realIp\":\"115.226.234.71\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"112.28.231.246\",\"port\":30418},{\"realIp\":\"27.189.129.185\",\"pid\":14,\"cid\":139,\"area\":\"河北-廊坊\",\"ip\":\"112.28.231.246\",\"port\":33404},{\"realIp\":\"27.22.127.154\",\"pid\":24,\"cid\":413,\"area\":\"湖北-襄阳\",\"ip\":\"112.28.231.246\",\"port\":36701},{\"realIp\":\"183.142.99.11\",\"pid\":26,\"cid\":308,\"area\":\"浙江-湖州\",\"ip\":\"112.28.231.246\",\"port\":39728},{\"realIp\":\"60.175.22.58\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"112.28.231.246\",\"port\":20102},{\"realIp\":\"112.195.157.145\",\"pid\":18,\"cid\":189,\"area\":\"四川-绵阳\",\"ip\":\"112.28.231.246\",\"port\":21061},{\"realIp\":\"114.98.149.131\",\"pid\":28,\"cid\":355,\"area\":\"安徽-合肥\",\"ip\":\"112.28.231.246\",\"port\":21621},{\"realIp\":\"114.104.182.190\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"112.28.231.246\",\"port\":24367},{\"realIp\":\"121.57.164.102\",\"pid\":7,\"cid\":59,\"area\":\"内蒙古-乌兰察布\",\"ip\":\"112.28.231.246\",\"port\":24414},{\"realIp\":\"121.206.142.4\",\"pid\":22,\"cid\":260,\"area\":\"福建-三明\",\"ip\":\"112.28.231.246\",\"port\":25624},{\"realIp\":\"111.72.25.216\",\"pid\":25,\"cid\":307,\"area\":\"江西-抚州\",\"ip\":\"112.28.231.246\",\"port\":25644},{\"realIp\":\"59.63.61.127\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"112.28.231.246\",\"port\":27795},{\"realIp\":\"202.101.250.80\",\"pid\":25,\"cid\":305,\"area\":\"江西-鹰潭\",\"ip\":\"112.28.231.246\",\"port\":27320},{\"realIp\":\"117.70.34.251\",\"pid\":28,\"cid\":345,\"area\":\"安徽-铜陵\",\"ip\":\"**************\",\"port\":33146},{\"realIp\":\"117.92.122.61\",\"pid\":27,\"cid\":335,\"area\":\"江苏-连云港\",\"ip\":\"**************\",\"port\":33736},{\"realIp\":\"113.75.151.212\",\"pid\":21,\"cid\":229,\"area\":\"广东-韶关\",\"ip\":\"**************\",\"port\":33775},{\"realIp\":\"171.110.83.242\",\"pid\":20,\"cid\":214,\"area\":\"广西-桂林\",\"ip\":\"**************\",\"port\":34218},{\"realIp\":\"49.70.185.127\",\"pid\":27,\"cid\":331,\"area\":\"江苏-镇江\",\"ip\":\"**************\",\"port\":34123},{\"realIp\":\"**************\",\"pid\":13,\"cid\":118,\"area\":\"山东-济南\",\"ip\":\"**************\",\"port\":34255},{\"realIp\":\"***************\",\"pid\":27,\"cid\":340,\"area\":\"江苏-南通\",\"ip\":\"**************\",\"port\":36724},{\"realIp\":\"**************\",\"pid\":27,\"cid\":332,\"area\":\"江苏-泰州\",\"ip\":\"**************\",\"port\":36804},{\"realIp\":\"*************\",\"pid\":14,\"cid\":135,\"area\":\"河北-邢台\",\"ip\":\"**************\",\"port\":39704},{\"realIp\":\"***************\",\"pid\":13,\"cid\":129,\"area\":\"山东-莱芜\",\"ip\":\"**************\",\"port\":34951},{\"realIp\":\"***********\",\"pid\":22,\"cid\":258,\"area\":\"福建-福州\",\"ip\":\"**************\",\"port\":39509},{\"realIp\":\"*************\",\"pid\":28,\"cid\":344,\"area\":\"安徽-淮北\",\"ip\":\"**************\",\"port\":35230},{\"realIp\":\"************\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"**************\",\"port\":20811},{\"realIp\":\"**************\",\"pid\":23,\"cid\":269,\"area\":\"湖南-益阳\",\"ip\":\"**************\",\"port\":20997},{\"realIp\":\"***************\",\"pid\":18,\"cid\":201,\"area\":\"四川-德阳\",\"ip\":\"**************\",\"port\":21407},{\"realIp\":\"*************\",\"pid\":14,\"cid\":136,\"area\":\"河北-张家口\",\"ip\":\"**************\",\"port\":22500},{\"realIp\":\"**************\",\"pid\":7,\"cid\":58,\"area\":\"内蒙古-鄂尔多斯\",\"ip\":\"**************\",\"port\":24230},{\"realIp\":\"**************\",\"pid\":14,\"cid\":134,\"area\":\"河北-石家庄\",\"ip\":\"**************\",\"port\":28954},{\"realIp\":\"*************\",\"pid\":15,\"cid\":144,\"area\":\"河南-三门峡\",\"ip\":\"**************\",\"port\":29567},{\"realIp\":\"***************\",\"pid\":13,\"cid\":120,\"area\":\"山东-淄博\",\"ip\":\"**************\",\"port\":26000},{\"realIp\":\"***************\",\"pid\":14,\"cid\":140,\"area\":\"河北-唐山\",\"ip\":\"**************\",\"port\":30505},{\"realIp\":\"*************\",\"pid\":28,\"cid\":355,\"area\":\"安徽-合肥\",\"ip\":\"**************\",\"port\":34365},{\"realIp\":\"**************\",\"pid\":14,\"cid\":134,\"area\":\"河北-石家庄\",\"ip\":\"**************\",\"port\":34948},{\"realIp\":\"*************\",\"pid\":14,\"cid\":141,\"area\":\"河北-保定\",\"ip\":\"**************\",\"port\":34793},{\"realIp\":\"*************\",\"pid\":28,\"cid\":344,\"area\":\"安徽-淮北\",\"ip\":\"**************\",\"port\":33275},{\"realIp\":\"***************\",\"pid\":18,\"cid\":206,\"area\":\"四川-资阳\",\"ip\":\"**************\",\"port\":33996},{\"realIp\":\"*************\",\"pid\":25,\"cid\":416,\"area\":\"江西-吉安\",\"ip\":\"**************\",\"port\":33773},{\"realIp\":\"***************\",\"pid\":27,\"cid\":326,\"area\":\"江苏-苏州\",\"ip\":\"**************\",\"port\":35823},{\"realIp\":\"*************\",\"pid\":15,\"cid\":159,\"area\":\"河南-商丘\",\"ip\":\"**************\",\"port\":36153},{\"realIp\":\"***************\",\"pid\":22,\"cid\":256,\"area\":\"福建-南平\",\"ip\":\"**************\",\"port\":36469},{\"realIp\":\"***************\",\"pid\":26,\"cid\":322,\"area\":\"浙江-丽水\",\"ip\":\"**************\",\"port\":37874},{\"realIp\":\"**************\",\"pid\":28,\"cid\":358,\"area\":\"安徽-黄山\",\"ip\":\"**************\",\"port\":38074},{\"realIp\":\"***************\",\"pid\":26,\"cid\":308,\"area\":\"浙江-湖州\",\"ip\":\"**************\",\"port\":37767},{\"realIp\":\"**************\",\"pid\":22,\"cid\":260,\"area\":\"福建-三明\",\"ip\":\"**************\",\"port\":39493},{\"realIp\":\"**************\",\"pid\":26,\"cid\":324,\"area\":\"浙江-温州\",\"ip\":\"**************\",\"port\":34210},{\"realIp\":\"***************\",\"pid\":26,\"cid\":321,\"area\":\"浙江-衢州\",\"ip\":\"**************\",\"port\":22737},{\"realIp\":\"**************\",\"pid\":7,\"cid\":58,\"area\":\"内蒙古-鄂尔多斯\",\"ip\":\"**************\",\"port\":20409},{\"realIp\":\"*************\",\"pid\":22,\"cid\":257,\"area\":\"福建-宁德\",\"ip\":\"**************\",\"port\":21383},{\"realIp\":\"**************\",\"pid\":28,\"cid\":357,\"area\":\"安徽-安庆\",\"ip\":\"**************\",\"port\":21911},{\"realIp\":\"**************\",\"pid\":22,\"cid\":257,\"area\":\"福建-宁德\",\"ip\":\"**************\",\"port\":23642},{\"realIp\":\"**************\",\"pid\":27,\"cid\":335,\"area\":\"江苏-连云港\",\"ip\":\"**************\",\"port\":24496},{\"realIp\":\"*************\",\"pid\":8,\"cid\":63,\"area\":\"宁夏-固原\",\"ip\":\"**************\",\"port\":26849},{\"realIp\":\"*************\",\"pid\":24,\"cid\":282,\"area\":\"湖北-黄冈\",\"ip\":\"**************\",\"port\":28409},{\"realIp\":\"***************\",\"pid\":27,\"cid\":340,\"area\":\"江苏-南通\",\"ip\":\"**************\",\"port\":29226},{\"realIp\":\"************\",\"pid\":22,\"cid\":262,\"area\":\"福建-漳州\",\"ip\":\"**************\",\"port\":29414},{\"realIp\":\"*************\",\"pid\":25,\"cid\":303,\"area\":\"江西-景德镇\",\"ip\":\"**************\",\"port\":29965},{\"realIp\":\"**************\",\"pid\":26,\"cid\":308,\"area\":\"浙江-湖州\",\"ip\":\"**************\",\"port\":30031}]}";

        // 请求供应商的代理IP接口【一次请求200个】
//        String jsonStr = HttpUtil.get(redisUtil.get(URL));
        String jsonStr = HttpUtil.createGet(redisUtil.get(URL), true).execute().body().toString();
        System.out.println(redisUtil.get(URL));
        if (JSONUtil.isJson(jsonStr)) {
            JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
            Integer code = (Integer) jsonObject.get("code");
            if (BasicConstant.NO_NUM.equals(code)) {
                JSONArray data = jsonObject.getJSONArray("data");
                List<SrcProxyipBO> srcProxyipBOList = data.toList(SrcProxyipBO.class);

                String batchJsonStr = JSONUtil.toJsonStr(srcProxyipBOList);

                // 代理IP理论过期时间180s，TTL_BATCH为175s相当于空出了5s的误差值
                DateTime expireTime = DateUtil.offsetSecond(new Date(), TTL_BATCH);
                // 代理IP过期时间格式化后作为批次号（batchNum）
                String batchNum = expireTime.toString("yyyyMMddHHmmss");

                String redisKeyBatch = RedisConstant.BATCH_PREFIX + batchNum;
                // redis中存入某批次代理IP的jsonStr
                redisUtil.setEx(redisKeyBatch, batchJsonStr, TTL_BATCH, TimeUnit.SECONDS);
            } else {
                log.error("EEEEEError: 调用供应商代理IP接口异常 @ " + DateUtil.now());
            }
        } else {
            log.error("EEEEEError: 调用供应商代理IP接口异常【" + jsonStr + "】 @ " + DateUtil.now());
        }
    }

    /**
     * 对外提供给租户使用的代理IP接口
     */
    @RequestMapping("/ips")
    public Return ips(Integer num, Integer dataType) {
        if (dataType == null) {
            dataType = 1;
        }
        // 获取租户标识（tenantFlag），包含对Authorization和IP的验证
        String tenantFlag = super.getTenantToken();

        if (tenantFlag == null) {
            return Return.error("参数有误");
        }
        // 单次可调用的IP数范围[1,1000]
        if (num < 1 || num > 1000) {
            return Return.error("参数有误");
        }

        // 租户请求接口时间
        Date reqTime = new Date();

        String redisKeyBatch = RedisConstant.BATCH_PREFIX + "*";
        Set<String> keys = redisUtil.keys(redisKeyBatch);
        if (keys.size() > 0) {
            // 逆序（key中拼接了代理IP过期时间）的目的：优先给租户提供有效时间相对更长的IP
            List<String> keyList = keys.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

            // 组装后，存入redis，等待定时任务消费到DB中
            List<ProxyipBO> proxyipBOList = new ArrayList<>();

            // 使用Set的目的：确保单次返回给租户的IP:PORT不重复
            Set<String> proxyipSet = new HashSet<>();

            MARK_OUT:
            for (String key : keyList) {
                // key中后14位是过期时间（yyyyMMddHHmmss）
                String expireTimeStr = TimeUtil.getDateTimeStrFromNumStr(StrUtil.subSuf(key, -14));

                String batchJsonStr = redisUtil.get(key);
                JSONArray objects = JSONUtil.parseArray(batchJsonStr);
                List<SrcProxyipBO> srcProxyipBOList = objects.toList(SrcProxyipBO.class);

                // 打乱的目的：某一批次IP如果无需全量返回时，可以尽量保证其随机性
                Collections.shuffle(srcProxyipBOList);

                // 遍历缓存中存储的某批次IP信息
                for (SrcProxyipBO srcProxyipBO : srcProxyipBOList) {
                    String ip = srcProxyipBO.getIp();
                    Integer port = srcProxyipBO.getPort();
                    String ipPort = ip + ":" + port;
                    if (proxyipSet.contains(ipPort)) {
                        continue;
                    }
                    proxyipSet.add(ipPort);

                    ProxyipBO proxyipBO = new ProxyipBO();
                    proxyipBO.setIp(ip);
                    proxyipBO.setPort(port);
                    proxyipBO.setExpTimeStr(expireTimeStr);
                    proxyipBO.setTenantFlag(tenantFlag);
                    proxyipBOList.add(proxyipBO);

                    // 达到请求的IP数（num）时跳出外层循环
                    if (proxyipSet.size() == num) {
                        break MARK_OUT;
                    }
                }
            }

            DateTime reqDateTime = DateUtil.date(reqTime);
            String reqDateTimeStr = reqDateTime.toString("yyyyMMddHHmmss");
            String redisKeyReqLog = RedisConstant.REQ_LOG_PREFIX + reqDateTimeStr;
            // 写入redis，等待定时任务消费到DB中（租户的调用日志）
            redisUtil.set(redisKeyReqLog, JSONUtil.toJsonStr(proxyipBOList));

            // 包装成租户需要的VO形式返回
            if (dataType == 2) {
                ProxyIpTimeVO proxyIpTimeVO = new ProxyIpTimeVO();
                List<IpTimeVO> ipTimeVOList = new ArrayList<>();
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                for (ProxyipBO proxyipBO : proxyipBOList) {
                    IpTimeVO ipTimeVO = new IpTimeVO();
                    ipTimeVO.setIp(proxyipBO.getIp() + ":" + proxyipBO.getPort());
                    LocalDateTime ttlTime = LocalDateTime.parse(proxyipBO.getExpTimeStr(), df);
                    LocalDateTime createTime = ttlTime.minusSeconds(TTL_BATCH + 5);
                    ipTimeVO.setGenTime(df.format(createTime));
                    ipTimeVO.setExpTime(df.format(createTime.plusSeconds(300)));
                    ipTimeVOList.add(ipTimeVO);
                }
                proxyIpTimeVO.setIps(ipTimeVOList);

                return Return.ok("ok", proxyIpTimeVO);
            } else {
                ProxyIpVO proxyipVO = new ProxyIpVO();
                proxyipVO.setIps(new ArrayList<>(proxyipSet));

                return Return.ok("ok", proxyipVO);
            }
        }
        return Return.error("err", "");
    }

    @GetMapping("/reqLog")
    public void reqLog() {
        String redisKeyReqLog = RedisConstant.REQ_LOG_PREFIX + "*";
        Set<String> keys = redisUtil.keys(redisKeyReqLog);

        if (keys.size() > 0) {
            List<String> keyList = keys.stream().sorted().collect(Collectors.toList());
            for (String key : keyList) {
                // key中后14位是租户请求接口的时间
                String reqTimeStr = TimeUtil.getDateTimeStrFromNumStr(StrUtil.subSuf(key, -14));
                DateTime reqTime = DateUtil.parseDateTime(reqTimeStr);
                // 按月分表
                String ym = DateUtil.format(reqTime, "yyyyMM");

                String reqLogJsonStr = redisUtil.get(key);
                JSONArray objects = JSONUtil.parseArray(reqLogJsonStr);
                redisUtil.delete(key);
                List<ProxyipBO> proxyIpBOList = objects.toList(ProxyipBO.class);
                for (ProxyipBO proxyipBO : proxyIpBOList) {
                    String ip = proxyipBO.getIp();
                    Integer port = proxyipBO.getPort();
                    String ipPort = ip + ":" + port;
                    Date expireTime = DateUtil.parseDateTime(proxyipBO.getExpTimeStr());

                    String tenantFlag = proxyipBO.getTenantFlag();

                    ReqLogPO reqLogPO = new ReqLogPO();
                    reqLogPO.setIpPort(ipPort);
                    reqLogPO.setReqTime(reqTime);
                    reqLogPO.setWipeTime(expireTime);
                    reqLogPO.setTenantFlag(tenantFlag);
                    reqLogPO.setDay(DateUtil.dayOfMonth(reqTime));

                    // 将租户调用日志落表
                    reqLogService.insertReqLog(ym, reqLogPO);
                }
                // 删除key，避免重复消费
            }
        }

    }

}
