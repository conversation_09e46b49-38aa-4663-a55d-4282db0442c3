package com.boryou.servs.proxyip.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.servs.common.config.RequestDataHelper;
import com.boryou.servs.proxyip.mapper.ReqLogMapper;
import com.boryou.servs.proxyip.pojo.po.ReqLogPO;
import com.boryou.servs.proxyip.service.ReqLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ReqLogServiceImpl extends ServiceImpl<ReqLogMapper, ReqLogPO> implements ReqLogService {

    @Autowired
    private ReqLogMapper reqLogMapper;

    @Override
    public Long insertReqLog(String ym, ReqLogPO reqLogPO) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("ym", ym);
        RequestDataHelper.setRequestData(reqMap);

        QueryWrapper<ReqLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ip_port", reqLogPO.getIpPort()).eq("tenant_flag", reqLogPO.getTenantFlag()).eq("day", reqLogPO.getDay());
        ReqLogPO repeatLog = reqLogMapper.selectOne(queryWrapper);
        if (repeatLog != null) {
            DateTime apiExpTime = DateUtil.offsetMinute(reqLogPO.getWipeTime(), 2);

            if (StrUtil.isNotBlank(repeatLog.getReqTimeStr())) {
                reqLogPO.setReqTimeStr(repeatLog.getReqTimeStr() + "," + DateUtil.format(reqLogPO.getReqTime(), "yyyyMMddHHmmss"));

                String expTimeStr = repeatLog.getExpTimeStr();
                reqLogPO.setExpTimeStr(expTimeStr + "," + DateUtil.format(apiExpTime, "yyyyMMddHHmmss"));
            } else {
                reqLogPO.setReqTimeStr(DateUtil.format(repeatLog.getReqTime(), "yyyyMMddHHmmss") + "," + DateUtil.format(reqLogPO.getReqTime(), "yyyyMMddHHmmss"));

                reqLogPO.setExpTimeStr(DateUtil.format(DateUtil.offsetMinute(repeatLog.getWipeTime(), 2), "yyyyMMddHHmmss") + "," + DateUtil.format(apiExpTime, "yyyyMMddHHmmss"));
            }
            reqLogPO.setId(repeatLog.getId());
            reqLogMapper.updateById(reqLogPO);
        } else {
            reqLogPO.setExpTimeStr(DateUtil.format(DateUtil.offsetMinute(reqLogPO.getWipeTime(),2),"yyyyMMddHHmmss"));
            reqLogMapper.insert(reqLogPO);
        }
        RequestDataHelper.removeRequestData();

        return reqLogPO.getId();
    }
}
