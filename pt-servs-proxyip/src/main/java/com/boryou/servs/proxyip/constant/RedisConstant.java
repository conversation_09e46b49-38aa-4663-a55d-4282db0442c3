package com.boryou.servs.proxyip.constant;

/**
 * <AUTHOR>
 */
public class RedisConstant {

    /**
     * 多租户 占位匹配
     */
    private static final String TENANT_FLAG = "{0}:";

    private static final String APP_PREFIX = "proxyip:";

    public static final String APP_PREFIX_TENANT = APP_PREFIX + TENANT_FLAG;

    public static final String BATCH_PREFIX = APP_PREFIX + "batch:";

    public static final String REQ_LOG_PREFIX = APP_PREFIX + "req_log:";

    public static final String URL = APP_PREFIX + "url";

    public static final String AU_IP_WHITE = APP_PREFIX + "au_ipwhite";

    /**
     * 3分钟（180秒）-5秒误差空间
     */
    public static final Integer TTL_BATCH = 175;

}
