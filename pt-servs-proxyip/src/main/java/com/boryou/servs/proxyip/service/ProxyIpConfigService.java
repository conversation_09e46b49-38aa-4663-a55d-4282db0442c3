package com.boryou.servs.proxyip.service;

import com.boryou.servs.proxyip.pojo.sb.ProxyIpConfigSB;
import com.boryou.servs.proxyip.pojo.vo.ProxyIpConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProxyIpConfigService {
    List<ProxyIpConfigVO> selectProxyIpConfigList(ProxyIpConfigSB proxyIpConfigSB);

    ProxyIpConfigVO selectProxyIpConfig(ProxyIpConfigSB proxyIpConfigSB);

    void initConfig();
}
