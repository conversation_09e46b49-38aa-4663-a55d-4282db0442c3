package com.boryou.servs.sms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boryou.servs.sms.mapper.TemplateMapper;
import com.boryou.servs.sms.pojo.po.TemplatePO;
import com.boryou.servs.sms.pojo.sb.TemplateSB;
import com.boryou.servs.sms.service.TemplateService;
import com.diboot.core.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;


/**
 * @author: Young
 */
@Service
public class TemplateServiceImpl extends BaseServiceImpl<TemplateMapper, TemplatePO> implements TemplateService {
    @Resource
    private TemplateMapper templateMapper;

    @Override
    public TemplatePO selectSsTemplate(TemplateSB templateSB) {
        QueryWrapper<TemplatePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", templateSB.getId());
        queryWrapper.eq("tpl_status", templateSB.getTplStatus());
        return templateMapper.selectOne(queryWrapper);
    }
}
