package com.boryou.servs.sms.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.sms.pojo.bo.SendBO;
import com.boryou.servs.sms.service.SendService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 短信服务控制层
 *
 * @author: Young
 */
@RestController
@RequestMapping("/sms")
public class SendController extends AheadController {
    @Resource
    private SendService sendService;

    /**
     * 发送单条短信
     */
    @PostMapping("/send")
    public Return send(@RequestBody SendBO sendBO) {
        sendBO.setTplId(super.getTplId(sendBO.getType()));
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTenantFlag(super.getTenantFlag());
        sendBO.setBizFlag(super.getBizFlag());
        return sendService.smsSend(sendBO);
    }


    /**
     * 批量对多个手机号发送短信
     */
    @PostMapping("/multiSend")
    public Return multiSend(@RequestBody SendBO sendBO) {
        sendBO.setTplId(super.getTplId(sendBO.getType()));
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTenantFlag(super.getTenantFlag());
        sendBO.setBizFlag(super.getBizFlag());
        return sendService.smsMultiSend(sendBO);
    }


    public static void main(String[] args) {
        for (int i = 0; i < 57; i++) {

        }
    }

    @GetMapping("/testSend")
    public String testSend() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("type","lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0031");
        jsonObject.set("phone","15229455987");
        jsonObject.set("p1","株洲市攸县人民法院法院");
        jsonObject.set("p2","低");
        jsonObject.set("p3","抖音");
        jsonObject.set("p4","发帖人称湖南省株洲市攸县人民法院谭湘甫、肖文雅、涉嫌在案件审理过程中枉法裁判。（0点赞量，0评论量，0转发量）");
        jsonObject.set("p5","https://www.iesdouyin.com/share/video/7368695423466229029/?schema_type=37");
        String body = HttpUtil.createPost("localhost:36509/sms/send")
                .body(jsonObject.toString())
                .header("Authorization", "5epIEIbq8XoIQJddSAyOgRxY4RKqlOkS9hi")
                .execute()
                .body();
        return body;
    }


}
