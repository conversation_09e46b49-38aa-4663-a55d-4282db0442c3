package com.boryou.servs.sms.controller;

import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.boryou.servs.common.util.RedisUtil;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @author: Young
 */
public class AheadController {
    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    private RedisUtil redisUtil;

    private static final String TENANT_FLAG = "tenantFlag";
    private static final String PROJ_FLAG = "projFlag";
    private static final String BIZ_FLAG = "bizFlag";
    private static final String MAX_SEND_TIMES = "maxSendTimes";

    /**
     * 验证Authorization
     */
    private String verifyAuth() {
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isEmptyIfStr(authorization) || StrUtil.isEmptyIfStr(getTemplateData(authorization))) {
            throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "无权限");
        }
        //验证此token发送短信次数是否超出限制
//        String sendTimes = redisUtil.get(RedisConstant.SEND_TIMES_PREFIX + authorization);
//        if (!StrUtil.isEmptyIfStr(sendTimes)) {
//            Long currentSendTimes = Long.valueOf(sendTimes);
//            if (currentSendTimes >= getMaxSendTimes()){
//                throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "已超出最大发送量");
//            }
//        }
        return authorization;
    }


    /**
     * 获取模板ID（tplId）
     */
    protected Long getTplId(String type) {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        Object getTplId = templateData.get(type);
        if (getTplId == null) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        } else {
            return Long.valueOf(getTplId.toString());
        }
    }

    /**
     * 获取租户标识（tenantFlag）
     */
    protected String getTenantFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get(TENANT_FLAG).toString();
    }


    /**
     * 获取项目标识（projFlag）
     */
    protected String getProjFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get(PROJ_FLAG).toString();
    }

    /**
     * 获取业务标识（bizFlag）
     */
    protected String getBizFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get(BIZ_FLAG).toString();
    }

    /**
     * 获取权限最大发送量（sendTimes）
     */
    protected Long getMaxSendTimes() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        Object maxSendTimes = templateData.get(MAX_SEND_TIMES);
        return Long.valueOf(maxSendTimes.toString());
    }


    /**
     * 模拟token与短信数据对应关系
     */
    private static Map<String, Object> getTemplateData(String token) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();

        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgLOGIN", 12L);   //舆情系统登录验证码模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgBACKPWD", 11L);   //舆情找回密码验证模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgCONTACT", 10L);   //舆情用户联系人手机验证模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgEXPIRE", 9L);   //舆情用户账号到期通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgREPORT", 8L);   //舆情报告推送模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgWARN", 7L);   //舆情预警通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgPUSH", 6L);   //舆情信息报送通知模板id映射
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgOPERATE", 5L);   //舆情用户运维通知模板id映射
        tokenMap1.put(PROJ_FLAG, "博约舆情");      //项目标记
        tokenMap1.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap1.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap1.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM", tokenMap1);



        //客户端用户
        Map<String, Object> tokenMap2 = new HashMap<>();
        tokenMap2.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzCbXnNOPASS", 13L);   //传播效能活动创建审核未通过提醒
        tokenMap2.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzCbXnPASS", 14L);   //传播效能活动创建审核已通过提醒
        tokenMap2.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzCbXnCHECK", 15L);   //传播效能管理员活动审核提醒
        tokenMap2.put(PROJ_FLAG, "传播效能");      //项目标记
        tokenMap2.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap2.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap2.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("CBXNdGczZGFhYzMyZm1zZG0xMzEyZXdBYKJ", tokenMap2);


        //客户端用户
        Map<String, Object> tokenMap3 = new HashMap<>();
        tokenMap3.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMgYWYJ", 16L);   //博约运维预警
        tokenMap3.put(PROJ_FLAG, "博约运维");      //项目标记
        tokenMap3.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap3.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap3.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("BYYWdHJldGczZGFhYzMyZm1zZG0xMzEyZXd", tokenMap3);

        //客户端用户
        Map<String, Object> tokenMap4 = new HashMap<>();
        tokenMap4.put("GZAipGFhY3hjZ8ac9aqwa29mbXNkbTEyMzMxWPRWTS", 17L);   //网评任务推送
        tokenMap4.put(PROJ_FLAG, "网评系统");      //项目标记
        tokenMap4.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap4.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap4.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("WPXTdDAQWRadcGFhYzMyZm1zZG0xMzEyACF", tokenMap4);

        //客户端用户
        Map<String, Object> tokenMap5 = new HashMap<>();
        tokenMap5.put("FAXCaEQWsdASDhjZ438aqw9XNkbTEyMzMxWPSDJYZM", 18L);   //验证码
        tokenMap5.put(PROJ_FLAG, "属地监管系统");      //项目标记
        tokenMap5.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap5.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap5.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("SVWadsDASACa43hYzMyZm1zZG0DSAAac91A", tokenMap5);

        //客户端用户
        Map<String, Object> tokenMap6 = new HashMap<>();
        tokenMap6.put("jJvK7S6AnrNhLnrbFYgDM2R9kL4Ce1MXPDABmsC0BL", 19L);   //【博约科技】您上报的信息需修改材料，如已修改，请忽略！
        tokenMap6.put("vmH54x8lAuQbhBYH6LQX1AsxhSBu4o0KivPyYmznQT", 20L);   //【博约科技】您有一条分发信息待处理，如已处理，请忽略！
        tokenMap6.put("qIYQewl0MhOEiQtZ2GPObWAuHlu7kmPjozZODtcpEf", 21L);   //【博约科技】您有一条上报信息待审核，如已审核，请忽略！
        tokenMap6.put(PROJ_FLAG, "信息安全传输");      //项目标记
        tokenMap6.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap6.put(BIZ_FLAG, "普通业务");   //业务标记
        tokenMap6.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("RAudCrWQe8LGQEORznt10Va5bdNlotZ0pgB", tokenMap6);

        //客户端用户 舆情报送短信模板
        Map<String, Object> tokenMap7 = new HashMap<>();
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABmsC0BL", 22L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0031", 31L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0032", 32L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0033", 33L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0034", 34L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0035", 35L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0036", 36L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0037", 37L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0038", 38L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0039", 39L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0040", 40L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0041", 41L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0042", 42L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0043", 43L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0044", 44L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0045", 45L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0046", 46L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0047", 47L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0048", 48L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0049", 49L);
        tokenMap7.put("lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0050", 50L);

        tokenMap7.put(PROJ_FLAG, "博约舆情");      //项目标记
        tokenMap7.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap7.put(BIZ_FLAG, "信息报送");   //业务标记
        tokenMap7.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("5epIEIbq8XoIQJddSAyOgRxY4RKqlOkS9hi", tokenMap7);



        //客户端用户
        Map<String, Object> tokenMap8 = new HashMap<>();
        tokenMap8.put("sGqMwDEUxgez6agOk2jyGXCpNfVk7JmThMyz5RWWjl", 23L);
        tokenMap8.put(PROJ_FLAG, "博约舆情");      //项目标记
        tokenMap8.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap8.put(BIZ_FLAG, "信息报送");   //业务标记
        tokenMap8.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("RvYy6WIiPRzmCm27YawLGpQVJI0ng6l5DLi", tokenMap8);

        //客户端用户
        Map<String, Object> tokenMap9 = new HashMap<>();
        //【博约科技】博约违法和不良信息举报管理中心，您的验证码为[?]，该验证码3分钟内有效，请勿泄漏于他人。
        tokenMap9.put("jfvgc2aRt3jPPGLlVSYMpM6ITnIuZjaLzLAhQnKoL8", 24L);
        //【博约科技】博约自媒体登记备案管理中心，您的验证码为[?]，该验证码3分钟内有效，请勿泄漏于他人。
        tokenMap9.put("ykTSJ9oWJRUP8NYvgJimSZjetB6t69eEA3809XhSYI", 25L);
        //【博约科技】博约违法和不良信息举报中心，您的验证码为[?]，该验证码3分钟内有效，请勿泄漏于他人。
        tokenMap9.put("N0VU8pc305RMRsR4ALyC71wl2HAwiCr4j3e7YmWUJD", 26L);
        //【博约科技】博约自媒体登记备案中心，您的验证码为[?]，该验证码3分钟内有效，请勿泄漏于他人。
        tokenMap9.put("NsHITnrLDiFiAQoAv2uZ2DhnYHsIknq3gVsn22WX5O", 27L);
        //【博约科技】博约违法和不良信息举报管理中心，您的绑定验证码为[?]，有效期3分钟，请尽快完成绑定！
        tokenMap9.put("FEFpM3vvKia6BDpWJ0Q2QHJYgXgHqGu3R4goaZ1txv", 28L);
        //【博约科技】博约违法和不良信息举报管理中心，您有[?]条举报信息待处理，请尽快处理！
        tokenMap9.put("Yj0COC0BJyK9GpyCEzvBHNQs4VdpuuPPMHsZYhyvPQ", 29L);
        tokenMap9.put("Yj0COC0BJyK9GskjdnjdiNQs4VdpuuPPMHsZYhyvPQ", 30L);
        tokenMap9.put(PROJ_FLAG, "博约举报和备案中心");      //项目标记
        tokenMap9.put(TENANT_FLAG, "内部用户");    //租户标记
        tokenMap9.put(BIZ_FLAG, "短息验证码");   //业务标记
        tokenMap9.put(MAX_SEND_TIMES, 3);   //最大发送次数
        map.put("JkRTgUbybthn3o6H4mBaOGxwGMXpGf8k7My", tokenMap9);

        return map.get(token);
    }

}
