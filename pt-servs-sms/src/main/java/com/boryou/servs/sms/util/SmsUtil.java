package com.boryou.servs.sms.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpUtil;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.sms.config.SmsConfig;
import java.util.HashMap;
import java.util.Map;


/**
 * 短信工具
 * @author: Young
 */

public class SmsUtil {

    /**
     * 发送短信
     */
    public static Return send(String phone, String tplSign, String tplContent) {
        //封装参数
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("username", SmsConfig.USERNAME);
        paramsMap.put("password", SmsConfig.PASSWORD);
        paramsMap.put("phone", phone);
        paramsMap.put("message", tplContent);
        paramsMap.put("epid", SmsConfig.ENTERPRISE_ID);
        paramsMap.put("mid", tplSign);
        //发送请求
        String result = HttpUtil.post(SmsConfig.SERVER_URL, HttpUtil.toParams(paramsMap, CharsetUtil.charset("gb2312")));
        if ("00".equals(result)) {
            return Return.ok("短信发送成功");
        } else {
            return Return.ok("短信发送失败，错误码：" + result);
        }
    }
}
