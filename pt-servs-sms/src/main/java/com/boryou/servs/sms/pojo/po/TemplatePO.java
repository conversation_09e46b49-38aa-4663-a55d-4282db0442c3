package com.boryou.servs.sms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.servs.common.annotation.valid.PickNum;
import com.boryou.servs.common.validator.ValidGroup;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 短信模板表
 * @author: Young
 */
@Data
@TableName("ss_template")
public class TemplatePO {
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板名称
     */
    private String tplName;

    /**
     * 模板内容
     */
    private String tplContent;

    /**
     * 模板标识
     */
    private String tplSign;

    /**
     * 项目标记
     */
    private String projFlag;

    /**
     * 模板状态
     */
    @PickNum(groups = ValidGroup.ChangeStatus.class, value = {0, 1})
    private Integer tplStatus;

    /**
     * 备注
     */
    private String remark;
}
