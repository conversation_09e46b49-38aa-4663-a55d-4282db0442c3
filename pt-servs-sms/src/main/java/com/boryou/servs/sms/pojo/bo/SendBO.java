package com.boryou.servs.sms.pojo.bo;

import lombok.Data;
import java.util.List;

/**
 * @author: Young
 */
@Data
public class SendBO {

    private static final long serialVersionUID = 1L;

    /**
     * 认证token
     */
    private String token;

    /**
     * 模板类型
     */
    private String type;

    /**
     * 单个手机号
     */
    private String phone;

    /**
     * 多个手机号
     */
    private List<String> phones;

    /**
     * 参数1
     */
    private String p1;

    /**
     * 参数2
     */
    private String p2;


    /**
     * 参数3
     */
    private String p3;


    /**
     * 参数4
     */
    private String p4;


    /**
     * 参数5
     */
    private String p5;


    /**
     * 参数6
     */
    private String p6;

    /**
     * 项目标记
     */
    private String projFlag;

    /**
     * 租户标记
     */
    private String tenantFlag;

    /**
     * 业务标识
     */
    private String bizFlag;

    /**
     * 模板id
     */
    private Long tplId;

}
