package com.boryou.servs.sms.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.sms.pojo.bo.SendBO;
import com.boryou.servs.sms.pojo.po.LogPO;
import com.boryou.servs.sms.pojo.po.TemplatePO;
import com.boryou.servs.sms.pojo.sb.TemplateSB;
import com.boryou.servs.sms.service.LogService;
import com.boryou.servs.sms.service.SendService;
import com.boryou.servs.sms.service.TemplateService;
import com.boryou.servs.sms.util.SmsUtil;
import com.diboot.core.util.JSON;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Young
 */
@Service
public class SendServiceImpl implements SendService {
    @Resource
    private TemplateService templateService;
    @Resource
    private LogService logService;

    @Override
    public Return smsSend(SendBO sendBO) {
        LogPO logPO = new LogPO();  //日志记录
        logPO.setReqTime(DateTime.now());  //请求时间
        //判定手机号
        String phone = sendBO.getPhone();
        if (!PhoneUtil.isMobile(phone)) {
            //手机号认证失败
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "手机号错误");
        }
        TemplateSB templateSB = new TemplateSB(sendBO.getTplId(), 1);  //查询当前模板id的可用id
        TemplatePO templatePO = templateService.selectSsTemplate(templateSB);
        //判定模板数据是否存在
        if (StrUtil.isEmptyIfStr(templatePO)) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        }
        //生成短信
        String tplContent = String.format(templatePO.getTplContent().replaceAll("\\[\\?\\]", "%s"), sendBO.getP1(), sendBO.getP2(), sendBO.getP3(), sendBO.getP4(), sendBO.getP5(), sendBO.getP6());
        //发送短信
        Return send = SmsUtil.send(phone, templatePO.getTplSign(), tplContent);

        //记录日志
        logPO.setRespCode(String.valueOf(send.get("code")));  //状态码
        logPO.setRespMsg(String.valueOf(send.get("msg")));  //返回信息
        logPO.setRespTime(DateTime.now()); //响应时间
        logPO.setLogContent(getLogContent(tplContent, phone)); //发送内容日志
        logPO.setProjFlag(sendBO.getProjFlag());
        logPO.setTenantFlag(sendBO.getTenantFlag());
        logPO.setBizFlag(sendBO.getBizFlag());
        logService.insertSsLog(logPO);
        return send;
    }

    @Override
    public Return smsMultiSend(SendBO sendBO) {
        LogPO logPO = new LogPO();  //日志记录
        logPO.setReqTime(DateTime.now());  //请求时间
        //判定手机号
        List<String> phones = sendBO.getPhones();
        if (StrUtil.isEmptyIfStr(phones)) {
            //手机号认证失败
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "手机号错误");
        }
        StringBuilder errorPhones = new StringBuilder();  //错误的手机号
        for (String phone : phones) {
            if (!PhoneUtil.isMobile(phone)) {
                errorPhones.append(phone).append(" ");
            }
        }
        //获取模板ID
        TemplateSB templateSB = new TemplateSB(sendBO.getTplId(), 1);  //查询当前模板id的可用id
        TemplatePO templatePO = templateService.selectSsTemplate(templateSB);
        //判定模板数据是否存在
        if (StrUtil.isEmptyIfStr(templatePO)) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        }
        //生成短信
        String tplContent = String.format(templatePO.getTplContent().replaceAll("\\[\\?\\]", "%s"), sendBO.getP1(), sendBO.getP2(), sendBO.getP3(), sendBO.getP4(), sendBO.getP5(), sendBO.getP6());
        //批量发送短信
        String phonesStr = String.join(",", phones);
        Return send = SmsUtil.send(phonesStr, templatePO.getTplSign(), tplContent);
        send.put("err_phones", errorPhones);  //错误的手机号

        //记录日志
        logPO.setRespCode(String.valueOf(send.get("code")));  //状态码
        logPO.setRespMsg(String.valueOf(send.get("msg")));  //返回信息
        logPO.setRespTime(DateTime.now()); //响应时间
        logPO.setLogContent(getLogContent(tplContent, phonesStr)); //发送内容日志
        logPO.setProjFlag(sendBO.getProjFlag());
        logPO.setTenantFlag(sendBO.getTenantFlag());
        logPO.setBizFlag(sendBO.getBizFlag());
        logService.insertSsLog(logPO);
        return send;
    }


    /**
     * 短信日志内容封装
     *
     * @author: Young
     * @Date: 2021/12/7 10:49
     */
    private static String getLogContent(String tplContent, String phone) {
        Map<String, Object> map = new HashMap<>();
        map.put("短信内容", tplContent);
        map.put("目标手机", String.join(",", phone));
        return JSON.toJSONString(map);
    }
}
