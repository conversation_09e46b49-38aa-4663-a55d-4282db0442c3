package com.boryou.servs.sms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * 短信配置
 * @author: Young
 */
@Data
@Component
@PropertySource({"classpath:hlsms-config.properties"})
@ConfigurationProperties(prefix = "hl")
public class SmsConfig {

    /**
     * 用户名
     */
    public static String USERNAME;

    /**
     * 密码
     */
    public static String PASSWORD;

    /**
     * 企业ID
     */
    public static String ENTERPRISE_ID;

    /**
     * 服务链接
     */
    public static String SERVER_URL;

    @Value("${hl.username}")
    public void setUSERNAME(String USERNAME) {
        SmsConfig.USERNAME = USERNAME;
    }

    @Value("${hl.password}")
    public void setPASSWORD(String PASSWORD) {
        SmsConfig.PASSWORD = PASSWORD;
    }

    @Value("${hl.enterpriseId}")
    public void setEnterpriseId(String enterpriseId) {
        SmsConfig.ENTERPRISE_ID = enterpriseId;
    }

    @Value("${hl.serverUrl}")
    public void setServerUrl(String serverUrl) {
        SmsConfig.SERVER_URL = serverUrl;
    }
}
