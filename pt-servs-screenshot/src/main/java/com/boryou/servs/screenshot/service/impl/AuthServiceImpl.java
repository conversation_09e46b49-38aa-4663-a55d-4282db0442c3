package com.boryou.servs.screenshot.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.boryou.servs.screenshot.constant.ServConstant;
import com.boryou.servs.screenshot.pojo.bo.AuthInfoBO;
import com.boryou.servs.screenshot.pojo.po.PrjPO;
import com.boryou.servs.screenshot.service.AuthService;
import com.boryou.servs.screenshot.service.PrjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private PrjService prjService;

    @Override
    public AuthInfoBO getAuthInfo(String token) {

        String prjIdStr = StrUtil.subPre(token, ServConstant.PRJ_ID_LENGTH);
        String encStr = StrUtil.subSuf(token, ServConstant.PRJ_ID_LENGTH);

        if (StrUtil.isNotBlank(prjIdStr) && StrUtil.isNotBlank(encStr)) {
            Integer prjId = Integer.valueOf(prjIdStr);
            PrjPO prj = prjService.getPrjById(prjId);
            String prjSecret = prj.getPrjSecret();

            DateTime date = DateUtil.date();
            // -1（S端比C端多1秒）
            int[] offsets = new int[]{0, -1, -2, -3, 1, -4, 2, -5, 3, -6, 4, -7, -8, -9, 5, 6, 7};

            DateTime nowDateTime = DateUtil.date();
            for (int i : offsets) {
                DateTime dateTime = DateUtil.offsetSecond(nowDateTime, i);
                String now = dateTime.toString();
                System.out.println("-------------> " + now);
                String tryEncStr = SecureUtil.md5(now + prjSecret);
                if (encStr.equals(tryEncStr)) {
                    AuthInfoBO authInfoBO = new AuthInfoBO();
                    authInfoBO.setPrjId(prjId);
                    authInfoBO.setPrjName(prj.getPrjName());
                    authInfoBO.setPrjCode(prj.getPrjCode());

                    return authInfoBO;
                }
            }
        }
        return null;
    }
}
