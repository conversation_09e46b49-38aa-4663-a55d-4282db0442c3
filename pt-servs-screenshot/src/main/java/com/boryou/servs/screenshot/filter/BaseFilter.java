package com.boryou.servs.screenshot.filter;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.screenshot.kit.AuthKit;
import com.boryou.servs.screenshot.pojo.bo.AuthInfoBO;
import com.boryou.servs.screenshot.service.AuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@WebFilter(filterName = "baseFilter", urlPatterns = "/screenshot/*")
public class BaseFilter implements Filter {

    @Autowired
    private AuthService authService;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String requestURI = request.getRequestURI();
        // 白名单（无需auth认证）
        List<String> whiteList = new ArrayList<>();
        whiteList.add("/screenshot/testRun");
        whiteList.add("/screenshot/fileUrl/");
        whiteList.add("/screenshot/fileDownload/");

        boolean needAuthGuard = true;
        for (String s : whiteList) {
            if (requestURI.contains(s)) {
                needAuthGuard = false;
                break;
            }
        }

        if (needAuthGuard) {
            String token = request.getHeader("token");
            AuthInfoBO authInfo = authService.getAuthInfo(token);
            if (authInfo == null) {
                HttpServletResponse response = (HttpServletResponse) servletResponse;

                // 返回JSON
                response.setStatus(500);
                response.setCharacterEncoding("UTF-8");
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);

                OutputStream out = response.getOutputStream();
                new ObjectMapper().writeValue(out, Return.error("非法访问1"));

            } else {
                AuthKit.setAuthInfo(authInfo);
            }
        }

        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void destroy() {
        AuthKit.removeAuthInfo();
        Filter.super.destroy();
    }
}
