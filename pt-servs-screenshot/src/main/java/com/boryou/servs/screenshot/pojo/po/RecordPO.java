package com.boryou.servs.screenshot.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 截图记录表（按年分表）
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("sc_record")
public class RecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer prjId;

    private String pageUrl;

    private String fileId;

    private String fileUrl;

    private String fileType;

    private Long fileSize;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
}
