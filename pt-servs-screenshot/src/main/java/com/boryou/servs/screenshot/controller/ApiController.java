package com.boryou.servs.screenshot.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.screenshot.pojo.bo.ScreenshotRunBO;
import com.boryou.servs.screenshot.service.ScreenshotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/screenshot")
public class ApiController extends AheadController {

    @Autowired
    private ScreenshotService screenshotService;

    @PostMapping("/runPng")
    public Return runPng(@RequestBody ScreenshotRunBO screenshotRunBO) throws FileNotFoundException {
        String pageUrl = screenshotRunBO.getPageUrl();
        String fileId = screenshotService.runPng(pageUrl);
        return Return.ok("ok", fileId);
    }

    @PostMapping("/runPdf")
    public Return runPdf(@RequestBody ScreenshotRunBO screenshotRunBO) throws FileNotFoundException {
        String pageUrl = screenshotRunBO.getPageUrl();
        String fileId = screenshotService.runPdf(pageUrl);
        return Return.ok("ok", fileId);
    }

    @GetMapping("/fileUrl/{fileId}")
    public String fileUrl(@PathVariable("fileId") String fileId) {
        return screenshotService.getObjectUrl(fileId);
    }

    @GetMapping({"/fileDownload/{fileId}/{fileName}", "/fileDownload/{fileId}"})
    public void fileDownload(@PathVariable("fileId") String fileId, @PathVariable(value = "fileName", required = false) String fileName) {
        screenshotService.downloadObject(fileId, fileName);
    }

    @GetMapping("/testRun")
    public String testRun() {
        String prjId = "100";
        String prjSecret = "$gD%NyES86IqfTV";
        String now = DateUtil.now();
        String secretStr = now + prjSecret;
        String token = prjId + SecureUtil.md5(secretStr);
//        String pageUrl = "https://www.baidu.com/";
        String pageUrl = "http://www.hfljsh.com/info/4/20/212.html";
//        String pageUrl = "https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&tn=baidu&wd=legend%20修改多个字体颜色&oq=legend%2520%25E4%25BF%25AE%25E6%2594%25B9%25E5%258D%2595%25E4%25B8%25AA%25E5%25AD%2597%25E9%25A2%259C%25E8%2589%25B2&rsv_pq=f5ce463300194da3&rsv_t=ef29cyvsICW%2BI7O6DDW2%2F4cLabVYdDsy73R%2FagPyKRzo95LhHpZPOgiQz6o&rqlang=cn&rsv_dl=tb&rsv_enter=1&rsv_btype=t&inputT=9716&rsv_sug3=76&rsv_sug1=31&rsv_sug7=100&rsv_sug2=0&rsv_sug4=10363";
//        String pageUrl = "http://toutiao.com/group/6529844739934519816/";
//        String pageUrl = "https://mp.weixin.qq.com/s?__biz=MzUzNDUyNDMxOQ==&mid=2247638465&idx=5&sn=7f1a74b1ad66df724d5d1d5f21f42a22&chksm=fa9fe531cde86c27f4fd3843a9326c49ca93c8be4cc7aac52a3c93c41d0c95ade7c5a5c9fbac";

        JSONObject json = new JSONObject();
        json.set("pageUrl", pageUrl);

        System.out.println("-------------->>> " + now);

        String url = "http://localhost:36501/screenshot/runPng";
        // 注意：切下面的url不会入本地库！
//        String url = "http://iapi.boryou.com:36501/screenshot/runPdf";
//        String url = "http://iapi.boryou.com:36501/screenshot/runPng";
        String resJsonStr = HttpUtil.createPost(url)
                .header("token", token)
                .body(json.toString())
                .execute()
                .body();

        return resJsonStr;
    }
}
