package com.boryou.servs.screenshot.kit;

import com.boryou.servs.screenshot.pojo.bo.AuthInfoBO;

/**
 * 存储/获取当前线程的认证信息 工具类
 *
 * <AUTHOR>
 */
public abstract class AuthKit {

    private static ThreadLocal<AuthInfoBO> authThreadLocal = new ThreadLocal<AuthInfoBO>();

    public static AuthInfoBO getAuthInfo() {
        return authThreadLocal.get();
    }

    public static Integer getPrjId() {
        AuthInfoBO authInfoBO = authThreadLocal.get();
        if (authInfoBO != null) {
            return authInfoBO.getPrjId();
        }
        return null;
    }

    /**
     * 为线程变量赋值
     */
    public static void setAuthInfo(AuthInfoBO authInfoBO) {
        authThreadLocal.set(authInfoBO);
    }

    /**
     * 清除线程变量
     */
    public static void removeAuthInfo() {
        authThreadLocal.remove();
    }
}
