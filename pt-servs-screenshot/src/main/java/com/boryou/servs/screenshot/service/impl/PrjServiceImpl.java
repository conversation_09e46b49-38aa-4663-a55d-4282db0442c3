package com.boryou.servs.screenshot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.servs.screenshot.mapper.PrjMapper;
import com.boryou.servs.screenshot.pojo.po.PrjPO;
import com.boryou.servs.screenshot.service.PrjService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class PrjServiceImpl extends ServiceImpl<PrjMapper, PrjPO> implements PrjService {

    @Override
    public PrjPO getPrjById(Integer prjId) {
        return baseMapper.selectById(prjId);
    }
}
