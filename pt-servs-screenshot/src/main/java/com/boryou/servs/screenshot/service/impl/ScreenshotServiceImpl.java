package com.boryou.servs.screenshot.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.boryou.servs.common.config.RequestDataHelper;
import com.boryou.servs.common.util.IdUtil;
import com.boryou.servs.file.pojo.bo.FileInfoBO;
import com.boryou.servs.file.service.MinioService;
import com.boryou.servs.file.util.FileConvertUtil;
import com.boryou.servs.screenshot.kit.AuthKit;
import com.boryou.servs.screenshot.pojo.bo.AuthInfoBO;
import com.boryou.servs.screenshot.pojo.po.RecordPO;
import com.boryou.servs.screenshot.service.RecordService;
import com.boryou.servs.screenshot.service.ScreenshotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ScreenshotServiceImpl implements ScreenshotService {

    private static final String BASE_LINK_PIC = "http://**************:3000/screenshot/png?url=";

    private static final String BASE_LINK_PDF = "http://**************:3000/screenshot/pdf?url=";

    public static final String ENDPOINT = "https://iapi.boryou.com:33003";

    public static final String BUCKET_NAME = "bucket--zz-hefei-webgov-dev";

    @Autowired
    private MinioService minioService;

    @Autowired
    private RecordService recordService;

    @Autowired
    private HttpServletResponse httpServletResponse;

    @Override
    public String runPng(String pageUrl) {
        String pageUrlEncoded = URLUtil.encodeAll(pageUrl);

        AuthInfoBO authInfo = AuthKit.getAuthInfo();
        Integer prjId = authInfo.getPrjId();

        // 开始截图
        LocalDateTime beginTime = LocalDateTime.now();
        String year = String.valueOf(beginTime.getYear());

        byte[] picBytes = HttpUtil.downloadBytes(BASE_LINK_PIC + pageUrlEncoded);

        // 完成截图
        LocalDateTime finishTime = LocalDateTime.now();
        MultipartFile multipartFile = FileConvertUtil.bytesToMultipartFile(picBytes, "tmp", "tmp.png");

        FileInfoBO fileInfoBO = null;
        try {
            fileInfoBO = minioService.putObject(BUCKET_NAME, multipartFile, year, "image/png");
        } catch (Exception e) {
            e.printStackTrace();
        }
        String fileId = fileInfoBO.getFileId();
        String fileUrl = fileInfoBO.getFileUrl();
        String fileType = fileInfoBO.getFileType();
        Long fileSize = fileInfoBO.getFileSize();

        RecordPO recordPO = new RecordPO();
        Long id = IdUtil.nextLong();
        recordPO.setId(id);
        recordPO.setPrjId(prjId);
        recordPO.setPageUrl(pageUrl);
        recordPO.setFileId(fileId);
        recordPO.setFileUrl(fileUrl);
        recordPO.setFileType(fileType);
        recordPO.setFileSize(fileSize);
        recordPO.setBeginTime(beginTime);
        recordPO.setFinishTime(finishTime);

        recordService.insertRecord(year, recordPO);
        return fileId;
    }

    @Override
    public String runPdf(String pageUrl) {
        String pageUrlEncoded = URLUtil.encodeAll(pageUrl);

        AuthInfoBO authInfo = AuthKit.getAuthInfo();
        Integer prjId = authInfo.getPrjId();

        // 开始截图
        LocalDateTime beginTime = LocalDateTime.now();
        String year = String.valueOf(beginTime.getYear());

        byte[] pdfBytes = HttpUtil.downloadBytes(BASE_LINK_PDF + pageUrlEncoded);

        // 完成截图
        LocalDateTime finishTime = LocalDateTime.now();
        MultipartFile multipartFile = FileConvertUtil.bytesToMultipartFile(pdfBytes, "tmp", "tmp.pdf");

        FileInfoBO fileInfoBO = null;
        try {
            fileInfoBO = minioService.putObject(BUCKET_NAME, multipartFile, year, "application/pdf");
        } catch (Exception e) {
            e.printStackTrace();
        }
        String fileId = fileInfoBO.getFileId();
        String fileUrl = fileInfoBO.getFileUrl();
        String fileType = fileInfoBO.getFileType();
        Long fileSize = fileInfoBO.getFileSize();

        RecordPO recordPO = new RecordPO();
        Long id = IdUtil.nextLong();
        recordPO.setId(id);
        recordPO.setPrjId(prjId);
        recordPO.setPageUrl(pageUrl);
        recordPO.setFileId(fileId);
        recordPO.setFileUrl(fileUrl);
        recordPO.setFileType(fileType);
        recordPO.setFileSize(fileSize);
        recordPO.setBeginTime(beginTime);
        recordPO.setFinishTime(finishTime);

        recordService.insertRecord(year, recordPO);
        return fileId;
    }

    @Override
    public byte[] getImageBytes(String fileId) {
        String imageUrl = getImageUrl(fileId);
        return HttpUtil.downloadBytes(imageUrl);
    }

    @Override
    public String getImageUrl(String fileId) {
        Map<String, Object> reqMap = new HashMap<>();
        String year = fileId.substring(0, 4);

        String imageUrl = "";
        if (StrUtil.isNotBlank(year)) {
            reqMap.put("year", year);
            RequestDataHelper.setRequestData(reqMap);
            RecordPO recordPO = recordService.getRecordByFileId(fileId);
            RequestDataHelper.removeRequestData();
            String fileUrl = recordPO.getFileUrl();
            //TODO
            imageUrl = ENDPOINT + "/" + BUCKET_NAME + "/" + fileUrl;
        }
        return imageUrl;
    }

    @Override
    public String getObjectUrl(String fileId) {
        Map<String, Object> reqMap = new HashMap<>();
        String year = fileId.substring(0, 4);

        String objectUrl = "";
        if (StrUtil.isNotBlank(year)) {
            reqMap.put("year", year);
            RequestDataHelper.setRequestData(reqMap);
            RecordPO recordPO = recordService.getRecordByFileId(fileId);
            RequestDataHelper.removeRequestData();
            String fileUrl = recordPO.getFileUrl();
            try {
                objectUrl = minioService.getObjectUrl(BUCKET_NAME, fileUrl);

                // TODO
                if (objectUrl.startsWith("https")) {
                    objectUrl = "https://o" + StrUtil.sub(objectUrl, 9, StrUtil.indexOf(objectUrl, '?'));
                } else {
                    objectUrl = "https://o" + StrUtil.sub(objectUrl, 8, StrUtil.indexOf(objectUrl, '?'));
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return objectUrl;
    }

    @Override
    public void downloadObject(String fileId, String fileName) {
        Map<String, Object> reqMap = new HashMap<>();
        String year = fileId.substring(0, 4);

        if (StrUtil.isNotBlank(year)) {
            reqMap.put("year", year);
            RequestDataHelper.setRequestData(reqMap);
            RecordPO recordPO = recordService.getRecordByFileId(fileId);
            RequestDataHelper.removeRequestData();

            String fileUrl = recordPO.getFileUrl();


            if (StrUtil.isBlank(fileName)) {
                fileName = SecureUtil.md5(recordPO.getFileId()) + "." + FileUtil.getSuffix(fileUrl);
            }
            try {
                minioService.downloadObject(BUCKET_NAME, fileName, fileUrl, httpServletResponse);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
