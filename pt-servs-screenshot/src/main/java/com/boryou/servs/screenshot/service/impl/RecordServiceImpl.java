package com.boryou.servs.screenshot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.servs.common.config.RequestDataHelper;
import com.boryou.servs.screenshot.mapper.RecordMapper;
import com.boryou.servs.screenshot.pojo.po.RecordPO;
import com.boryou.servs.screenshot.service.RecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class RecordServiceImpl extends ServiceImpl<RecordMapper, RecordPO> implements RecordService {

    @Autowired
    private RecordMapper recordMapper;

    @Override
    public int insertRecord(String year, RecordPO recordPO) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("year", year);
        RequestDataHelper.setRequestData(reqMap);

        int ret = recordMapper.insert(recordPO);
        RequestDataHelper.removeRequestData();
        return ret;
    }

    @Override
    public RecordPO getRecordByFileId(String fileId) {
        QueryWrapper<RecordPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        return recordMapper.selectOne(queryWrapper);
    }
}
