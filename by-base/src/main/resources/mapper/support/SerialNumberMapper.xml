<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.base.module.support.serialnumber.dao.SerialNumberDao">

    <update id="updateLastNumberAndTime">
        update t_serial_number
        set
        last_number = #{lastNumber},
        last_time = #{lastTime}
        where
        serial_number_id = #{serialNumberId}

    </update>

    <!-- 查询最后生成记录 -->
    <select id="selectForUpdate" resultType="com.boryou.web.base.module.support.serialnumber.domain.SerialNumberEntity">
       select * from t_serial_number where serial_number_id = #{serialNumberId} for update
    </select>


</mapper>
