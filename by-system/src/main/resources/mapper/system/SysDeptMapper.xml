<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.system.mapper.SysDeptMapper">
    <resultMap type="com.boryou.common.core.domain.entity.SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDeptVo">
        select d.*
        from sys_dept d
    </sql>

    <select id="selectDeptList" parameterType="com.boryou.common.core.domain.entity.SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptListAuth" parameterType="com.boryou.common.core.domain.entity.SysDept"
            resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="deptId != null and deptId != 0">
            AND (ancestors like concat('%,', #{deptId}, ',%')
            OR dept_id = #{deptId} OR parent_id = #{deptId})
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <!-- 数据范围过滤 -->
        order by d.parent_id, d.order_num
    </select>

    <select id="selectPermiDeptList" parameterType="com.boryou.common.core.domain.entity.SysDept"
            resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        and (d.dept_id = #{deptId} or find_in_set(#{deptId}, d.ancestors))
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptListByRoleId" resultType="Integer">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where dept_id = #{deptId}
    </select>

    <select id="selectDeptByIds" parameterType="Long" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
        select count(1)
        from sys_user
        where dept_id = #{deptId}
        and del_flag = '0'
    </select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
        select count(1)
        from sys_dept
        where del_flag = '0'
        and parent_id = #{deptId} limit 1
    </select>

    <select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
        select *
        from sys_dept
        where find_in_set(#{deptId}, ancestors)
    </select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
        select count(*)
        from sys_dept
        where status = 0
        and del_flag = '0'
        and find_in_set(#{deptId}, ancestors)
    </select>

    <select id="checkDeptNameUnique" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where dept_name=#{deptName} and parent_id = #{parentId} limit 1
    </select>

    <select id="getDeptIdBySystemId" resultType="java.lang.Long">
        select dept_id from sys_dept where del_flag = 0 and system_id = #{systemId} limit 1
    </select>
    <select id="getSaasConfig" resultType="com.boryou.common.core.domain.vo.OpenCustomizationVO">
        select system_name, system_id, title, system_link, logo, title_logo, login_bg1, login_bg2, dept_id
        from sys_dept
        where dept_id = #{deptId}
    </select>
    <select id="getDeptSaasConfig" resultType="com.boryou.common.core.domain.vo.OpenCustomizationVO">
        select system_name, system_id, title, system_link, logo, title_logo, login_bg1, login_bg2, dept_id
        from sys_dept
        where system_id = #{linkTag}
        limit 1
    </select>
    <select id="selectManageDept" resultType="java.lang.Long">
        SELECT dept_id FROM sys_dept WHERE FIND_IN_SET(#{deptId},ancestors) or dept_id = #{deptId}
    </select>
    <select id="selectManageDeptUsers" resultType="java.lang.Long">
        SELECT s.user_id FROM sys_dept d inner join sys_user s on s.dept_id=d.dept_id WHERE d.parent_id=#{deptId} or
        d.dept_id = #{deptId} and s.del_flag='0' and s.status='0'
    </select>
    <select id="selectManageDeptString" resultType="string">
        SELECT dept_id FROM sys_dept WHERE FIND_IN_SET(#{deptId},ancestors) or dept_id = #{deptId}
    </select>
    <select id="checkSystemId" resultType="java.lang.Integer">
        select count(1) from sys_dept where system_id = #{systemId} and del_flag = 0
    </select>

    <insert id="insertDept" parameterType="com.boryou.common.core.domain.entity.SysDept" useGeneratedKeys="true"
            keyProperty="deptId">
        insert into sys_dept(
        <if test="deptId != null and deptId != 0">
            dept_id,
        </if>
        <if test="parentId != null and parentId != 0">
            parent_id,
        </if>
        <if test="deptName != null and deptName != ''">
            dept_name,
        </if>
        <if test="ancestors != null and ancestors != ''">
            ancestors,
        </if>
        <if test="orderNum != null and orderNum != ''">
            order_num,
        </if>
        <if test="leader != null and leader != ''">
            leader,
        </if>
        <if test="phone != null and phone != ''">
            phone,
        </if>
        <if test="email != null and email != ''">
            email,
        </if>
        <if test="status != null">
            status,
        </if>
        <if test="createBy != null and createBy != ''">
            create_by,
        </if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">
            #{deptId},
        </if>
        <if test="parentId != null and parentId != 0">
            #{parentId},
        </if>
        <if test="deptName != null and deptName != ''">
            #{deptName},
        </if>
        <if test="ancestors != null and ancestors != ''">
            #{ancestors},
        </if>
        <if test="orderNum != null and orderNum != ''">
            #{orderNum},
        </if>
        <if test="leader != null and leader != ''">
            #{leader},
        </if>
        <if test="phone != null and phone != ''">
            #{phone},
        </if>
        <if test="email != null and email != ''">
            #{email},
        </if>
        <if test="status != null">
            #{status},
        </if>
        <if test="createBy != null and createBy != ''">
            #{createBy},
        </if>
        sysdate()
        )
    </insert>

    <update id="updateDept" parameterType="com.boryou.common.core.domain.entity.SysDept">
        update sys_dept
        <set>
            <if test="parentId != null and parentId != 0">
                parent_id = #{parentId},
            </if>
            <if test="deptName != null and deptName != ''">
                dept_name = #{deptName},
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors = #{ancestors},
            </if>
            <if test="orderNum != null and orderNum != ''">
                order_num = #{orderNum},
            </if>
            <if test="leader != null">
                leader = #{leader},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            update_time = sysdate()
        </set>
        where dept_id = #{deptId}
    </update>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update sys_dept set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case dept_id" close="end">
            when #{item.deptId} then #{item.ancestors}
        </foreach>
        where dept_id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatus" parameterType="com.boryou.common.core.domain.entity.SysDept">
        update sys_dept
        <set>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            update_time = sysdate()
        </set>
        where dept_id in (${ancestors})
    </update>
    <update id="updateSassConfig">
        update sys_dept
        <set>
            update_time = now(),
            <if test="map.systemId != null and map.systemId != ''">
                system_id = #{map.systemId},
            </if>
            <if test="map.systemName != null and map.systemName != ''">
                system_name = #{map.systemName},
            </if>
            <if test="map.title != null and map.title != ''">
                title = #{map.title},
            </if>
            <if test="map.systemLink != null and map.systemLink != ''">
                system_link = #{map.systemLink},
            </if>
            <if test="map.logo != null and map.logo != ''">
                logo = #{map.logo},
            </if>
            <if test="map.titleLogo != null and map.titleLogo != ''">
                title_logo = #{map.titleLogo},
            </if>
            <if test="map.loginBg1 != null and map.loginBg1 != ''">
                login_bg1 = #{map.loginBg1},
            </if>
            <if test="map.loginBg2 != null and map.loginBg2 != ''">
                login_bg2 = #{map.loginBg2},
            </if>
        </set>
        where dept_id = #{map.deptId}
    </update>


    <delete id="deleteDeptById" parameterType="Long">
        update sys_dept
        set del_flag = '2'
        where dept_id = #{deptId}
    </delete>

    <select id="selectDeptLists" parameterType="SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="roleIds!=null">
            and exists(
            select 1 from sys_user u
            inner join sys_user_role ur on u.user_id=ur.user_id
            inner join sys_role r on r.role_id=ur.role_id
            where d.dept_id=u.dept_id and r.role_id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
            )
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="getDeptsByRoleKey" parameterType="SysDept" resultMap="SysDeptResult">
        select distinct d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email,
        d.status, d.del_flag, d.create_by, d.create_time
        from sys_user u
        inner join sys_user_role ur on u.user_id=ur.user_id
        inner join sys_role r on r.role_id=ur.role_id
        inner join sys_dept d on d.dept_id=u.dept_id
        where r.role_key=#{roleKey} and u.status='0' and d.status='0' and d.del_flag='0'
        <if test="deptId!=null and deptId!=''">
            and d.dept_id=#{deptId}
        </if>
    </select>


    <select id="getRootDeptsByRole" parameterType="SysDept" resultMap="SysDeptResult">
        select distinct d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email,
        d.status, d.del_flag, d.create_by, d.create_time
        FROM
        sys_dept d
        left JOIN sys_user u ON d.dept_id = u.dept_id AND u.STATUS = '0'
        left JOIN sys_user_role ur ON u.user_id = ur.user_id
        left JOIN sys_role r ON r.role_id = ur.role_id
        WHERE
        (r.role_key =#{roleKey} <if test="root!=null">or d.parent_id=#{root}</if> )
        AND d.STATUS = '0'
        AND d.del_flag = '0'
    </select>

    <select id="deptTree" resultType="com.boryou.common.core.domain.entity.SysDept">
        SELECT * FROM sys_dept WHERE FIND_IN_SET(dept_id,queryChildrenDept(#{deptId}))
        UNION
        SELECT * FROM sys_dept WHERE FIND_IN_SET(dept_id,queryFatherDept(#{deptId}))
    </select>
    <select id="selectSystemIdById" resultType="java.lang.String">
        select system_id from sys_dept where dept_id = #{deptId}
    </select>
</mapper>
