package com.boryou.manage.domain;

import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * apk版本数据对象 by_apk_data
 *
 * <AUTHOR>
 * @date 2023-04-18
 */
@Data
public class ByApkData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 版本名称
     */
    @Excel(name = "版本名称")
    private String versionName;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private String versionNum;

    /**
     * 版本描述
     */
    @Excel(name = "版本描述")
    private String versionDescribe;

    /**
     * 版本文件ID
     */
    @Excel(name = "版本文件ID")
    private String versionFileId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ctime;

    /**
     * 文件类
     */
    private ByUploadFile byUploadFile;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public String getVersionDescribe() {
        return versionDescribe;
    }

    public void setVersionDescribe(String versionDescribe) {
        this.versionDescribe = versionDescribe;
    }

    public String getVersionFileId() {
        return versionFileId;
    }

    public void setVersionFileId(String versionFileId) {
        this.versionFileId = versionFileId;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("versionName", getVersionName())
                .append("versionNum", getVersionNum())
                .append("versionDescribe", getVersionDescribe())
                .append("versionFileId", getVersionFileId())
                .append("ctime", getCtime())
                .toString();
    }
}
