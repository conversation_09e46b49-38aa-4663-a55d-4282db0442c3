package com.boryou.manage.mapper;

import com.boryou.manage.domain.ByUploadFile;

import java.util.List;

/**
 * 文件上传Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface ByUploadFileMapper {
    /**
     * 查询文件上传
     *
     * @param id 文件上传ID
     * @return 文件上传
     */
    public ByUploadFile selectByUploadFileById(Long id);


    /**
     * 查询文件上传
     *
     * @param ids 文件上传ID集合
     * @return 文件上传
     */
    public List<ByUploadFile> selectByUploadFileByIds(String ids);

    /**
     * 查询文件上传列表
     *
     * @param byUploadFile 文件上传
     * @return 文件上传集合
     */
    public List<ByUploadFile> selectByUploadFileList(ByUploadFile byUploadFile);

    /**
     * 新增文件上传
     *
     * @param byUploadFile 文件上传
     * @return 结果
     */
    public int insertByUploadFile(ByUploadFile byUploadFile);

    /**
     * 修改文件上传
     *
     * @param byUploadFile 文件上传
     * @return 结果
     */
    public int updateByUploadFile(ByUploadFile byUploadFile);

    /**
     * 删除文件上传
     *
     * @param id 文件上传ID
     * @return 结果
     */
    public int deleteByUploadFileById(Long id);

    /**
     * 批量删除文件上传
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteByUploadFileByIds(Long[] ids);
}
