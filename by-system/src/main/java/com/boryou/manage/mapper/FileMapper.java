package com.boryou.manage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.manage.domain.File;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface FileMapper extends BaseMapper<File> {
    /**
     * 查询文件管理
     *
     * @param fileId 文件管理ID
     * @return 文件管理
     */
    File selectFileById(Long fileId);

    /**
     * 查询文件管理列表
     *
     * @param file 文件管理
     * @return 文件管理集合
     */
    List<File> selectFileList(File file);

    /**
     * 新增文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    int insertFile(File file);

    /**
     * 修改文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    int updateFile(File file);

    /**
     * 删除文件管理
     *
     * @param fileId 文件管理ID
     * @return 结果
     */
    int deleteFileById(Long fileId);

    /**
     * 批量删除文件管理
     *
     * @param fileIds 需要删除的数据ID
     * @return 结果
     */
    int deleteFileByIds(Long[] fileIds);

    int insertFileList(@Param("infoList") List<File> fileList);

    File selectFileByFileName(String fileName);
}
