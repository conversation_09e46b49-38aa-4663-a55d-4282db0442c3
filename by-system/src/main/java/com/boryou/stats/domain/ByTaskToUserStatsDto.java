package com.boryou.stats.domain;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 网评员任务统计
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Data
public class ByTaskToUserStatsDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 完成任务数
     */
    private Integer finishCount;

    /**
     * 待处理任务数
     */
    private Integer waitCount;

    /**
     * 参与任务数
     */
    private Integer allCount;

    /**
     * 总绩效
     */
    private Integer allGradeCount;

    /**
     * 周绩效
     */
    private Integer weekGradeCount;

    /**
     * 月绩效
     */
    private Integer monthGradeCount;

    /**
     * 半年绩效
     */
    private Integer halfYearGradeCount;

    /**
     * 任务媒体类型及数量
     */
    private JSONArray mediaTypeArray = new JSONArray();

    /**
     * 任务时间及数量列表的JSON
     */
    private JSONObject taskTimeJSON = new JSONObject();

    /**
     * 平台类型列表
     */
    private List<String> mediaTypes = new ArrayList<>();

    /**
     * 平台各类型数量列表
     */
    private List<Integer> mediaCounts = new ArrayList<>();
}
