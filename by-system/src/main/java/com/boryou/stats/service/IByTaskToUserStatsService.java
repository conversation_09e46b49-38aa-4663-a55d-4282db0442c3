package com.boryou.stats.service;

import com.boryou.manage.domain.ByTaskToUser;
import com.boryou.stats.domain.ByTaskToUserStatsDto;

/**
 * 网评员任务统计Service接口
 *
 * <AUTHOR>
 */
public interface IByTaskToUserStatsService {

    public ByTaskToUserStatsDto count(ByTaskToUser byTaskToUser);

    public ByTaskToUserStatsDto getGradeCount(ByTaskToUser byTaskToUser);

    /***
     * <AUTHOR>
     * @description //获取任务数量趋势
     * @date 14:25 2023/5/5
     */
    public ByTaskToUserStatsDto getTaskNum(String timeType);
}
