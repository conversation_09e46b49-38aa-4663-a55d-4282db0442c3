package com.boryou.stats.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.page.PageDomain;
import com.boryou.common.core.page.TableSupport;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.sql.SqlUtil;
import com.boryou.manage.service.IByNetworkReviewTaskService;
import com.boryou.stats.domain.AchievementsDataDto;
import com.boryou.stats.domain.DataCountDto;
import com.boryou.stats.domain.OrgScheduleDto;
import com.boryou.stats.domain.StatisticalDataDto;
import com.boryou.stats.mapper.StatisticalDataMapper;
import com.boryou.stats.service.StatisticalDataService;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysDictDataMapper;
import com.boryou.system.mapper.SysUserMapper;
import com.boryou.system.service.ISysDeptService;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * @description //数据处理业务成
 * @date 10:10 2023/3/20
 * @param
 */
@Service
public class StatisticalDataServiceImpl implements StatisticalDataService {

    @Resource
    private StatisticalDataMapper statisticalDataMapper;
    @Resource
    private SysDictDataMapper dictDataMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private IByNetworkReviewTaskService byNetworkReviewTaskService;

    @Override
    public Map<String, Object> getQueryData() {
        /**1.网评任务数量
         2.各媒体账号数据--取前两名
         3.已完成任务数量
         4.进行中任务数量
         5.已作废任务数量*/
        /**现在查询只能查询同属一个二级机构的数据*/
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        List<Long> deptUserIdList = deptService.selectUserIdForDept(deptId);
        Long[] deptUserIdArr = new Long[deptUserIdList.size()];
        deptUserIdArr = deptUserIdList.toArray(deptUserIdArr);
        //获取所有网评任务状态数量 1 进行 2未完成 3已完成 4作废
        List<StatisticalDataDto> taskStatusList = statisticalDataMapper.selectByNetworkReviewTaskNum(deptUserIdArr);
        /**获取所有媒体的任务数量--已经过排序取前两条数据则为数据量最高的*/
        List<StatisticalDataDto> mediaTypeList = statisticalDataMapper.selectByNetworkReviewTaskMediaNum(deptUserIdArr);
        //构建结果集
        Map<String, Object> map = new HashMap<>();
        //进行中的任务量
        int process_task_num = 0;
        //已完成的任务量
        int accomplish_task_num = 0;
        //未完成的任务量
        int un_finished = 0;
        //已作废的任务量
        int cancel_task_num = 0;
        //所有任务量--包含了未完成
        int all_task_num = 0;
        for (StatisticalDataDto statisticalDataDto : taskStatusList) {
            //进行中
            if (statisticalDataDto.getType() == 1) {
                process_task_num = statisticalDataDto.getNum();
            }
            //已完成
            if (statisticalDataDto.getType() == 3) {
                accomplish_task_num = statisticalDataDto.getNum();
            }
            //未完成
            if (statisticalDataDto.getType() == 2) {
                un_finished = statisticalDataDto.getNum();
            }
            //已作废
            if (statisticalDataDto.getType() == 4) {
                cancel_task_num = statisticalDataDto.getNum();
            }
            all_task_num += statisticalDataDto.getNum();
        }
        map.put("process_task_num", process_task_num);
        map.put("accomplish_task_num", accomplish_task_num);
        map.put("cancel_task_num", cancel_task_num);
        map.put("all_task_num", all_task_num);
        map.put("un_finished", un_finished);
        map.put("NO_1", 0);
        map.put("NO_2", 0);
        map.put("NO_1_NAME", "暂无");
        map.put("NO_2_NAME", "暂无");
        List<Integer> nullNumList = mediaTypeList.stream()
                .filter(item -> item.getType() == null)
                .map(StatisticalDataDto::getNum)
                .collect(Collectors.toList());
        for (int a = 1; a < mediaTypeList.size() + 1; a++) {
            Integer type = mediaTypeList.get(a - 1).getType();
            if (type == null) {
                continue;
            }
            Integer num = mediaTypeList.get(a - 1).getNum();
            if (type == 0 && CollUtil.isNotEmpty(nullNumList)) {
                //type为其他的加上type为空的
                num += nullNumList.get(0);
            }
            map.put("NO_" + a + "_NAME", dictDataMapper.selectDictLabel("media_type", type.toString()));
            map.put("NO_" + a, num);
        }
        return map;
    }

    @Override
    public StatisticalDataDto getMediaData() {
        /**现在查询只能查询同属一个二级机构的数据*/
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        List<Long> deptUserIdList = deptService.selectUserIdForDept(deptId);
        Long[] deptUserIdArr = new Long[deptUserIdList.size()];
        deptUserIdArr = deptUserIdList.toArray(deptUserIdArr);
        /**获取所有媒体的数据任务量*/
        List<StatisticalDataDto> mediaTypeList = statisticalDataMapper.selectByNetworkReviewTaskMediaNum(deptUserIdArr);
        //获取总量
        int dataCount = mediaTypeList.stream().collect(Collectors.summingInt(StatisticalDataDto::getNum));
        //结果集
        List<Map<String, String>> mapList = new ArrayList<>();
        List<Integer> nullNumList = mediaTypeList.stream()
                .filter(item -> item.getType() == null)
                .map(StatisticalDataDto::getNum)
                .collect(Collectors.toList());
        for (StatisticalDataDto statisticalDataDto : mediaTypeList) {
            Map<String, String> map = new HashMap<>();
            Integer type = statisticalDataDto.getType();
            //链接可以不填
            if (type == null) {
                continue;
            }
            Integer num = statisticalDataDto.getNum();
            if (type == 0 && CollUtil.isNotEmpty(nullNumList)) {
                //type为其他的加上type为空的
                num += nullNumList.get(0);
            }
            map.put("name", dictDataMapper.selectDictLabel("media_type", type.toString()));
            map.put("value", num.toString());
            mapList.add(map);
        }
        //将数据结果放入返回对象
        StatisticalDataDto statisticalDataDto = new StatisticalDataDto();
        statisticalDataDto.setAllCount(dataCount);
        statisticalDataDto.setMapList(mapList);
        return statisticalDataDto;
    }

    @Override
    public Map<String, List<String>> getPersonData(String selectTimeOption) {
        /**根据所传值来确定时间区间  1  近一周  2 近一月  3 近半年 */
        StatisticalDataDto statisticalDataDto = new StatisticalDataDto();
        if (StrUtil.isEmpty(selectTimeOption)) {
            throw new CustomException("请选择时间区间");
        }
        if ("1".equals(selectTimeOption)) {
            statisticalDataDto.setSelectEndTime(DateUtil.now());
            statisticalDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -7)));
        }
        if ("2".equals(selectTimeOption)) {
            statisticalDataDto.setSelectEndTime(DateUtil.now());
            statisticalDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
        }
        if ("3".equals(selectTimeOption)) {
            statisticalDataDto.setSelectEndTime(DateUtil.now());
            statisticalDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -180)));
        }
        /**现在查询只能查询同属一个二级机构的数据*/
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        List<Long> deptUserIdList = deptService.selectUserIdForDept(deptId);
        Long[] deptUserIdArr = new Long[deptUserIdList.size()];
        deptUserIdArr = deptUserIdList.toArray(deptUserIdArr);
        statisticalDataDto.setDeptUserIdList(deptUserIdArr);
        List<StatisticalDataDto> personList = statisticalDataMapper.selectPersonTaskNum(statisticalDataDto);
        //结果集
        List<String> xList = new ArrayList<>();
        List<String> yList = new ArrayList<>();
        Map<String, List<String>> listMap = new HashMap<>();
        for (StatisticalDataDto sdd : personList) {
            xList.add(userMapper.selectUserById(sdd.getType().longValue()).getNickName());
            yList.add(sdd.getNum().toString());
        }
        listMap.put("xList", xList);
        listMap.put("yList", yList);
        return listMap;
    }

    @Override
    public List<AchievementsDataDto> getManageAchievementsData(AchievementsDataDto achievementsDataDto) {
        if (StringUtils.isNull(achievementsDataDto.getDeptId())) {
            //如果部门为空则默认传当前人的所在部门
            achievementsDataDto.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (StrUtil.isEmpty(achievementsDataDto.getTimeOption())) {
            throw new CustomException("请选择时间区间");
        }
        /**先获取所选部门的所有子部门*/
        List<SysDept> sysDeptList = sysDeptMapper.selectChildrenDeptById(achievementsDataDto.getDeptId());
        List<String> list = new ArrayList<>();
        //将所有部门ID放入集合
        for (SysDept sysDept : sysDeptList) {
            list.add(sysDept.getDeptId().toString());
        }
        list.add(achievementsDataDto.getDeptId().toString());
        achievementsDataDto.setDeptIds(StringUtils.join(list, ","));
        /**时间选择 1 全部  2 一周  3 一月  4 三个月  5 一年*/
        if ("2".equals(achievementsDataDto.getTimeOption())) {
            achievementsDataDto.setSelectEndTime(DateUtil.now());
            achievementsDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -7)));
        }
        if ("3".equals(achievementsDataDto.getTimeOption())) {
            achievementsDataDto.setSelectEndTime(DateUtil.now());
            achievementsDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
        }
        if ("4".equals(achievementsDataDto.getTimeOption())) {
            achievementsDataDto.setSelectEndTime(DateUtil.now());
            achievementsDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -90)));
        }
        if ("5".equals(achievementsDataDto.getTimeOption())) {
            achievementsDataDto.setSelectEndTime(DateUtil.now());
            achievementsDataDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -365)));
        }
        startPage(achievementsDataDto.getPageNum(), achievementsDataDto.getPageSize());
        List<AchievementsDataDto> achievementsDataDtoList = statisticalDataMapper.getManageAchievementsData(achievementsDataDto);
        return achievementsDataDtoList;
    }


    @Override
    public List<OrgScheduleDto> getOrgSchedule(OrgScheduleDto orgScheduleDto) {
        if (StrUtil.isEmpty(orgScheduleDto.getTimeOption())) {
            throw new CustomException("请选择时间区间");
        }
        //创建部门集合
        List<SysDept> sysDeptList = new ArrayList<>();
        if (StringUtils.isNull(orgScheduleDto.getDeptParentId())) {
            //如果部门列表为空则默认传当前人的所在部门的所有下面一级部门
            sysDeptList = deptService.selectDeptListForPerson(SecurityUtils.getLoginUser().getUser().getUserId());
        } else {
            /**先获取所选部门的子部门*/
            SysDept dept = new SysDept();
            dept.setParentId(orgScheduleDto.getDeptParentId());
            sysDeptList = deptService.selectDeptList(dept);
        }
        if (StringUtils.isNull(sysDeptList) && sysDeptList.size() == 0) {
            throw new CustomException("该部门暂子部门数据");
        }
        //获取要查询的部门数组
        Long[] deptIds = null;
        //将部门数据转化为数组
        deptIds = sysDeptList.stream().map(sysDept -> sysDept.getDeptId()).collect(Collectors.toList()).toArray(new Long[0]);
        /**时间选择 1 全部  2 一周  3 一月  4 三个月  5 一年*/
        if ("2".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -7)));
        }
        if ("3".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
        }
        if ("4".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -90)));
        }
        if ("5".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -365)));
        }
        orgScheduleDto.setDeptIdLongArr(deptIds);
        /**先根据条件查询出所有满足的任务单*/
        List<StatisticalDataDto> statisticalDataDtoList = statisticalDataMapper.getScheduleTask(orgScheduleDto);
        List<DataCountDto> dataCountDtoList = new ArrayList<>();
        for (StatisticalDataDto sdd : statisticalDataDtoList) {
            List<Map<String, String>> mapList = byNetworkReviewTaskService.selectByNetworkReviewTaskScheduleById(sdd.getTaskId());
            for (Map<String, String> map : mapList) {
                DataCountDto dataCountDto = new DataCountDto();
                dataCountDto.setDeptName(map.get("name"));
                dataCountDto.setSchedule(Long.parseLong(map.get("value")));
                dataCountDtoList.add(dataCountDto);
            }
        }
        //分组后，求出每组中某属性的平均值
        Map<String, Double> avg = dataCountDtoList.stream().filter(i -> i.getSchedule() != null).
                collect(Collectors.groupingBy(DataCountDto::getDeptName, Collectors.averagingDouble(DataCountDto::getSchedule)));
        //遍历map,对每个组织单位进度进行统计并排序
        List<DataCountDto> dataCountDtoListResult = new ArrayList<>();
        Set keySet = avg.keySet();
        for (Object key : keySet) {
            DataCountDto dataCountDto = new DataCountDto();
            dataCountDto.setDeptName(String.valueOf(key));
            dataCountDto.setSchedule(avg.get(key).longValue());
            dataCountDtoListResult.add(dataCountDto);
        }
        //dataCountDtoListResult = dataCountDtoListResult.stream().sorted(Comparator.comparing(DataCountDto::getSchedule)).collect(Collectors.toList());
        //降序排列
        dataCountDtoListResult.sort((a, b) -> b.getSchedule().intValue() - a.getSchedule().intValue());
        //新建返回集合
        List<OrgScheduleDto> listResult = new ArrayList<>();
        //双层循环判断出需要展示的组织
        int a = 0;
        for (DataCountDto dataCountDto : dataCountDtoListResult) {
            for (SysDept sysDept : sysDeptList) {
                if (dataCountDto.getDeptName().equals(sysDept.getDeptName())) {
                    OrgScheduleDto orgScheduleDto1 = new OrgScheduleDto();
                    orgScheduleDto1.setDeptName(dataCountDto.getDeptName());
                    orgScheduleDto1.setTaskSchedule(String.valueOf(dataCountDto.getSchedule()));
                    orgScheduleDto1.setRank(String.valueOf(++a));
                    orgScheduleDto1.setDeptId(sysDept.getDeptId());
                    listResult.add(orgScheduleDto1);
                }
            }
        }
        return listResult;
    }


    @Override
    public List<OrgScheduleDto> getOrgScheduleForById(OrgScheduleDto orgScheduleDto) {
        if (StrUtil.isEmpty(orgScheduleDto.getTimeOption())) {
            throw new CustomException("请选择时间区间");
        }
        //创建部门集合
        List<SysDept> sysDeptList = new ArrayList<>();
        //获取要查询的部门数组
        Long[] deptIds = null;
        if (StringUtils.isNull(orgScheduleDto.getDeptIds())) {
            throw new CustomException("部门ID为空");
        } else {
            /**先获取所选部门*/
            String[] deptIdArr = orgScheduleDto.getDeptIds().split(",");
            List<Long> longList = new ArrayList<>();
            for (String str : deptIdArr) {
                longList.add(Long.parseLong(str));
            }
            deptIds = longList.toArray(new Long[0]);
            //根绝数组查询出所有的部门
            sysDeptList = sysDeptMapper.selectDeptByIds(deptIds);
        }
        //获取该组织名称
        SysDept sysDept = sysDeptMapper.selectDeptById(deptIds[0]);
        /**时间选择 1 全部  2 一周  3 一月  4 三个月  5 一年*/
        if ("2".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -7)));
        }
        if ("3".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
        }
        if ("4".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -90)));
        }
        if ("5".equals(orgScheduleDto.getTimeOption())) {
            orgScheduleDto.setSelectEndTime(DateUtil.now());
            orgScheduleDto.setSelectStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -365)));
        }
        orgScheduleDto.setDeptIdLongArr(deptIds);
        /**先根据条件查询出所有满足的任务单*/
        startPage(orgScheduleDto.getPageNum(), orgScheduleDto.getPageSize());
        List<StatisticalDataDto> statisticalDataDtoList = statisticalDataMapper.getScheduleTask(orgScheduleDto);
        List<OrgScheduleDto> orgScheduleDtoList = new ArrayList<>();
        for (StatisticalDataDto sdd : statisticalDataDtoList) {
            List<Map<String, String>> mapList = byNetworkReviewTaskService.selectByNetworkReviewTaskScheduleById(sdd.getTaskId());
            for (Map<String, String> map : mapList) {
                //如果名称相同则该任务单代表该组织已参与
                if (sysDept.getDeptName().equals(map.get("name"))) {
                    OrgScheduleDto orgScheduleDto1 = new OrgScheduleDto();
                    orgScheduleDto1.setTaskName(sdd.getTaskName());
                    orgScheduleDto1.setTaskSchedule(map.get("value"));
                    orgScheduleDto1.setTaskId(sdd.getTaskId());
                    orgScheduleDtoList.add(orgScheduleDto1);
                }
            }
        }
        return orgScheduleDtoList;
    }

    @Override
    public Map<String, Object> taskNum(Long deptId) {

        List<Long> deptUserIdList = deptService.selectUserIdForDept(deptId);
        Long[] deptUserIdArr = new Long[deptUserIdList.size()];
        deptUserIdArr = deptUserIdList.toArray(deptUserIdArr);
        //获取所有网评任务状态数量 1 进行 2未完成 3已完成 4作废
        List<StatisticalDataDto> taskStatusList = statisticalDataMapper.selectByNetworkReviewTaskNum(deptUserIdArr);
        //构建结果集
        Map<String, Object> map = new HashMap<>();
        //进行中的任务量
        int pending = 0;
        //已完成的任务量
        int completed = 0;

        for (StatisticalDataDto statisticalDataDto : taskStatusList) {
            //进行中
            if (statisticalDataDto.getType() == 1) {
                pending = statisticalDataDto.getNum();
            }
            //已完成
            if (statisticalDataDto.getType() == 3) {
                completed = statisticalDataDto.getNum();
            }

        }
        map.put("pending", pending);
        map.put("completed", completed);
        map.put("name", "网评管理任务");

        return map;
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage(Integer pageNum, Integer pageSize) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }
}
