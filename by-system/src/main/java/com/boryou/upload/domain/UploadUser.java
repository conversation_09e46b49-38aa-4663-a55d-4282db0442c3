package com.boryou.upload.domain;

import com.boryou.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/***
 * <AUTHOR>
 * @description //文件上传文件ID和绑定人
 * @date 11:19 2021/12/6
 * @param
 */
@Data
public class UploadUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID--当前登录用户ID--查询条件
     */
    private String fileId;

    /**
     * 当前文件的可下载用户
     */
    private List<Long> downloadUserIds;


}
