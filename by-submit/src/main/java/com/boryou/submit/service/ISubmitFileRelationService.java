package com.boryou.submit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.domain.SFile;
import com.boryou.manage.domain.File;
import com.boryou.submit.domain.SubmitFileRelation;

import java.util.List;

/**
 * 信息报送附件Service接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface ISubmitFileRelationService extends IService<SubmitFileRelation> {
    /**
     * 查询信息报送附件
     *
     * @param id 信息报送附件ID
     * @return 信息报送附件
     */
    public SubmitFileRelation selectSubmitFileRelationById(Long id);

    /**
     * 查询信息报送附件列表
     *
     * @param submitFileRelation 信息报送附件
     * @return 信息报送附件集合
     */
    public List<SubmitFileRelation> selectSubmitFileRelationList(SubmitFileRelation submitFileRelation);

    /**
     * 新增信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    public int insertSubmitFileRelation(SubmitFileRelation submitFileRelation);

    /**
     * 修改信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    public int updateSubmitFileRelation(SubmitFileRelation submitFileRelation);

    /**
     * 批量删除信息报送附件
     *
     * @param ids 需要删除的信息报送附件ID
     * @return 结果
     */
    public int deleteSubmitFileRelationByIds(String[] ids);

    public int deleteSubmitFileRelationByFileIds(String[] ids);

    /**
     * 删除信息报送附件信息
     *
     * @param id 信息报送附件ID
     * @return 结果
     */
    public int deleteSubmitFileRelationById(Long id);


    List<SFile> getFilesList(String id);
}
