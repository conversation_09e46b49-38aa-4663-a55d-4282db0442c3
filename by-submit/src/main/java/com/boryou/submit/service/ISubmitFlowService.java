package com.boryou.submit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.submit.domain.SubmitFlow;

import java.util.List;

/**
 * 报送流程关系映射Service接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface ISubmitFlowService extends IService<SubmitFlow> {
    /**
     * 查询报送流程关系映射
     *
     * @param id 报送流程关系映射ID
     * @return 报送流程关系映射
     */
    public SubmitFlow selectSubmitFlowById(String id);

    /**
     * 查询报送流程关系映射列表
     *
     * @param submitFlow 报送流程关系映射
     * @return 报送流程关系映射集合
     */
    public List<SubmitFlow> selectSubmitFlowList(SubmitFlow submitFlow);

    /**
     * 新增报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    public int insertSubmitFlow(SubmitFlow submitFlow);

    /**
     * 修改报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    public int updateSubmitFlow(SubmitFlow submitFlow);

    /**
     * 批量删除报送流程关系映射
     *
     * @param ids 需要删除的报送流程关系映射ID
     * @return 结果
     */
    public int deleteSubmitFlowByIds(String[] ids);

    /**
     * 删除报送流程关系映射信息
     *
     * @param id 报送流程关系映射ID
     * @return 结果
     */
    public int deleteSubmitFlowById(String id);
}
