package com.boryou.submit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysRole;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.domain.InfoType;
import com.boryou.domain.SFile;
import com.boryou.init.ExpireTaskInit;
import com.boryou.service.IInfoTypeService;
import com.boryou.submit.constant.SubmitConstant;
import com.boryou.submit.constant.SubmitUserTypeEnum;
import com.boryou.submit.domain.SubmitFileRelation;
import com.boryou.submit.domain.SubmitFlow;
import com.boryou.submit.domain.SubmitInfo;
import com.boryou.submit.domain.SubmitProcess;
import com.boryou.submit.dto.ApprovalDTO;
import com.boryou.submit.dto.SubmitInfoAddDTO;
import com.boryou.submit.dto.SubmitInfoDTO;
import com.boryou.submit.mapper.SubmitInfoMapper;
import com.boryou.submit.service.ISubmitFileRelationService;
import com.boryou.submit.service.ISubmitFlowService;
import com.boryou.submit.service.ISubmitInfoService;
import com.boryou.submit.service.ISubmitProcessService;
import com.boryou.submit.vo.SubmitInfoCzzVo;
import com.boryou.submit.vo.SubmitInfoFxsVo;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysRoleService;
import com.boryou.system.service.ISysUserService;
import com.boryou.utils.AreaUtil;
import com.boryou.utils.SMSUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import static com.boryou.submit.constant.SubmitConstant.*;

/**
 * 信息报送Service业务层处理
 * 报送的截至日期只有两个地方可以设置：1.管理者主动下发时选择截至日期    2.管理者对分析师的单子进行分发时选择。 当然他也可以设置暂无
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubmitInfoServiceImpl extends ServiceImpl<SubmitInfoMapper, SubmitInfo> implements ISubmitInfoService {

    private final ISubmitProcessService submitProcessService;
    private final TaskService taskService;
    private final RuntimeService runtimeService;
    private final HistoryService historyService;
    private final ISubmitFileRelationService submitFileRelationService;
    private final ISysUserService userService;
    private final ISysDeptService deptService;
    private final ISysRoleService roleService;
    private final IInfoTypeService typeService;
    private final ISubmitFlowService flowService;
    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    @Lazy
    private ISubmitInfoService submitInfoService;

    @Autowired
    private AreaUtil areaUtil;


    //流程节点对应的对象操作
    private final static Map<String, String> PROCESS_ACT_MAPS = new HashMap<>();
    //流程状态Map
    private final static Map<Integer, Map<Integer, String>> TYPE_NAMES = new HashMap<>();
    // 定时任务map
    private final static Map<String, ScheduledFuture> SCHEDULE_MAP = ExpireTaskInit.SCHEDULE_MAP;

    static {
        HashMap<Integer, String> value = new HashMap<>();
        value.put(0, "待审核");
        value.put(1, "待修改");
        value.put(2, "未采纳");
        value.put(3, "已采纳");
        TYPE_NAMES.put(0, value);


        HashMap<Integer, String> value1 = new HashMap<>();
        value1.put(0, "待审核");
        value1.put(1, "待处置");
        value1.put(2, "已处理");
        value1.put(3, "已过期");
        value1.put(4, "已完结");
        TYPE_NAMES.put(1, value1);

        HashMap<Integer, String> value2 = new HashMap<>();
        value2.put(1, "待处置");
        value2.put(2, "已处置");
        value2.put(3, "已转派");
        value2.put(4, "已过期");
        value2.put(5, "已完结");
        TYPE_NAMES.put(2, value2);

        //操作事件 1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结
        PROCESS_ACT_MAPS.put("1", "无需处置");
        PROCESS_ACT_MAPS.put("2", "审核(补充材料)");
        PROCESS_ACT_MAPS.put("3", "直接处置");
        PROCESS_ACT_MAPS.put("4", "审核(上报)");
        PROCESS_ACT_MAPS.put("5", "分发");
        PROCESS_ACT_MAPS.put("6", "转派");
        //重新处置不是一个节点，而是继续转派或者直接处理，或者完结
        PROCESS_ACT_MAPS.put("7", "处置");
        PROCESS_ACT_MAPS.put("8", "完结");
    }

    /**
     * 查询信息报送
     *
     * @param id 信息报送ID
     * @return 信息报送
     */
    @Override
    public SubmitInfo selectSubmitInfoById(Long id) {
        SubmitInfo submitInfo = this.getById(id);
        if (null == submitInfo) {
            throw new CustomException("该报送不存在");
        }
        String dispenserId = submitInfo.getDispenserId();
        if (StringUtils.isNotEmpty(dispenserId)) {
            List<String> strings = StrUtil.splitTrim(dispenserId, ",");
            submitInfo.setDisposers(strings.stream().map(Long::parseLong).collect(Collectors.toList()));
        }
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(submitInfo.getId()).taskInvolvedUser(SecurityUtils.getUserId() + "").singleResult();
        if (null != task) {
            submitInfo.setAssigneer(task.getAssignee());
        }
        if (null != submitInfo.getManagerUser()) {
            SysUser sysUser = userService.selectUserById(submitInfo.getManagerUser());
            if (null != sysUser) {
                submitInfo.setManagerUserName(sysUser.getNickName());
            }
        }
        setReciverName(submitInfo);
        //设置信息类别中文名称
        InfoType type = typeService.getById(submitInfo.getInfoType());
        if (null != type) {
            submitInfo.setInfoTypeName(type.getTypeName());
        }
        //设置任务的创建人昵称
        Long createById = submitInfo.getCreateById();
        if (null != createById) {
            SysUser sysUser = userService.selectUserById(createById);
            if (sysUser != null) {
                submitInfo.setCreateBy(sysUser.getNickName());
            }
        }
        //设置任务的管理者昵称
        Long managerUser = submitInfo.getManagerUser();
        if (null != managerUser) {
            SysUser sysUser = userService.selectUserById(managerUser);
            if (sysUser != null) {
                submitInfo.setManagerUserName(sysUser.getNickName());
            }
        }
        if (validPermission() == SubmitUserTypeEnum.CHUZHIZHE.getType()) {
            setCzzState(submitInfo);
        }
        return submitInfo;
    }

    /**
     * 查询信息报送列表
     *
     * @param submitInfo 信息报送
     * @return 信息报送
     */
    @Override
    public List<SubmitInfo> selectSubmitInfoList(SubmitInfo submitInfo) {
        int userType = setQueryCondition(submitInfo);
        List<SubmitInfo> submitInfos = baseMapper.selectSubmitInfoAll(submitInfo);
        Map<Integer, String> statusMaps = TYPE_NAMES.get(userType);
        for (SubmitInfo info : submitInfos) {
            Task task = taskService.createTaskQuery().processInstanceBusinessKey(info.getId()).singleResult();
            if (null != task) {
                //在管理者 user.userId==infoParams.assigneer  的时候可以进行（无需处置、补充材料、审核上报、处置, 分发）操作
                info.setAssigneer(task.getAssignee());
            }
            setReciverName(info);
            if (userType == 0) {
                info.setStatusName(statusMaps.get(info.getFxsState()));
            } else if (userType == 1) {
                info.setStatusName(statusMaps.get(info.getGlState()));
            } else if (userType == 2) {
                setCzzState(info);
                info.setStatusName(statusMaps.get(info.getCzState()));
            }
        }
        return submitInfos;
    }

    /**
     * 设置查询过滤条件
     *
     * @param submitInfo 过滤对象
     * @return int
     * <AUTHOR>
     * @date 2024/1/30 10:01
     **/
    private int setQueryCondition(SubmitInfo submitInfo) {
        String currentUserId = SecurityUtils.getUserIdStr();
        submitInfo.setCreateBy(currentUserId);
        int userType = validPermission();
        if (userType == 0 || userType == 2) {
            //分析师、处置者，就查找第一次处置的管理者是谁
            submitInfo.setManagerUserName(submitInfo.getManagerUser() == null ? null : submitInfo.getManagerUser() + "");
        } else if (userType == 1) {
            //页面选的谁，查看谁的报送
            submitInfo.setCreateById(submitInfo.getManagerUser());
        }
        int fxsstate = submitInfo.getFxsState() == null ? NO_BUSINESS_CODE : submitInfo.getFxsState();
        int czState = submitInfo.getCzState() == null ? NO_BUSINESS_CODE : submitInfo.getCzState();
        int glState = submitInfo.getGlState() == null ? NO_BUSINESS_CODE : submitInfo.getGlState();
        if (SecurityUtils.isAdmin(SecurityUtils.getUserIdL()) && (fxsstate == -1 || glState == -1 || czState == -1)) {
            //超级管理员可见全部分析师报送、处置者报送   注意fxsstate和glState和处置者参数看到的有区别
            submitInfo.setAll(1);
            submitInfo.setOwner(1);
            submitInfo.setFilter(null);
            submitInfo.setAdmin(true);
        } else {
            if (fxsstate == -1 || czState == -1 || glState == -1) {
                //如果是查全部，需要关联工作流历史表
                submitInfo.setAll(1);
                //如果是owner可以查询委托人的任务
                submitInfo.setOwner(1);
            } else {
                if (userType == 0) {
                    if (fxsstate == 0 || fxsstate == 1 || fxsstate == 2 || fxsstate == 3) {
                        //如果是分析师 ，需要关联工作流历史表
                        submitInfo.setAll(1);
                        //如果是owner可以查询委托人的任务
                        submitInfo.setOwner(1);
                    }
                    if (fxsstate != 0) {
                        //如果是分析师，除了查看全部,待审核，其他tab状态只能看自己创建的单子，否则会有数据共享问题。
                        // 2024年1月31日确实我是这样想的，张三a只能看到张三a的修改，王五看不到张三a的修改， 2024年2月1日测试提：其他的tab页相加应该等于全部，于是我又注释了下面这个代码，相当于就要数据共享这个意思
//                        submitInfo.setAssigneer(currentUserId);
                    }
                } else if (userType == 1) {
                    submitInfo.setAll(1);
                    submitInfo.setOwner(1);
                } else if (userType == 2) {
                    //如果是已过期，已完结，需要关联工作流历史表
                    submitInfo.setAll(1);
                    //如果是owner可以查询委托人的任务
                    submitInfo.setOwner(1);
                    if (1 == czState) {
                        //待处理  查询由处置者(我)处理的单子
                        submitInfo.setCzState(null);
                        submitInfo.setOwner(1);
                        submitInfo.setFilter(
                                " inner join ( select f.info_id,max(create_time) create_time FROM by_submit_flow f group by f.info_id  )f on f.info_id=b.id  " +
                                        " inner join by_submit_flow k on k.info_id=f.info_id and (k.act_name='distribute' or k.act_name='forward') and k.receiver_id='" + currentUserId + "' and f.create_time=k.create_time and b.cz_state in(1,3)"
                        );
                    } else if (3 == czState) {
                        //已转派   查询我转派的单子
                        submitInfo.setOwner(1);
                        submitInfo.setFilter(
                                " inner join ( select f.info_id,max(create_time) create_time FROM by_submit_flow f group by f.info_id  )f on f.info_id=b.id  " +
                                        " inner join by_submit_flow k on k.info_id=f.info_id and k.act_name='forward' and k.forward_id='" + currentUserId + "' and f.create_time=k.create_time  and b.cz_state =3"
                        );
                    }
                }
            }
        }
        return userType;
    }

    /**
     * 如果是处置者的全部、待处理的话，就需要对单子归属做转换，A处置者转派B,B登录进来就是待处置，A登录进来就是已转派
     *
     * @param info 报送对象
     * <AUTHOR>
     * @date 2024/1/24 14:14
     **/
    private void setCzzState(SubmitInfo info) {
        int czState1 = info.getCzState() == null ? NO_BUSINESS_CODE : info.getCzState();
        if (czState1 == -1 || czState1 == 1 || czState1 == 3) {
            LambdaQueryWrapper<SubmitFlow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubmitFlow::getInfoId, info.getId());
            queryWrapper.isNotNull(SubmitFlow::getCreateTime);
            List<SubmitFlow> flowAllList = flowService.list(queryWrapper);
            if (CollectionUtil.isEmpty(flowAllList)) {
                return;
            }
            List<SubmitFlow> runFlowList = flowAllList.stream().filter(s -> "0".equals(s.getHistory())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(runFlowList)) {
                return;
            }
            List<SubmitFlow> latestFlowMap = runFlowList.stream().sorted(Comparator.comparing(SubmitFlow::getCreateTime).reversed()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(latestFlowMap)) {
                //需要获取最近一次相同name的接收人，因为有可能不是同一阶段的操作，导致操作乱序
                Map<String, List<SubmitFlow>> groupedByNames = latestFlowMap.stream().collect(Collectors.groupingByConcurrent(SubmitFlow::getActName));
                List<SubmitFlow> firstValue = getFirstValue(groupedByNames);
                if (CollectionUtil.isEmpty(firstValue)) {
                    return;
                }
                List<String> latestAssigneer = firstValue.stream().map(SubmitFlow::getReceiverId).collect(Collectors.toList());
                String assigneer = CollectionUtil.join(latestAssigneer, ",");
                //上面的排序是为了判断最新的节点是不是分发，还是转派，如果是转派，就设置为待处理
                List<String> actNames = firstValue.stream().map(SubmitFlow::getActName).collect(Collectors.toList());
                List<String> forwards = firstValue.stream().map(SubmitFlow::getForwardId).collect(Collectors.toList());
                String latestAct = actNames.get(0);
                info.setAssigneer(assigneer);
                if ("distribute".equals(latestAct)) {
                    info.setCzState(1);
                } else if ("forward".equals(latestAct)) {
                    if (latestAssigneer.contains(SecurityUtils.getUserIdStr())) {
                        info.setCzState(1);
                    } else if (forwards.contains(SecurityUtils.getUserIdStr())) {
                        info.setCzState(3);
                    }
                }
            }
        }
    }

    /**
     * 从Map中取出第一个Key的值
     */
    public static <K, V> V getFirstValue(Map<K, V> map) {
        Iterator<Map.Entry<K, V>> iterator = map.entrySet().iterator();
        if (iterator.hasNext()) {
            Map.Entry<K, V> entry = iterator.next();
            return entry.getValue();
        }
        // 如果Map为空，你可以根据需求返回一个默认值，这里返回null
        return null;
    }

    /**
     * 根据原型设置列表中的接收人
     *
     * @param info 报送对象
     * <AUTHOR>
     * @date 2024/1/17 13:38
     **/
    private void setReciverName(SubmitInfo info) {
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        List<String> roles = currentUser.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        //0是分析报送  1.主动下发
        Integer submitType = info.getSubmitType();
        if (submitType == null) {
            return;
        }
        //分析师：分析师看到的列表、详情是管理者(接收人)
        //处置者：看到的列表、详情都是管理者(分发人)
        //管理者：如果是分析师报送给管理者  管理者看到的详情是分析师名称（接收人），列表叫（报送/分发人)          管理者自己下发的看到的是自己名称（分发人）
        String createBy = info.getCreateBy();
        if ((roles.contains(SubmitUserTypeEnum.FENXISHI.getName()) || roles.contains(SubmitUserTypeEnum.CHUZHIZHE.getName())) && submitType == SubmitUserTypeEnum.FENXISHI.getType()) {
            info.setDispenser(info.getManagerUserName());
        } else if (roles.contains(SubmitUserTypeEnum.GUANLI.getName()) || submitType == SubmitUserTypeEnum.GUANLI.getType()) {
            info.setDispenser(createBy);
        }
        info.setSubmitTypeName(submitType == 1 ? "主动下发" : "分析报送");

    }


    /**
     * 新增信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSubmitInfo(SubmitInfoAddDTO submitInfo) {
        validPermission(submitInfo);
        String id = IdUtil.getSnowflakeNextIdStr();
        submitInfo.setId(id);
        submitInfo.setArticleAreaName(areaUtil.getAreaNames(submitInfo.getArticleArea()));
        if (submitInfo.getSubmitType() == 0) {
            saveFxsSubmit(submitInfo);
        } else {
            saveManagerSubmit(submitInfo);
        }
        //新增时大概率不需要记录日志附件表，因为报送基本信息中已经存在附件表了
        List<SubmitFileRelation> files = submitInfo.getFiles();
        submitInfoService.addAttachmentFiles(files, submitInfo.getId(), null);
        return 1;
    }

    /**
     * 新增管理者报送
     *
     * <AUTHOR>
     * @date 2024/1/30 13:15
     **/
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveManagerSubmit(SubmitInfoAddDTO submitInfo) {
        long start1 = System.currentTimeMillis();
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        //获取账号名称-唯一
        String applicatId = SecurityUtils.getLoginUser().getUser().getUserId() + "";
        String applicatName = SecurityUtils.getUsername();
        if (CollectionUtil.isEmpty(submitInfo.getDisposers())) {
            throw new CustomException("请选择处置人员!");
        }
        //管理人员需要走另一个流程审批
        //启动流程
        Authentication.setAuthenticatedUserId(applicatId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("manager_user", applicatId);
        ProcessInstance instance = runtimeService.startProcessInstanceByKey(SubmitConstant.MANAGER_FLOW, submitInfo.getId(), map);
        String instanceId = instance.getProcessInstanceId();
        log.info("流程启动:{},申请人:{}", instanceId, applicatName);
        //管理者流程启动后必须是处置员处置
        String join = CollectionUtil.join(submitInfo.getDisposers(), ",");
        Task task = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).orderByTaskCreateTime().desc().singleResult();
        taskService.addComment(task.getId(), instance.getProcessInstanceId(), join + "处置员处置");

        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("approval_opinion", "distribute");
        //设置处置者，处置者需要签收
        variables.put("processor", join);
        taskService.complete(task.getId(), variables);
        submitInfo.setDispenserId(join);

        //记录共有日志
        SubmitProcess process = new SubmitProcess();
        process.setDispenserId(join);
        process.setDescription("分发至：" + getDispenserName(join));
        submitInfoService.recordFlow(submitInfo.getId(), SubmitConstant.FLOW_ACT_DISTRIBUTE, process, SubmitConstant.SCOPE_SHARE);
        saveSubmitInfo(submitInfo, currentUser);
        //更新处理状态
        submitInfoService.updateState(submitInfo.getId(), null, 1, 1);
        log.info("报送耗时1---" + (System.currentTimeMillis() - start1));
        long start2 = System.currentTimeMillis();
        for (Long disposer : submitInfo.getDisposers()) {
            SysUser sysUser = userService.selectUserById(disposer);
            if (null != sysUser && StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
                SMSUtils.sendUndo(Collections.singletonList(sysUser.getPhonenumber()));
            }
            saveFlow(submitInfo.getId(), "distribute", disposer, "0");
        }
        // 如果存在截止时间，需要创建挂起任务
        suspendProcess(instanceId, submitInfo);
        log.info("报送耗时2---" + (System.currentTimeMillis() - start2));
    }

    /**
     * 新增分析师报送
     *
     * <AUTHOR>
     * @date 2024/1/30 13:16
     **/
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveFxsSubmit(SubmitInfoAddDTO submitInfo) {
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        //获取账号名称-唯一
        String applicatId = SecurityUtils.getLoginUser().getUser().getUserId() + "";
        String applicatName = SecurityUtils.getUsername();
        Long managerUser = submitInfo.getManagerUser();

        if (null == managerUser || managerUser <= 0) {
            throw new CustomException("请选择处置人员!");
        }
        String managerUserStr = submitInfo.getManagerUser() + "";
        //启动流程
        Authentication.setAuthenticatedUserId(applicatId);

        //获取当前小组的所有用户，用于组内可看
        SysUser user = new SysUser();
        user.setDeptIds(currentUser.getDeptId());
        //加强过滤，只要同组的分析师可见
        Long[] longs = {roleService.selectRoles().stream().filter(role -> SubmitUserTypeEnum.FENXISHI.getName().equals(role.getRoleKey())).findFirst().get().getRoleId()};
        user.setRoleIds(longs);
        List<SysUser> sysUsers = userService.selectUserLists(user);
        List<Long> users = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        String userIds = CollectionUtil.join(users, ",");
        HashMap<String, Object> map = new HashMap<>(1);
        map.put(SubmitConstant.ANALYST_APPLICAT, userIds);
        ProcessInstance instance = runtimeService.startProcessInstanceByKey(SubmitConstant.FENXISHI_FLOW, submitInfo.getId(), map);
        log.info("流程启动:{},申请人:{}", instance.getProcessInstanceId(), applicatName);
//            instanceId = instance.getProcessInstanceId();
        //启动后立马让他签收
        Task task = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).orderByTaskCreateTime().desc().singleResult();
        //设置同组可看后，自己签收，小组其他人在历史任务里面可以查看到这一单
        //claim和Assignee都可以实现对任务的认领。两个方法的区别在于执行 claim 方法时会检查该任务是否已被签收，如果已被签收，则会抛出 ActivitiTaskAlreadyClaimedException 异常，其他方面两个方法效果一致。
        //设置委托人，后续可以继续查询出来工单，不然下次转派人后就查不出来了

        taskService.setOwner(task.getId(), managerUserStr);
        taskService.setAssignee(task.getId(), applicatId);
        //只有分析师报送才给注释-发给谁审批了，管理者是自己。
        taskService.addComment(task.getId(), instance.getProcessInstanceId(), submitInfo.getManagerUserName() + "审核处理");
//            Map<String, Object> variables = new HashMap<>(1);
//            variables.put("manager_user", submitInfo.getManagerUser());
//            taskService.complete(task.getId(), variables);
        taskService.complete(task.getId(), Collections.singletonMap("manager_user", managerUserStr));


        //记录共有日志
        SubmitProcess process = new SubmitProcess();
        process.setManagerUser(managerUser);
        recordFlow(submitInfo.getId(), FLOW_ACT_APPLY, process, SubmitConstant.SCOPE_SHARE);

        //记录分析师独有日志
        SubmitProcess process1 = new SubmitProcess();
        recordFlow(submitInfo.getId(), FLOW_ACT_APPLY, process1, SubmitConstant.SCOPE_FXS);

        saveSubmitInfo(submitInfo, currentUser);
        //更新处理状态
        submitInfoService.updateState(submitInfo.getId(), 0, 0, null);

        SysUser sysUser = userService.selectUserById(managerUser);
        if (null != sysUser && StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
            SMSUtils.sendApproval(Collections.singletonList(sysUser.getPhonenumber()));
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addAttachmentFiles(List<SubmitFileRelation> files, String infoId, String processId) {
        if (CollectionUtil.isNotEmpty(files)) {
            for (SubmitFileRelation file : files) {
                if (file.getFileId() == null && StringUtils.isEmpty(file.getFileId())) {
                    throw new CustomException("文件参数异常");
                }
                file.setId(IdUtil.getSnowflakeNextIdStr());
                file.setInfoId(infoId);
                //第一次附件上次上传不需要记录流程id
                file.setProcessId(processId);
            }
            submitFileRelationService.saveBatch(files);
        }
    }

    @Override
    public List<TreeSelect> loadApprover() {
        //1.获取当前管理人员id  2.获取所有管理人员的大部门集合  3.根据部门集合加载下面的管理人员列表
        List<SysDept> depts = deptService.getDeptsByRoleKey(SubmitUserTypeEnum.GUANLI.getName(), SecurityUtils.getLoginUser().getUser().getDeptId());
        List<TreeSelect> sysDepts = deptService.getDeptTree(depts);
        SysUser sysUser = new SysUser();
        sysUser.setRoleIds(new Long[]{roleService.selectRoles().stream().filter(role -> SubmitUserTypeEnum.GUANLI.getName().equals(role.getRoleKey())).findFirst().get().getRoleId()});
        deptService.addPeopleByRole(sysDepts, sysUser);
        return sysDepts;
    }

    @Override
    public List<TreeSelect> loadManager() {
        SysDept dept = new SysDept();
        List<TreeSelect> deptTree = new ArrayList<>();
        Long relaDeptId = SecurityUtils.getLoginUser().getUser().getRelaDeptId();
        if (null == relaDeptId) {
            return deptTree;
        }
        dept.setDeptId(relaDeptId);
        SysRole manager = roleService.selectRoles().stream().filter(role -> SubmitUserTypeEnum.GUANLI.getName().equals(role.getRoleKey())).findFirst().get();
        //只查当前用户的部门及下属
        Long[] roleIds = {manager.getRoleId()};
        dept.setRoleIds(roleIds);
        List<SysDept> depts = deptService.selectDeptLists(dept);
        deptTree = deptService.getDeptTree(depts);
        SysUser sysUser = new SysUser();
        sysUser.setRoleIds(roleIds);
        deptService.addPeopleByRole(deptTree, sysUser);
        return deptTree;
    }

    @Override
    public Integer getUserRole() {
        return validPermission();
    }

    private void setDeadLine(SubmitInfo submitInfo) {
        String deadlineNum = submitInfo.getDeadlineNum();
        if (null != deadlineNum && !"暂无".equals(deadlineNum)) {
            DateTime dateTime = getDeadTime(deadlineNum);
            submitInfo.setDeadline(dateTime);
            submitInfo.setDeadlineNum(deadlineNum);
        } else {
            submitInfo.setDeadline(null);
        }
    }

    private DateTime getDeadTime(String deadlineNum) {
        deadlineNum = deadlineNum.replace("H", "");
        return DateUtil.offsetHour(new Date(), Integer.parseInt(deadlineNum));
    }

    public void saveSubmitInfo(SubmitInfo submitInfo, SysUser currentUser) {
        setDeadLine(submitInfo);
        Date createTime = new Date();
        String username = SecurityUtils.getUsername();
        submitInfo.setCreateBy(username);
        submitInfo.setCreateTime(createTime);
        submitInfo.setUpdateBy(username);
        submitInfo.setUpdateTime(createTime);
        submitInfo.setCreateById(currentUser.getUserId());
        super.save(submitInfo);
    }


    private void validPermission(SubmitInfo submitInfo) {
        int submitType = validPermission();
        if (submitType == -1 || submitType == SubmitUserTypeEnum.CHUZHIZHE.getType()) {
            throw new CustomException("当前用户无法申报，请确认角色权限！");
        }
        submitInfo.setSubmitType(submitType);
    }

    @Override
    public int validPermission() {
        List<String> roles = SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        int submitType = -1;
        if (roles.contains(SubmitUserTypeEnum.FENXISHI.getName())) {
            submitType = SubmitUserTypeEnum.FENXISHI.getType();
        } else if (roles.contains(SubmitUserTypeEnum.GUANLI.getName())) {
            submitType = SubmitUserTypeEnum.GUANLI.getType();
        } else if (roles.contains(SubmitUserTypeEnum.CHUZHIZHE.getName())) {
            submitType = SubmitUserTypeEnum.CHUZHIZHE.getType();
        }
        return submitType;
    }

    /**
     * 审核操作按钮
     * 整个报送的处理任务核心在此处
     * 一共只有两种人员可以操作：
     * 1.管理人员 操作
     * 2.处置人员操作
     * <p>
     * <p>
     * 按钮事件：1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结     重新处置不是新的节点，而是直接变为转派
     *
     * @param approvalDTO 审核对象
     * @return boolean
     * <AUTHOR>
     * @date 2024/1/9 16:09
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approvalSubmit(ApprovalDTO approvalDTO) {
        String processResult = approvalDTO.getProcessResult();
        if (StringUtils.isEmpty(processResult) || StringUtils.isEmpty(approvalDTO.getInfoId()) || null == PROCESS_ACT_MAPS.get(processResult)) {
            throw new IllegalArgumentException("请检查参数是否缺失或者错误");
        }
        SubmitInfo submitinfo = this.getById(approvalDTO.getInfoId());
        if (null == submitinfo) {
            throw new CustomException("报送信息为空");
        }
        Task task = validFlowStep(approvalDTO, processResult, submitinfo);
        boolean logg = false;

        SubmitProcess submitProcess = new SubmitProcess();
        submitProcess.setProcessResult(Integer.parseInt(processResult));
        submitProcess.setActName(PROCESS_ACT_MAPS.get(processResult));
        submitProcess.setScopeType(1);
        String comment = approvalDTO.getComment();
        if (StringUtils.isNotEmpty(comment)) {
            submitProcess.setComment("添加评论：" + comment);
        }
        if ("1".equals(processResult)) {
            submitInfoService.noProcessSubmit(submitinfo, task, submitProcess);
            logg = true;
        } else if ("2".equals(processResult)) {
            List<SubmitFileRelation> files = approvalDTO.getFiles();
            submitInfoService.applySupply(submitinfo, task, files, submitProcess);
            logg = true;
        } else if ("3".equals(processResult)) {
            submitInfoService.directProcess(submitinfo, task);
            logg = true;
        } else if ("4".equals(processResult)) {
            submitInfoService.upSubmit(approvalDTO, submitinfo, task, submitProcess);
            logg = true;
        }
        //管理员转派和处置者转派是一样的
        else if ("5".equals(processResult) || "6".equals(processResult)) {
            if ("5".equals(processResult)) {
                submitInfoService.forwardSubmit(approvalDTO, submitinfo, task, submitProcess);
            } else {
                submitInfoService.distributeSubmit(approvalDTO, submitinfo, task, submitProcess);
            }
            //处置人 多个
            String dispenserIds = approvalDTO.getDispenserId();
            Long[] dispenserIdd = getDispenserIds(dispenserIds);
            for (Long disposer : dispenserIdd) {
                SysUser sysUser = userService.selectUserById(disposer);
                if (null != sysUser && StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
                    SMSUtils.sendUndo(Collections.singletonList(sysUser.getPhonenumber()));
                }
            }
            logg = true;
        } else if ("7".equals(processResult)) {
            //处置
            submitInfoService.processSubmit(approvalDTO, submitinfo, task, submitProcess);
            logg = true;
        } else if ("8".equals(processResult)) {
            //完结(管理者对处置人员进行结单)   1.流程结束   2.更新所有干系人状态为未采纳  3.更新流程日志，添加评论,附件
            submitInfoService.finishSubmit(submitinfo, task, submitProcess);
            logg = true;
        }
        if (logg) {
            //处置人 多个
            String dispenserIds = approvalDTO.getDispenserId();
            submitProcess.setManagerUser(approvalDTO.getManagerUser());
            submitProcess.setDispenserId(dispenserIds);
            submitInfoService.recordFlow(approvalDTO.getInfoId(), submitProcess.getActName(), submitProcess, submitProcess.getScopeType());
            List<SubmitFileRelation> files = approvalDTO.getFiles();
            submitInfoService.addAttachmentFiles(files, submitinfo.getId(), submitProcess.getId());
        }
        return true;
    }

    /**
     * 校验工作流节点是否被允许
     *
     * @return org.activiti.engine.task.Task
     * <AUTHOR>
     * @date 2024/1/30 14:19
     **/
    private Task validFlowStep(ApprovalDTO approvalDTO, String processResult, SubmitInfo submitinfo) {
        Long manager = approvalDTO.getManagerUser();
        String dispenserId = approvalDTO.getDispenserId();
        if ((null != manager && manager.equals(SecurityUtils.getUserId())) || (null != dispenserId && joinContains(dispenserId, SecurityUtils.getUserId() + ""))) {
            throw new CustomException("接收人不能含有自己！");
        }
        //按钮事件：1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结     重新处置不是新的节点，而是直接变为转派
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(submitinfo.getId()).taskInvolvedUser(SecurityUtils.getUserIdStr()).singleResult();
        if (null == task) {
            throw new CustomException("流程节点非当前人执行");
        }
        String taskDefinitionKey = task.getTaskDefinitionKey();
        if ("processor".equals(taskDefinitionKey)) {
            //当前处于处置者
            if (!StringUtils.equalsAny(processResult, "6", "7")) {
                throw new CustomException("该流程不允许此操作");
            }
        } else if ("approval".equals(taskDefinitionKey)) {
            //当前处于审核者
            if (!StringUtils.equalsAny(processResult, "1", "2", "3", "4", "5", "8")) {
                throw new CustomException("该流程不允许此操作");
            }
        }
        return task;
    }

    /**
     * 无需处置
     *
     * @param submitinfo 报送信息
     * @param task       任务节点
     * <AUTHOR>
     * @date 2024/1/30 14:19
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void noProcessSubmit(SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        boolean fxsSubmit = submitinfo.getSubmitType() == 0;
        //无需处置-最早叫"完结"   1.流程结束   2.更新所有干系人状态为未采纳  3.更新流程日志，添加评论,附件
        HashMap<String, Object> map = new HashMap<>();
        map.put("approval_opinion", "ok");
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "选择无需处理，流程结束");
        taskService.setVariable(task.getId(), "noProcess", SecurityUtils.getUserIdStr());
        taskService.complete(task.getId(), map);
        submitInfoService.updateState(submitinfo.getId(), fxsSubmit ? 2 : null, 4, null);
        //如果是无需处置，分析师看见的为未采纳
        SubmitProcess submitProcess1 = new SubmitProcess();
        submitProcess1.setActName("未采纳");
        recordFlow(submitinfo.getId(), submitProcess1.getActName(), submitProcess1, 0);
        //添加完状态后需要恢复管理者可见
        submitProcess.setScopeType(1);
    }

    /**
     * 发起补充材料流程
     *
     * <AUTHOR>
     * @date 2024/1/30 14:19
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void applySupply(SubmitInfo submitinfo, Task task, List<SubmitFileRelation> files, SubmitProcess submitProcess) {
        if (1 == submitinfo.getSubmitType()) {
            throw new CustomException("该报送是管理员下发，没有补充材料操作");
        }
        boolean fxsSubmit = submitinfo.getSubmitType() == 0;
        //管理者发起补充材料流程   1.流程结束   2.更新所有干系人状态为未采纳  3.更新流程日志，添加评论,附件
        HashMap<String, Object> map = new HashMap<>();
        map.put("approval_opinion", "provide");
        map.put("applicat", submitinfo.getCreateById() + "");
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "打回补充材料");
        taskService.setVariable(task.getId(), "applySupply", SecurityUtils.getUserIdStr());
        taskService.complete(task.getId(), map);
        submitInfoService.updateState(submitinfo.getId(), fxsSubmit ? 1 : null, 0, NO_BUSINESS_CODE);
        //如果是补充材料，分析师也能看见
        recordFlow(submitinfo.getId(), submitProcess.getActName(), submitProcess, 0);
        //添加完状态后需要恢复管理者可见
        submitProcess.setScopeType(1);
        submitInfoService.addAttachmentFiles(files, submitinfo.getId(), submitProcess.getId());

        SysUser sysUser = userService.selectUserById(submitinfo.getCreateById());
        if (null != sysUser && StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
            SMSUtils.sendEdit(Collections.singletonList(sysUser.getPhonenumber()));
        }
    }

    /**
     * 直接处置
     *
     * <AUTHOR>
     * @date 2024/1/30 14:20
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void directProcess(SubmitInfo submitinfo, Task task) {
        boolean fxsSubmit = submitinfo.getSubmitType() == 0;
        //直接处置   管理者对分析师的完结  最早叫"直接处置"  1.流程结束  2.更新分析师状态为已采纳  3.更新流程日志，添加评论,附件  4.更新其他相关人节点状态
        HashMap<String, Object> map = new HashMap<>();
        map.put("approval_opinion", "ok");
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "选择处置，流程结束");
        taskService.setVariable(task.getId(), "directProcess", SecurityUtils.getUserIdStr());
        taskService.complete(task.getId(), map);
        submitInfoService.updateState(submitinfo.getId(), fxsSubmit ? 3 : null, 4, null);
        //直接处置需要记录已采纳
        SubmitProcess submitProcess1 = new SubmitProcess();
        submitProcess1.setActName("已采纳");
        recordFlow(submitinfo.getId(), submitProcess1.getActName(), submitProcess1, 0);
    }

    /**
     * 审核上报
     *
     * <AUTHOR>
     * @date 2024/1/30 14:20
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void upSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        boolean fxsSubmit = submitinfo.getSubmitType() == 0;
        //设置当前委托人，后续可以继续查看该任务，否则无法查看
        taskService.setOwner(task.getId(), SecurityUtils.getUserId() + "");
        //审核上报   1.流程的assigneer变更为新的   3.更新流程日志，添加评论,附件  4.更新其他相关人节点状态
        taskService.setAssignee(task.getId(), approvalDTO.getManagerUser() + "");
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "更换处理人审核，流程结束");
        //同一个流程只会保留最后一个同名的
        taskService.setVariable(task.getId(), "upSubmit", approvalDTO.getManagerUser() + "");
        //审核上报-分析师处于待审核状态，只有其中一个管理人员分发或者直接处置了才是已采纳
        submitInfoService.updateState(submitinfo.getId(), fxsSubmit ? 0 : null, 0, null);
        SysUser sysUser = userService.selectUserById(approvalDTO.getManagerUser());
        if (null == sysUser || sysUser.getUserId() == null) {
            throw new CustomException("获取处理用户信息为空!");
        }
        submitProcess.setDescription("上报至：" + sysUser.getNickName());
        if (StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
            SMSUtils.sendApproval(Collections.singletonList(sysUser.getPhonenumber()));
        }
    }

    /***
     *分发
     * <AUTHOR>
     * @date 2024/1/30 14:20
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void forwardSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        boolean fxsSubmit = submitinfo.getSubmitType() == 0;
        //处置人 多个
        String dispenserIds = approvalDTO.getDispenserId();
        //5是分发  6是转派            管理者分发是走流程    处置人转派是重新指定人。
        String dispenserNames = getDispenserName(dispenserIds);
        Long[] dispenserIdd = getDispenserIds(dispenserIds);
        //分发
        String deadline = approvalDTO.getDeadlineNum();
        DateTime deadTime = null;
        if (null != deadline && !"暂无".equals(deadline)) {
            deadTime = getDeadTime(deadline);
        }
        if (null == submitinfo.getDeadlineNum()) {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(submitinfo.getId()).singleResult();
            if (processInstance == null) {
                log.info("{}，流程已结束", submitinfo.getId());
                throw new CustomException("报送流程已结束");
            }
            //如果是第一次选择截至日期就更新，后面其他管理者再选择就不给更新截至日期了
            UpdateWrapper<SubmitInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("deadline", deadTime);
            updateWrapper.set("deadline_num", deadline);
            updateWrapper.eq("id", submitinfo.getId());
            this.update(updateWrapper);
            submitinfo.setDeadline(deadTime);
            // 如果存在截止时间，需要创建挂起任务
            suspendProcess(processInstance.getProcessInstanceId(), submitinfo);
        }
        submitProcess.setDescription("分发至：" + dispenserNames);
        //转派  1.流程节点到处置人  3.更新流程日志，添加评论,附件   4.更新其他相关人节点状态
        HashMap<String, Object> map = new HashMap<>();
        map.put("approval_opinion", "distribute");
        map.put("processor", dispenserIds);
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "转派处理");
        taskService.setVariable(task.getId(), "fenfa", "," + dispenserIds + ",");
        taskService.setVariable(task.getId(), "fenfaUser", SecurityUtils.getUserId() + "");
        taskService.complete(task.getId(), map);
        //管理者分发更新状态   管理者为待处理  处置者为待处理
        submitInfoService.updateState(submitinfo.getId(), fxsSubmit ? 3 : null, 1, 1);

        //分发需要记录已采纳
        Integer fxsState = submitinfo.getFxsState();
        if (null != fxsState && fxsState != 3) {
            SubmitProcess submitProcess1 = new SubmitProcess();
            submitProcess1.setActName("已采纳");
            recordFlow(submitinfo.getId(), submitProcess1.getActName(), submitProcess1, 0);
        }
        for (Long aLong : dispenserIdd) {
            saveFlow(submitinfo.getId(), "distribute", aLong, "0");
        }
    }

    /**
     * 转派
     *
     * <AUTHOR>
     * @date 2024/1/30 14:20
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void distributeSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        //处置人 多个
        String dispenserIds = approvalDTO.getDispenserId();
        //5是分发  6是转派            管理者分发是走流程    处置人转派是重新指定人。
        String dispenserNames = getDispenserName(dispenserIds);
        Long[] dispenserIdd = getDispenserIds(dispenserIds);
        for (Long aLong : dispenserIdd) {
            saveFlow(submitinfo.getId(), "forward", aLong, "0");
        }
        submitProcess.setDescription("转派至：" + dispenserNames);
        //设置当前委托人，后续可以继续查看该任务，否则无法查看
        taskService.setOwner(task.getId(), SecurityUtils.getUserId() + "");
        //TODO 因为是可选多个处置人，此处有业务bug，指派多个人叫归还组任务，需要先清空原先人，会导致原先人看不到这单   SETOnwen可以解决
        taskService.setAssignee(task.getId(), null);
        for (String s : dispenserIds.split(",")) {
            taskService.addCandidateUser(task.getId(), s);
        }
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "转派其他人员处理" + approvalDTO.getDispenserId());
        taskService.setVariable(task.getId(), "zhuanpai", "," + dispenserIds + ",");
        taskService.setVariable(task.getId(), "zhuanpaiUser", SecurityUtils.getUserId() + "");
        //处置者转派人员转给自己   分析师状态无需更新状态传-2   处置者改为转派 TODO 此处有bug，要改为我看到的是我转发的
        submitInfoService.updateState(submitinfo.getId(), NO_BUSINESS_CODE, 1, 3);
    }

    /**
     * 处置者处置
     *
     * <AUTHOR>
     * @date 2024/1/30 14:21
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void processSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        //如果是处置者，需要先签收，不然activiti会报错
        Task task1 = taskService.createTaskQuery().processInstanceBusinessKey(submitinfo.getId()).taskInvolvedUser(SecurityUtils.getUserIdStr()).singleResult();
        claim(SecurityUtils.getUserIdStr(), task1);

        String managerId = findLastMnager(approvalDTO.getManagerUser(), task);
        HashMap<String, Object> map = new HashMap<>();
        map.put("manager_user", managerId);
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "处置者处置，任务回到管理者审核确认");
        taskService.setVariable(task.getId(), "process", SecurityUtils.getUserIdStr());
        taskService.complete(task.getId(), map);
        submitInfoService.updateState(submitinfo.getId(), NO_BUSINESS_CODE, 2, 2);
        submitProcess.setDescription("已处理");
        //需要把之前的转派和分发的小流程（为了解决已转派和待处理的切换逻辑）设置为历史状态，避免下次轮回转派和处置有歧义
        UpdateWrapper<SubmitFlow> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("info_id", submitinfo.getId());
        updateWrapper.set("history", "1");
        flowService.update(updateWrapper);
    }

    /**
     * 管理者完结
     *
     * <AUTHOR>
     * @date 2024/1/30 14:21
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void finishSubmit(SubmitInfo submitinfo, Task task, SubmitProcess submitProcess) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("approval_opinion", "ok");
        taskService.addComment(task.getId(), task.getProcessInstanceId(), "对处置人员进行结单，流程结束");
        taskService.setVariable(task.getId(), "finish", SecurityUtils.getUserIdStr());
        taskService.complete(task.getId(), map);
        submitInfoService.updateState(submitinfo.getId(), NO_BUSINESS_CODE, 4, 5);
        submitProcess.setDescription("已完结");
    }

    /**
     * 保存分发和转派的人员映射关系----解决处置者的待处置和已转派的映射关系
     *
     * <AUTHOR>
     * @date 2024/1/30 14:21
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveFlow(String infoId, String actName, Long receiverUserId, String status) {
        SubmitFlow entity = new SubmitFlow();
        entity.setId(IdUtil.getSnowflakeNextIdStr());
        entity.setCreateTime(new Date());
        entity.setInfoId(infoId);
        entity.setActName(actName);
        entity.setForwardId(SecurityUtils.getUserIdStr());
        entity.setReceiverId(receiverUserId + "");
        entity.setHistory(status);
        flowService.save(entity);
    }

    private Long[] getDispenserIds(String dispenserIds) {
        List<Long> ids = new ArrayList<Long>();
        for (String s : dispenserIds.split(",")) {
            ids.add(Long.parseLong(s));
        }
        return ids.toArray(new Long[0]);
    }

    private void claim(String userId, Task task) {
        if (null != task) {
            try {
                taskService.claim(task.getId(), userId);
            } catch (Exception e) {
                throw new CustomException("该流程已被其他人员签收");
            }
        }
    }

    private String getDispenserName(String dispenserIds) {
        StringBuilder dispenserNames = new StringBuilder();
        for (String userId : dispenserIds.split(",")) {
            SysUser sysUser = userService.selectUserById(Long.parseLong(userId));
            dispenserNames.append(sysUser.getNickName()).append(",");
        }
        if (dispenserNames.length() <= 1) {
            throw new CustomException("所选人员信息异常，请选择其他人员!");
        }
        return StrUtil.removeSuffix(dispenserNames, ",");
    }

    /**
     * 经过测试，同一个变量，在同一个流程名称会被后一个覆盖  2024-1-19
     *
     * @param managerUser 管理者
     * @param task        activiti任务对象
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/19 14:39
     **/
    private String findLastMnager(Long managerUser, Task task) {
        // 1.找到最后一次处理的管理员，因为有可能被上报过，真找不到我就取第一次处理的管理员  2.让任务回到管理者继续审核  3.记录流程日志和附件
        List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery().processInstanceId(task.getProcessInstanceId()).list();
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(HistoricVariableInstance::getVariableName, HistoricVariableInstance::getValue));
        String userId = managerUser == null ? null : managerUser.toString();
        if (collect.containsKey("upSubmit")) {
            userId = collect.get("upSubmit").toString();
        } else if (collect.containsKey("manager_user")) {
            userId = collect.get("manager_user").toString();
        }
        if (StringUtils.isEmpty(userId)) {
            throw new CustomException("所选人员信息异常!");
        }
        return userId;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void recordFlow(String infoId, String actName, SubmitProcess process, Integer scopeType) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        process.setCreateBy(user.getNickName());
        process.setCreateTime(new Date());
        process.setId(IdUtil.getSnowflakeNextIdStr());
        process.setActName(actName);
        process.setInfoId(infoId);
        process.setCreateById(user.getUserId());
        process.setScopeType(scopeType);
        submitProcessService.save(process);
    }

    @Override
    public List<SubmitInfo> taskNum(SubmitInfoDTO submitInfo, Long userId) {
        submitInfo.setAll(1);
        submitInfo.setOwner(1);
        submitInfo.setCreateBy(userId.toString());
        List<SubmitInfo> submitInfos = baseMapper.selectSubmitInfoAll(submitInfo);
        return submitInfos;
    }

    public boolean joinContains(String str, String containStr) {
        for (String s : str.split(",")) {
            if (s.equals(containStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 修改信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSubmitInfo(SubmitInfoAddDTO submitInfo) {
        String infoId;
        if (StringUtils.isEmpty(infoId = submitInfo.getId())) {
            throw new IllegalArgumentException("参数异常！");
        }
        submitInfo.setArticleAreaName(areaUtil.getAreaNames(submitInfo.getArticleArea()));
        SubmitInfo info = this.getById(infoId);
        submitInfo.setDeadline(info.getDeadline());
        submitInfo.setDeadlineNum(info.getDeadlineNum());
        submitInfo.setManagerUser(info.getManagerUser());
        //后期不给修改处置人这些,截止时间
//        setDeadLine(submitInfo);
        submitInfo.setUpdateTime(DateUtils.getNowDate());
        submitInfo.setUpdateBy(SecurityUtils.getUsername());
        LambdaQueryWrapper<SubmitInfo> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(SubmitInfo::getId, infoId);
        int update = baseMapper.updateById(submitInfo);

        int fxsState = null == info.getFxsState() ? -1 : info.getFxsState();
        int glState = null == info.getGlState() ? -1 : info.getGlState();
        int type = validPermission();
        if (type == 0 || type == 1) {
            //如果是分析师或者管理者来修改
            if (fxsState == 1 || fxsState == 0 || glState == 0 || glState == 1) {
                List<SubmitFileRelation> addFiles = new ArrayList<>();
                List<String> delFiles = new ArrayList<>();
                updateAttachment(submitInfo, null, addFiles, delFiles);
                //如过当前状态是待修改或者是待处理
                if (type == 0 && fxsState == 1) {
                    //完结补充材料这一步     说明这一单刚被打回补充材料，接下来需要提交流程记录为【补充材料】
                    Task task = taskService.createTaskQuery().processInstanceBusinessKey(infoId).taskAssignee(SecurityUtils.getUserId() + "").singleResult();

                    if (task != null) {
                        //考虑一种情况  如果之前审核者上报给其他处置者处置后，补充材料就是最后一次的管理者处理上报情况
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("manager_user", findLastMnager(info.getManagerUser(), task));
                        taskService.complete(task.getId(), map);
                    }
                    //只有分析师才有补充材料这一步
                    SubmitProcess process = new SubmitProcess();
                    recordFlow(infoId, SubmitConstant.FLOW_ACT_SUPPLY, process, SCOPE_FXS);
                    addRelaFiles(addFiles, info.getId(), process.getId());

                    SubmitProcess process1 = new SubmitProcess();
                    recordFlow(infoId, SubmitConstant.FLOW_ACT_SUPPLY, process1, SCOPE_SHARE);
                    addRelaFiles(addFiles, info.getId(), process1.getId());
                    //更新业务状态
                    updateState(infoId, 0, 0, NO_BUSINESS_CODE);
                } else {
                    //如果是分析师为待审核，管理者为待审核和待处理是可以修改的
                    if (type == 0) {
                        SubmitProcess process2 = new SubmitProcess();
                        recordFlow(infoId, FLOW_ACT_MODITY, process2, SCOPE_FXS);
                    }
                    SubmitProcess process3 = new SubmitProcess();
                    recordFlow(infoId, FLOW_ACT_MODITY, process3, SCOPE_SHARE);
                }
                //新上传的   新提交的files在历史文件中不存在
                if (addFiles.size() > 0) {
                    submitFileRelationService.saveBatch(addFiles);
                }
                //删除旧的 旧的文件在新的中不存在
                if (delFiles.size() > 0) {
                    submitFileRelationService.deleteSubmitFileRelationByFileIds(delFiles.toArray(new String[0]));
                }
            }
        } else {
            throw new IllegalArgumentException("该操作非当前人员处理情况");
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateAttachment(SubmitInfoAddDTO submitInfo, String processId, List<SubmitFileRelation> addFiles, List<String> delFiles) {
//        List<String> fileIds = new ArrayList<>();
        List<SFile> filesList = submitFileRelationService.getFilesList(submitInfo.getId());
        List<SubmitFileRelation> oldFiles = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(filesList)) {
//            fileIds = filesList.stream().map(s -> s.getId()).collect(Collectors.toList());
            oldFiles = filesList.stream().map(s -> new SubmitFileRelation(s.getId())).collect(Collectors.toList());

        }
        //历史已存在的文件
        List<SubmitFileRelation> files1 = submitInfo.getFiles();
//        Map<String, String> existsFileMaps = new HashMap<>();
//
//        if (CollectionUtil.isNotEmpty(files1)) {
//            for (SubmitFileRelation file : files1) {
//                if (fileIds.contains(file.getFileId())) {
//                    existsFileMaps.put(file.getFileId(), file.getFileId());
//                }
//            }
//            for (SubmitFileRelation file : files1) {
//                //处理新加的文件
//                if (!existsFileMaps.containsKey(file.getFileId())) {
//                    if (file.getFileId() == null || StringUtils.isEmpty(file.getFileId())) {
//                        continue;
//                    }
//                    SubmitFileRelation ff = JSONObject.parseObject(JSONObject.toJSONString(file), SubmitFileRelation.class);
//                    ff.setId(IdUtil.getSnowflakeNextIdStr());
//                    ff.setInfoId(submitInfo.getId());
//                    //第一次附件上次上传不需要记录流程id
//                    ff.setProcessId(processId);
//                    addFiles.add(ff);
//                }
//            }
//        }
//        List<String> collect = files1.stream().map(s -> s.getFileId()).collect(Collectors.toList());
//        for (String submitFileRelation : fileIds) {
//            if (!collect.contains(submitFileRelation)) {
//                //新文件列表中不包含以前的文件，需要删除以前的
//                delFiles.add(submitFileRelation);
//            }
//
//        }

        // 计算新增的元素   新的删除旧的就是新增的
        List<SubmitFileRelation> addedElements = new ArrayList<>(files1);
        addedElements.removeAll(oldFiles);
        for (SubmitFileRelation addedElement : addedElements) {
            addedElement.setInfoId(submitInfo.getId());
            addedElement.setProcessId(processId);
            addedElement.setId(IdUtil.getSnowflakeNextIdStr());
        }

        addFiles.clear();
        addFiles.addAll(addedElements);
        // 计算删除的元素    旧的删除新的就是多出来的
        List<SubmitFileRelation> removedElements = new ArrayList<>(oldFiles);
        removedElements.removeAll(files1);

        delFiles.clear();
        ;
        List<String> collect1 = removedElements.stream().map(SubmitFileRelation::getFileId).collect(Collectors.toList());
        delFiles.addAll(collect1);

    }

    /**
     * 更新关联的文件信息
     *
     * @param files 报送对象
     * <AUTHOR>
     * @date 2024/1/9 16:08
     **/
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addRelaFiles(List<SubmitFileRelation> files, String infoId, String processId) {
        List<SubmitFileRelation> newFiles = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(files)) {
            for (SubmitFileRelation file : files) {
                SubmitFileRelation ff = JSONObject.parseObject(JSONObject.toJSONString(file), SubmitFileRelation.class);
                if (file.getFileId() == null || StringUtils.isEmpty(file.getFileId())) {
                    continue;
                }
                ff.setId(IdUtil.getSnowflakeNextIdStr());
                ff.setInfoId(infoId);
                //第一次附件上次上传不需要记录流程id
                ff.setProcessId(processId);
                newFiles.add(ff);
            }
            submitFileRelationService.saveBatch(newFiles);
        }
    }

    /**
     * 删除基础附件(报送主体附件)
     *
     * <AUTHOR>
     * @date 2024/1/16 9:56
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteRelaFiles(SubmitInfoAddDTO submitInfo) {
        LambdaQueryWrapper<SubmitFileRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubmitFileRelation::getInfoId, submitInfo.getId());
        //基础附件的processId是空的，防止误删
        queryWrapper.isNull(SubmitFileRelation::getProcessId);
        List<SubmitFileRelation> list = submitFileRelationService.list(queryWrapper);
        //先刪除之前的附件信息
        String[] ids = list.stream().map(SubmitFileRelation::getId).toArray(String[]::new);
        if (ArrayUtil.isNotEmpty(ids)) {
            submitFileRelationService.deleteSubmitFileRelationByIds(ids);
        }
    }

    @Override
    public List<TreeSelect> loadManagerDept() {
        //1.获取当前管理人员id  2.获取所有管理人员的大部门集合  3.根据部门集合加载下面的管理人员列表
        List<SysDept> depts = deptService.getRootDeptsByRole(SubmitUserTypeEnum.GUANLI.getName(), null);
        return deptService.getDeptTree(depts);
    }

    @Override
    public void chaoshi(String id, String deadline, String deadlineNum) {
        //如果是第一次选择截至日期就更新，后面其他管理者再选择就不给更新截至日期了
        UpdateWrapper<SubmitInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("deadline", deadline);
        updateWrapper.set("deadline_num", deadlineNum);
        updateWrapper.eq("id", id);
        this.update(updateWrapper);
        SubmitInfo byId = submitInfoService.getById(id);
        // 如果存在截止时间，需要创建挂起任务
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(id).orderByTaskCreateTime().desc().singleResult();
        suspendProcess(task.getProcessInstanceId(), byId);
    }

    /**
     * 批量删除信息报送
     *
     * @param ids 需要删除的信息报送ID
     * @return 结果
     */
    @Override
    public int deleteSubmitInfoByIds(Long[] ids) {
        return baseMapper.deleteSubmitInfoByIds(ids);
    }

    /**
     * 删除信息报送信息
     *
     * @param id 信息报送ID
     * @return 结果
     */
    @Override
    public int deleteSubmitInfoById(Long id) {
        return baseMapper.deleteSubmitInfoById(id);
    }

    @Override
    public List<TreeSelect> loadProcessor(SubmitInfo submitInfo) {
        int i = validPermission();
        if (i == 0 || i == -1) {
            return new ArrayList<>();
        }
        SysDept dept = new SysDept();
        if (i == 1) {
            //如果是审核者 那么就读取该审核者的下级部门(处置者部门)及处置人员列表
            dept.setParentId(SecurityUtils.getLoginUser().getUser().getDeptId());
        } else if (i == 2) {
            //如果是处置人员 那么就读取该处置者所在公司的部门(处置者部门)及以下处置人员列表
            dept.setParentId(SecurityUtils.getLoginUser().getUser().getDept().getParentId());
        }
        Long[] longs = {roleService.selectRoles().stream().filter(role -> SubmitUserTypeEnum.CHUZHIZHE.getName().equals(role.getRoleKey())).findFirst().get().getRoleId()};
        dept.setRoleIds(longs);
        List<SysDept> depts = deptService.selectDeptLists(dept);
        List<TreeSelect> deptTree = deptService.getDeptTree(depts);
        SysUser sysUser = new SysUser();
        sysUser.setRoleIds(longs);
        deptService.addPeopleByRole(deptTree, sysUser);
        return deptTree;
    }

    @Override
    public List<LinkedHashMap<String, String>> loadSubmitUsers() {
        int i = validPermission();
        if (i == 0) {
            //分析师只看管理者    管理者
            Long parentId = SecurityUtils.getLoginUser().getUser().getRelaDeptId();
            if (null == parentId || parentId == 0) {
                return new ArrayList<>();
            }
            return baseMapper.loadManagerUsers(parentId);
        } else if (i == 2) {
            //处置者
            Long parentId = SecurityUtils.getLoginUser().getUser().getDept().getParentId();
            return baseMapper.loadManagerUsers(parentId);
        } else if (i == 1) {
            //管理者查看报送人    （管理者和分析师）
            Long parentId = SecurityUtils.getLoginUser().getUser().getDept().getDeptId();
            return baseMapper.loadSubmitUsers(parentId, SecurityUtils.getUserIdL());
        }
        //管理者和处置者看所有
        return new ArrayList<>();
    }

    @Override
    public AjaxResult export(SubmitInfoDTO submitInfo) {
        List<SubmitInfo> list = this.selectSubmitInfoList(submitInfo);
        if (null != submitInfo.getIds() && submitInfo.getIds().length > 0) {
            list = list.stream().filter(s -> Arrays.asList(submitInfo.getIds()).contains(s.getId())).collect(Collectors.toList());
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        int i = validPermission();
        AjaxResult result = new AjaxResult();
        if (i == 0 || currentUser.isAdmin()) {
            ExcelUtil<SubmitInfoFxsVo> util = new ExcelUtil<>(SubmitInfoFxsVo.class);
            result = util.exportExcel(JSONObject.parseArray(JSONObject.toJSONString(list), SubmitInfoFxsVo.class), "报送列表");
        } else if (i == 1) {
            ExcelUtil<SubmitInfo> util = new ExcelUtil<SubmitInfo>(SubmitInfo.class);
            result = util.exportExcel(list, "报送列表");
        } else if (i == 2) {
            ExcelUtil<SubmitInfoCzzVo> util = new ExcelUtil<>(SubmitInfoCzzVo.class);
            result = util.exportExcel(JSONObject.parseArray(JSONObject.toJSONString(list), SubmitInfoCzzVo.class), "报送列表");
        }
        return result;
    }

    /**
     * 更新处置状态，如果是-2或者传null就不更新对应字段
     *
     * <AUTHOR>
     * @date 2024/1/26 15:46
     **/
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateState(String id, Integer fxsState, Integer glState, Integer czState) {
        UpdateWrapper<SubmitInfo> updateWrapper = new UpdateWrapper<>();
        if (null == fxsState || fxsState != NO_BUSINESS_CODE) {
            //-1全部 0待审核 1.待修改 2 未采纳 3已采纳
            updateWrapper.set("fxs_state", fxsState);
        }
        if (null == glState || glState != NO_BUSINESS_CODE) {
            //-1全部 0待审核 1 待处理 2已处理3 未处理  4已完结
            updateWrapper.set("gl_state", glState);
        }
        if (null == czState || czState != NO_BUSINESS_CODE) {
            //-1全部 0无需处理 1待处理 2已处理 3未处理 4已完结
            updateWrapper.set("cz_state", czState);
        }
        updateWrapper.eq("id", id);
        boolean updateFlag = this.update(updateWrapper);
        log.info("更新分析师{}，分析师状态{},管理者状态:{},处置者状态:{}", updateFlag, fxsState, glState, czState);
    }


    // 挂起任务
    private void suspendProcess(String instanceId, SubmitInfo info) {
        if (StringUtils.isEmpty(instanceId)) {
            return;
        }
        Date deadline = info.getDeadline();
        if (ObjectUtil.isNotEmpty(deadline)) {
            // 如果已经存在该流程的截止时间，取消之前的截止时间任务
            if (SCHEDULE_MAP.containsKey(instanceId)) {
                SCHEDULE_MAP.get(instanceId).cancel(true);
                SCHEDULE_MAP.remove(instanceId);
                log.info("instanceId {} 挂起流程时间修改至{}", instanceId, deadline);
            } else {
                log.info("instanceId {} 新增挂起流程时间{}", instanceId, deadline);
            }
            ExpireTaskInit.suspendProcess(instanceId, info);
            redisTemplate.opsForHash().put(Constants.EXPIRE_TASK_KEY, instanceId, info.getId());
        }
    }
}
