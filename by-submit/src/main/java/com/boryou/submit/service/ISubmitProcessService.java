package com.boryou.submit.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.submit.domain.SubmitProcess;
import com.boryou.submit.dto.SubmitProcessDTO;

import java.util.List;

/**
 * 报送节点过程记录Service接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface ISubmitProcessService extends IService<SubmitProcess> {
    /**
     * 查询报送节点过程记录
     *
     * @param id 报送节点过程记录ID
     * @return 报送节点过程记录
     */
    public SubmitProcess selectSubmitProcessById(Long id);

    /**
     * 查询报送节点过程记录列表
     *
     * @param submitProcess 报送节点过程记录
     * @return 报送节点过程记录集合
     */
    public List<SubmitProcessDTO> selectSubmitProcessList(SubmitProcessDTO submitProcess);


    /**
     * 新增报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    public int insertSubmitProcess(SubmitProcess submitProcess);

    /**
     * 修改报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    public int updateSubmitProcess(SubmitProcess submitProcess);

    /**
     * 批量删除报送节点过程记录
     *
     * @param ids 需要删除的报送节点过程记录ID
     * @return 结果
     */
    public int deleteSubmitProcessByIds(Long[] ids);

    /**
     * 删除报送节点过程记录信息
     *
     * @param id 报送节点过程记录ID
     * @return 结果
     */
    public int deleteSubmitProcessById(Long id);
}
