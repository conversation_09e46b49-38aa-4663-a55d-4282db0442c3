package com.boryou.submit.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.submit.domain.SubmitFileRelation;
import com.boryou.submit.domain.SubmitInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-29 14:39
 */
@Data
public class SubmitInfoAddDTO extends SubmitInfo {

    private List<SubmitFileRelation> files;
    // 加密密文
    @TableField(exist = false)
    private String encryptJson;
}

