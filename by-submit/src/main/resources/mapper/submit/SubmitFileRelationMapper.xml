<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.submit.mapper.SubmitFileRelationMapper">

    <resultMap type="SubmitFileRelation" id="SubmitFileRelationResult">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="processId" column="process_id"/>
        <result property="fileId" column="file_id"/>
    </resultMap>

    <sql id="selectSubmitFileRelationVo">
        select id, info_id, process_id, file_id from by_submit_file_relation
    </sql>

    <select id="selectSubmitFileRelationList" parameterType="SubmitFileRelation" resultMap="SubmitFileRelationResult">
        <include refid="selectSubmitFileRelationVo"/>
        <where>
            <if test="infoId != null ">and info_id = #{infoId}</if>
            <if test="processId != null ">and process_id = #{processId}</if>
            <if test="fileId != null  and fileId != ''">and file_id = #{fileId}</if>
        </where>
    </select>

    <select id="selectSubmitFileRelationById" parameterType="Long" resultMap="SubmitFileRelationResult">
        <include refid="selectSubmitFileRelationVo"/>
        where id = #{id}
    </select>


    <delete id="deleteSubmitFileRelationById" parameterType="Long">
        delete from by_submit_file_relation where id = #{id}
    </delete>

    <delete id="deleteSubmitFileRelationByIds" parameterType="String">
        delete from by_submit_file_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
