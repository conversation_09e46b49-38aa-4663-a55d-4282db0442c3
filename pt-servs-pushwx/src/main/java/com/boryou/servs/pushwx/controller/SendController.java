package com.boryou.servs.pushwx.controller;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.boryou.servs.common.bean.Return;
import com.boryou.servs.pushwx.pojo.bo.SendBO;
import com.boryou.servs.pushwx.service.SendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信推送服务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechatpush")
public class SendController extends AheadController {

    @Autowired
    private SendService sendService;

    /**
     * 发送单个模板消息接口
     */
    @PostMapping("/send")
    public Return send(@RequestBody SendBO sendBO) {
        sendBO.setAppId(super.getAppId());
        sendBO.setAppSecret(super.getAppSecret());
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTplId(super.getTplId(sendBO.getType()));
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTenantFlag(super.getTenantFlag());
        sendBO.setBizFlag(super.getBizFlag());
        sendService.send(sendBO);
        return Return.ok();
    }

    /**
     * 批量发送模板消息接口
     */
    @PostMapping("/multiSend")
    public Return multiSend(@RequestBody SendBO sendBO) {
        sendBO.setAppId(super.getAppId());
        sendBO.setAppSecret(super.getAppSecret());
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTplId(super.getTplId(sendBO.getType()));
        sendBO.setProjFlag(super.getProjFlag());
        sendBO.setTenantFlag(super.getTenantFlag());
        sendBO.setBizFlag(super.getBizFlag());
        sendService.multiSend(sendBO);
        return Return.ok();
    }
}
