package com.boryou.servs.pushwx.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.boryou.servs.pushwx.service.QrCodeService;
import com.boryou.servs.pushwx.util.QRCodeUtils;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @description
 */
@Service
public class QrCodeServiceImpl implements QrCodeService {

    //微信网页授权链接头部
    private final String AUTH_URL_HEAD = "https://open.weixin.qq.com/connect/oauth2/authorize";
    //微信网页授权链接尾部
    private final String AUTH_URL_TAIL_1 = "&response_type=code&scope=snsapi_userinfo&state=";
    private final String AUTH_URL_TAIL_2 = "#wechat_redirect";

    @Override
    public String getQRCode(String appId, String redirectUrl, String data) throws Exception {
        if (StrUtil.isBlank(data)){
            data="STATE";
        }
        String redirect = AUTH_URL_HEAD + "?appid=" + appId + "&redirect_uri=" + URLEncoder.encode(redirectUrl) + AUTH_URL_TAIL_1+data+AUTH_URL_TAIL_2;
        BufferedImage image = QRCodeUtils.generateQRCode(redirect, 30);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", stream);
        String encode = Base64.encode(stream.toByteArray());
        return "data:image/png;base64," + encode;
    }
}
