package com.boryou.servs.pushwx.pojo.bo;


import com.boryou.servs.common.annotation.valid.PickNum;
import com.boryou.servs.common.validator.ValidGroup;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TemplateBO {
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 模板标识
     */
    private String tplSign;

    /**
     * 发送内容
     */
    private List<String> content;

    /**
     * 模板跳转链接
     */
    private String tplUrl;

    /**
     * 项目标记
     */
    private String projFlag;

    /**
     * 模板状态
     */
    @PickNum(groups = ValidGroup.ChangeStatus.class, value = {0, 1})
    private Integer tplStatus;

    /**
     * 备注
     */
    private String remark;

    public TemplateBO(String tplSign, String tplUrl,List<String> content) {
        this.tplSign = tplSign;
        this.tplUrl = tplUrl;
        this.content = content;
    }
}
