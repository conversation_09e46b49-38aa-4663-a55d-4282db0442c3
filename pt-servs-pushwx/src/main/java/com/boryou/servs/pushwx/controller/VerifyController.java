package com.boryou.servs.pushwx.controller;

import com.boryou.servs.pushwx.util.WeixinCheckoutUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 微信号公众号认证接口
 */
@RestController
@RequestMapping("/verify")
public class VerifyController {

    /**
     * 微信端请求验证接口
     * <p>
     * 原理逻辑：微信发送请求时会提供4个参数nonce、echostr、timestamp、signature，代码需要根据timestamp、nonce和代码提供的token进行散列解密(不可逆)，最后得出一个结果，与signature作对比，如果相同，直接将echostr返回给微信端即可验证完成
     */
    @RequestMapping("verify")
    public String verify(String signature, String timestamp, String nonce, String echostr) {
        // 通过检验signature对请求进行校验，若校验成功则原样返回echostr，表示接入成功，否则接入失败
        if (signature != null && WeixinCheckoutUtil.checkSignature(signature, timestamp, nonce)) {
            return echostr;
        }
        return null;
    }
}
