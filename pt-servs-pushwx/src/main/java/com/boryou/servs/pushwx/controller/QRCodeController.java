package com.boryou.servs.pushwx.controller;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.pushwx.service.QrCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 微信登录二维码
 */
@RestController
public class QRCodeController extends AheadController {
    @Autowired
    QrCodeService qrCodeService;

    /**
     * @description 获取微信登录二维码
     * <AUTHOR>
     */
    @GetMapping("getQRCode")
    public Return getQRCode(@RequestParam(value = "redirectUrl", required = true) String redirectUrl,@RequestParam(value = "data", required = false) String data) throws Exception {
        String qrCode = qrCodeService.getQRCode(getAppId(), redirectUrl,data);
        return Return.ok(qrCode);
    }

}
