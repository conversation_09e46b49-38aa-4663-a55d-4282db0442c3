package com.boryou.servs.pushwx.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.servs.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 手动获取tocken  tocken设置超时过期 2小时
 */

@Configuration
public class WXTokenUtil {
    @Autowired
    private static RedisUtil redisUtil;
    //舆情业务存入redis accesssTocken的key
    private static final String ACCESSTOKEN_REDISKEY = "pushwx:accesstoken_";

    //请求tocken地址 get
    private static final String ACCESSTOKEN_FRESHEURL = "https://api.weixin.qq.com/cgi-bin/token";

    public static String token(String appid, String secret) {
        String key = ACCESSTOKEN_REDISKEY + appid;
        //刚好赶在失效前获取到也没问题 有5分钟缓冲时间内新旧都可用
        String token = redisUtil.get(key);
        if (StrUtil.isBlank(token)) {
            String url = ACCESSTOKEN_FRESHEURL + "?grant_type=" + "client_credential" + "&appid=" + appid + "&secret=" + secret;
            String jsonStr = HttpUtil.get(url);
            JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
            if (StrUtil.isBlank(jsonObject.getStr("errcode"))) {
                token = jsonObject.getStr("access_token");
                redisUtil.set(key, token);
                //设置过期时间两小时
                redisUtil.expireAt(key, DateUtil.offsetHour(new Date(), 2));
            } else {
                throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "获取微信公众号token获取失败！");
            }
        }
        return token;
    }
}
