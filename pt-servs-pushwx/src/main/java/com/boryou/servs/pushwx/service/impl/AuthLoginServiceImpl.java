package com.boryou.servs.pushwx.service.impl;

import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.servs.pushwx.service.AuthLoginService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @description
 */
@Service
public class AuthLoginServiceImpl implements AuthLoginService {

    //   https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code  获取网页授权access_token链接

    //https://api.weixin.qq.com/sns/userinfo?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN 拉取用户信息链接


    //
    private final String PAGEAUTH_HEAD="https://api.weixin.qq.com/sns/oauth2/access_token";

    private final String GETUSERLOFO_HEAD="https://api.weixin.qq.com/sns/userinfo";


    @Override
    public String getUserInfo(String appId, String appSecret, String code) {
        //通过code换取网页授权access_token
        String url=PAGEAUTH_HEAD+"?appid="+appId+"&secret="+appSecret+"&code="+code+"&grant_type=authorization_code";
        String res = HttpUtil.get(url);
        JSONObject json = JSONUtil.parseObj(res);
        if (StrUtil.isNotEmpty(json.getStr("errcode"))) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "code换取access_token失败！");
        }
        String access_token = json.getStr("access_token");
        String openid = json.getStr("openid");

        //拉取用户信息
        String userinfo_url = GETUSERLOFO_HEAD+"?access_token="+access_token+"&openid="+openid+"&lang=zh_CN";
        String userinfo = HttpUtil.get(userinfo_url);
        JSONObject userinfojson = JSONUtil.parseObj(userinfo);
        if (StrUtil.isNotEmpty(userinfojson.getStr("errcode"))) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "用户详情获取失败！");
        }

        return userinfo;

    }
}
