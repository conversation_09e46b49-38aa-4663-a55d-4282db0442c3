package com.boryou.servs.pushwx.controller;

import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.boryou.servs.common.util.RedisUtil;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: Young
 */
public class AheadController {
    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 验证Authorization
     */
    private String verifyAuth() {
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isEmptyIfStr(authorization) || StrUtil.isEmptyIfStr(getTemplateData(authorization))) {
            throw new StatefulException(HttpStatus.HTTP_UNAUTHORIZED, "无权限");
        }
        return authorization;
    }


    /**
     * 获取模板ID（tplId）
     */
    protected Long getTplId(String type) {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        Object getTplId = templateData.get(type);
        if (getTplId == null) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不存在");
        } else {
            return Long.valueOf(getTplId.toString());
        }
    }

    /**
     * 获取租户标识（tenantFlag）
     */
    protected String getTenantFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("tenantFlag").toString();
    }


    /**
     * 获取项目标识（projFlag）
     */
    protected String getProjFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("projFlag").toString();
    }

    /**
     * 获取业务标识（bizFlag）
     */
    protected String getBizFlag() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("bizFlag").toString();
    }

    /**
     * 获取公众号id（appId）
     */
    protected String getAppId() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("appId").toString();
    }

    /**
     * 获取公众号密码（appSecret）
     */
    protected String getAppSecret() {
        String auth = verifyAuth();
        Map<String, Object> templateData = getTemplateData(auth);
        return templateData.get("appSecret").toString();
    }



    /**
     * 模拟token与短信数据对应关系
     */
    private static Map<String, Object> getTemplateData(String token) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        //客户端用户
        Map<String, Object> tokenMap1 = new HashMap<>();
        tokenMap1.put("MTIzZGFhY3hjZ2hqa29wa29mbXNkbTEyMzMxMg", 1L);   //舆情系统舆情推送模板id映射
        tokenMap1.put("3DSmDVMTBsuPywAzFkOBSVZdgikJneJPGVh2ApIp", 2L);   //舆情系统舆情报送通知模板id映射
        tokenMap1.put("projFlag", "博约舆情");      //项目标记
        tokenMap1.put("tenantFlag", "内部用户");    //租户标记
        tokenMap1.put("bizFlag", "普通业务");   //业务标记
        tokenMap1.put("appId", "wx09cf13a2d107672b");
        tokenMap1.put("appSecret", "ab9ee33430458cf7c9a3180ae88c032c");
        tokenMap1.put("sendUrl", "http://www.boryou.com/WechatTest/warning/info");   //最大发送次数
        map.put("dHJldGczZGFhYzMyZm1zZG0xMzEyZXd3ZnM", tokenMap1);
        return map.get(token);
    }

}
