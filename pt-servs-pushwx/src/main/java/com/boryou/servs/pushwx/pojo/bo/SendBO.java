package com.boryou.servs.pushwx.pojo.bo;

import lombok.Data;

import java.util.List;

/**
*@auther xlf
*/
@Data
public class SendBO {

    private static final long serialVersionUID = 1L;


    /**
     * 模板类型
     */
    private String type;

    /**
     * 公众号私有标识
     */
    private String appId;

    /**
     * 公众号私有密码
     */
    private String appSecret;


    /**
     * 模板消息内容  添加模板时参数以 p0 p1 p2 ...
     */
    private List<String> content;

    /**
     * 项目标记
     */
    private String projFlag;

    /**
     * 租户标记
     */
    private String tenantFlag;

    /**
     * 业务标识
     */
    private String bizFlag;

    /**
     * 接收者openid
     */
    private String openId;

    /**
     * 多个接收者openid
     */
    private List<String> openIds;


    /**
     * 模板点击链接
     */
    private String tplUrl;

    /**
     * 模板id
     */
    private Long tplId;


}
