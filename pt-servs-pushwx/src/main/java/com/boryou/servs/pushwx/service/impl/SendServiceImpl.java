package com.boryou.servs.pushwx.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.StatefulException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boryou.servs.common.util.RedisUtil;
import com.boryou.servs.pushwx.pojo.bo.SendBO;
import com.boryou.servs.pushwx.pojo.dto.SendDto;
import com.boryou.servs.pushwx.pojo.po.LogPO;
import com.boryou.servs.pushwx.pojo.po.TemplatePO;
import com.boryou.servs.pushwx.service.SendService;
import com.boryou.servs.pushwx.service.TemplateService;
import com.boryou.servs.pushwx.service.WeLogService;
import com.boryou.servs.pushwx.util.WXTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SendServiceImpl implements SendService {

    @Autowired
    private TemplateService templateService;

    @Autowired
    private WeLogService weLogService;

    //发送模板消息地址 post
    private final String SENDURL = "https://api.weixin.qq.com/cgi-bin/message/template/send";

    //验证单个openid是否已关注公众号地址 get
    private final String CHECK_ONE_URL = "https://api.weixin.qq.com/cgi-bin/user/info";

    private final String Get_USERLIST = "https://api.weixin.qq.com/cgi-bin/user/get";

    @Override
    public void send(SendBO sendBO) {
        DateTime now = DateTime.now();
        //获取微信模板标识
        String templateId = getTemplateId(sendBO.getTplId());
        //获取token
        String token = WXTokenUtil.token(sendBO.getAppId(), sendBO.getAppSecret());
        //检查用户是否已关注
        checkOpenId(sendBO.getOpenId(), token);
        //发送消息
        JSONObject tplmessage = getTplmessage(templateId, sendBO.getContent(), sendBO.getTplUrl());
        tplmessage.put("touser", sendBO.getOpenId());
        sendMessage(new SendDto(token, sendBO.getProjFlag(), sendBO.getTenantFlag(),
                sendBO.getBizFlag(), tplmessage), now);

    }


    @Override
    public void multiSend(SendBO sendBO) {
        DateTime now = DateTime.now();
        //获取微信模板标识
        String templateId = getTemplateId(sendBO.getTplId());
        // 获取token
        String token = WXTokenUtil.token(sendBO.getAppId(), sendBO.getAppSecret());
        for (String openId : sendBO.getOpenIds()) {
            sendBO.setOpenId(openId);
            //检查用户是否已关注
            checkOpenId(sendBO.getOpenId(), token);
        }
        //todo 校验失败全部发送或未失败的用户继续发送
        JSONObject tplmessage = getTplmessage(templateId, sendBO.getContent(), sendBO.getTplUrl());
        for (String openId : sendBO.getOpenIds()) {
            //发送消息
            tplmessage.put("touser", openId);
            sendMessage(new SendDto(token, sendBO.getProjFlag(), sendBO.getTenantFlag(),
                    sendBO.getBizFlag(), tplmessage), now);
        }
    }

    @Override
    public String getUserList(SendBO sendBO) {
        String url = Get_USERLIST + "?access_token=" + WXTokenUtil.token(sendBO.getAppId(), sendBO.getAppSecret()) + "&next_openid=";
        String res = HttpUtil.get(url);
        JSONObject json = JSONUtil.parseObj(res);
        if (StrUtil.isBlank(json.getStr("errcode"))) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "获取用户列表失败！");
        }
        return res;
    }

    /**
     * 获取微信模板标识
     */
    private String getTemplateId(Long tplId) {
        QueryWrapper<TemplatePO> wrapper = new QueryWrapper<>();
        wrapper.eq("id", tplId).eq("tpl_status", 1);
        TemplatePO one = templateService.getOne(wrapper);
        if (null == one) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "模板不可用或无此模板");
        }
        //todo 是否需要校验跳转链接
        return one.getTplSign();
    }


    /**
     * 发送消息
     */
    private void sendMessage(SendDto sendDto, DateTime now) {
        String postRes = HttpUtil.post(SENDURL + "?access_token=" + sendDto.getToken(), sendDto.getMessage().toString());
        JSONObject jsonObject = JSONUtil.parseObj(postRes);
        if (!StrUtil.equals("0", jsonObject.getStr("errcode"))) {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "微信推送给opeid:" + sendDto.getMessage().getStr("touser") + "失败！");
        }

        LogPO logPO = new LogPO();
        logPO.setReqTime(now);
        logPO.setRespCode(String.valueOf(jsonObject.get("errcode")));
        logPO.setRespMsg(String.valueOf(jsonObject.get("errmsg")));
        logPO.setRespTime(DateTime.now());
        logPO.setCtime(DateTime.now());
        logPO.setLogContent(sendDto.getMessage().toString());
        logPO.setProjFlag(sendDto.getProjFlag());
        logPO.setTenantFlag(sendDto.getTenantFlag());
        logPO.setBizFlag(sendDto.getBizFlag());
        weLogService.save(logPO);
    }


    /**
     * 查验关联openid用户是否关注
     */
    private void checkOpenId(String openId, String token) {
        String checkUrl = CHECK_ONE_URL + "?access_token=" + token + "&openid=" + openId + "&lang=zh_CN";
        String checkOne = HttpUtil.get(checkUrl);
        JSONObject json = JSONUtil.parseObj(checkOne);
        if (StrUtil.isBlank(json.getStr("errcode"))) {
            if (StrUtil.equals("0", json.getStr("subscribe"))) {
                throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "用户未关注公众号！");
            }
        } else {
            throw new StatefulException(HttpStatus.HTTP_INTERNAL_ERROR, "用户未关注公众号或查验用户是否关注公众号接口调用失败！");
        }
    }


    /**
     * 拼接发送模板消息体json  根据规则 模板参数全以 p 定义 p0,p1,p2...
     */
    private JSONObject getTplmessage(String sign, List<String> content, String url) {
        String[] dataKey = new String[content.size()];
        String[] dataValue = new String[content.size()];
        String[] dataColor = new String[content.size()];
        for (int i = 0; i < content.size(); i++) {
            String value = content.get(i);
            dataKey[i] = "p" + i;
            if (value.indexOf("[") > 0) {
                dataValue[i] = value.substring(0, value.indexOf("["));
                dataColor[i] = value.substring(value.indexOf("[") + 1, value.length() - 1);
            } else {
                dataValue[i] = value;
                dataColor[i] = "";
            }
        }

        //拼接请求模板参数
        JSONObject outData = new JSONObject();
        JSONObject data = new JSONObject();
        //暂时不传 遍历再加入
        //outData.put("touser", sendBO.getOpenId());

        outData.put("template_id", sign);
        outData.put("url", url);
        outData.put("data", data);
        for (int i = 0; i < dataKey.length; i++) {
            JSONObject indata = new JSONObject();
            indata.put("value", dataValue[i]);
            indata.put("color", dataColor[i]);
            data.put(dataKey[i], indata);
        }
        return outData;
    }
}
