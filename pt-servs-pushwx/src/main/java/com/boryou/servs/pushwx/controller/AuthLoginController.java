package com.boryou.servs.pushwx.controller;

import com.boryou.servs.common.bean.Return;
import com.boryou.servs.pushwx.service.AuthLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 微信扫码登录认证服务
 */

@RestController
@RequestMapping("/authLogin")
public class AuthLoginController extends AheadController {

    @Autowired
    AuthLoginService authLoginService;


    /**
     * @description 根据扫码返回的code获取登陆者信息 从新博约舆情发来的请求 code经过国密加密
     * <AUTHOR>
     */
    @RequestMapping("qrTrdirect")
    public Return qrTrdirect(@RequestParam(value = "code", required = true) String code) {
        String userInfo = authLoginService.getUserInfo( getAppId(), getAppSecret(),code);
        return Return.ok(userInfo);
    }


}
