package com.boryou.framework.util;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class AESUtils {

    private static final String KEY_AES = "AES";
    // private static final String KEY = "0102030405060708";
    // private static final String IV = "0102030405060708";
    private static final String KEY = "N5FQBYZVIHNYZ0GCTYQ47UOP9FI9Z4L0";
    private static final String IV = "WCDBP0QL54X9XRGF";


    /**
     * 加密字符串
     *
     * @param src
     * @return
     * @throws Exception
     */
    public static String encrypt(String src) throws Exception {
        byte[] raw = KEY.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, new IvParameterSpec(IV.getBytes()));
        byte[] encrypted = cipher.doFinal(src.getBytes());
        return byte2hex(encrypted);
    }

    /**
     * 加密文件
     *
     * @param src    文件路径
     * @param target 输出路径
     * @throws Exception
     */
    public static void encryptFile(String src, String target) throws Exception {
        // 初始化 key，iv，加密算法
        byte[] raw = KEY.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, new IvParameterSpec(IV.getBytes()));

        // 将输入文件读入一个字节数组中
        fileDoFinal(src, target, cipher);
    }

    /**
     * 解密文件
     *
     * @param src    文件路径
     * @param target 输出路径
     * @throws Exception
     */
    public static void decryptFile(String src, String target) throws Exception {
        // 初始化 key，iv，解密算法
        byte[] raw = KEY.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(IV.getBytes()));

        // 将输入文件读入一个字节数组中
        fileDoFinal(src, target, cipher);
    }

    private static void fileDoFinal(String src, String target, Cipher cipher) throws IOException, IllegalBlockSizeException, BadPaddingException {
        // 将输入文件读入一个字节数组中
        FileInputStream inputStream = new FileInputStream(src);
        java.io.File inputFile = new java.io.File(src);
        byte[] inputBytes = new byte[(int) inputFile.length()];
        inputStream.read(inputBytes);

        // 对输入字节进行解密
        byte[] outputBytes = cipher.doFinal(inputBytes);
        // 将加密后的字节写入目标文件
        FileOutputStream outputStream = new FileOutputStream(target);
        outputStream.write(outputBytes);

        inputStream.close();
        outputStream.close();
    }

    /**
     * 解密文件，得到新的 MultipartFile
     *
     * @param file
     * @return
     * @throws Exception
     */
    public static MultipartFile decryptFile(MultipartFile file) throws Exception {
        // 初始化 key，iv，解密算法
        byte[] raw = KEY.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(IV.getBytes()));

        // 将输入文件读入一个字节数组中
        byte[] inputBytes = file.getBytes();

        // 对输入字节进行解密
        byte[] outputBytes = cipher.doFinal(inputBytes);

        // 将解密后的文件重新转成 MultipartFile
        return new MockMultipartFile(file.getName(), file.getOriginalFilename(), file.getContentType(), outputBytes);
    }

    /**
     * 解密字符串，如果解密失败，返回原字符串
     *
     * @param src
     * @return
     * @throws Exception
     */

    public static String decrypt(String src) throws Exception {
        byte[] raw = KEY.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(IV.getBytes()));
        byte[] encrypted1 = hex2byte(src);
        try {
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original);
        } catch (Exception e) {
            return src;
        }
    }


    private static byte[] hex2byte(String strhex) {
        if (strhex == null) {
            return null;
        }
        int l = strhex.length();
        if (l % 2 == 1) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i < l / 2; i++) {
            b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2), 16);
        }
        return b;
    }

    private static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (byte value : b) {
            stmp = Integer.toHexString(value & 0xFF);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    public static void main(String[] args) throws Exception {
        String encrypt = "你好";
        String decrype = "A9B659BEEA5B80322D1B7EE786B9C011";
        String encrypt1 = encrypt(encrypt);
        System.out.println("加密后：" + encrypt1);

        String decrypt = decrypt(decrype);
        System.out.println("解密后：" + decrypt);
        System.out.println(KEY.toUpperCase());
        System.out.println(IV.toUpperCase());
        // JSONObject jsonObject = JSON.parseObject(decrypt);
        // System.out.println(jsonObject);

        // 加密文件
        // encryptFile("D:\\同步盘\\书\\Redis 深度历险：核心原理与应用实践 (钱文品) (Z-Library).pdf", "D:\\test.pdf");
        // encryptFile("D:\\同步盘\\图片和证书\\头像.png", "C:\\Users\\<USER>\\Desktop\\头像.png");
        // //解密文件
        // decryptFile("C:\\Users\\<USER>\\Desktop\\2.jpg", "C:\\Users\\<USER>\\Desktop\\3.jpg");
    }
}
